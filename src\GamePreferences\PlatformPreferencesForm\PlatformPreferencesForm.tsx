import { Controller, useFormContext } from "react-hook-form";
import React, { useCallback, useEffect, useState } from "react";
import ProfileFormAction from "../../Information/ProfileFormAction/ProfileFormAction";
import Card from "../../cards/Card";
import { Icon } from "@eait-playerexp-cn/core-ui-kit";
import classNames from "classnames/bind";
import { Select } from "@eait-playerexp-cn/core-ui-kit";
import CheckboxCards from "../../cards/CheckboxCards";
import FormTitle from "../../formTitle/FormTitle";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import Nintendo from "../../icons/Platforms/Nintendo";
import { PlatformPreferencesFormProps, SecPlatformCardProps, Platform } from "../types";

const PlatformPreferencesForm: React.FC<PlatformPreferencesFormProps> = ({
  infoLabels,
  buttons,
  onChange,
  isSaved = false,
  platforms,
  preferredPrimaryPlatforms,
  preferredSecondaryPlatforms,
  isLoader
}) => {
  const { control, setValue } = useFormContext();

  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [primaryPlatformImage, setPrimaryPlatformImage] = useState<string>(
    (preferredPrimaryPlatforms && preferredPrimaryPlatforms.imageAsIcon) || "/img/default-platform.png"
  );
  const [primaryPlatforms, setPrimaryPlatforms] = useState<Platform[]>([]);
  const { success: successToast } = useToast();
  let modalSuccessText = infoLabels.success.platformPreferences;
  const timetoDisplay = Math.min(Math.max(modalSuccessText.length * 50, 2000), 7000);

  const onEditChange = useCallback(
    (isChecked) => {
      setIsEdit(isChecked);
      onChange && onChange();
    },
    [onChange]
  );

  useEffect(() => {
    isEdit && isSaved && setIsEdit(false);
    isEdit && !preferredPrimaryPlatforms && primaryPlatforms && setPrimaryPlatformImage(primaryPlatforms[0].imageAsIcon || "/img/default-platform.png");
  }, [isSaved, isEdit]);

  useEffect(() => {
    const primaryPlatformOptions: Platform[] = platforms.map((_iter) => {
      const platform: Platform = {
        imageAsIcon: _iter.imageAsIcon,
        label: _iter.label,
        value: _iter.value
      };
      return platform;
    });
    setPrimaryPlatforms(primaryPlatformOptions);
    setValue("primaryPlatform", preferredPrimaryPlatforms || primaryPlatformOptions[0]);
  }, [platforms]);

  return (
    <>
      {isEdit &&
        isSaved &&
        successToast(
          <Toast
            header={infoLabels.success.updatedInformationHeader}
            content={modalSuccessText}
          />,
          {
            autoClose: { timetoDisplay }
          }
        )}
      <div className="profile-game-preferences-title-actions profile-platform-preferences">
        <FormTitle title={infoLabels.platformPreferences} />
        <ProfileFormAction {...{ buttons, action: onEditChange, isSaved, isLoader }} />
      </div>
      <div className="profile-primary-platform-container">
        <h4 className="profile-game-preferences-primary-title">{infoLabels.primaryPlatform}</h4>
        <div className="profile-game-preferences-primary-subtitle">{infoLabels.platformPreferencesTitle}</div>
        <div className="profile-game-preferences-primary-show">
          {!isEdit && preferredPrimaryPlatforms && (
            <>
              <div className="profile-game-preferences-tag">{preferredPrimaryPlatforms.label}</div>
              <div className="profile-platform-preferences-option">
                <Card>
                  {preferredPrimaryPlatforms.imageAsIcon && (
                    <img
                      alt="Platform image"
                      className="profile-platform-icon"
                      src={`${preferredPrimaryPlatforms.imageAsIcon}`}
                    />
                  )}
                </Card>
              </div>
            </>
          )}
          {isEdit && (
            <Controller
              control={control}
              name="primaryPlatform"
              render={({ field, fieldState: { error } }) => (
                <>
                  <Select
                    id="primary-platform"
                    selectedOption={preferredPrimaryPlatforms}
                    errorMessage={error && error.message}
                    options={primaryPlatforms}
                    onChange={(item: Platform) => {
                      setPrimaryPlatformImage(item.imageAsIcon || "/img/default-platform.png");
                      field.onChange(item);
                    }}
                  />
                  <div className="profile-platform-preferences-option">
                    <Card>
                      {primaryPlatformImage && (
                        <img alt="Platform image" className="profile-platform-icon" src={`${primaryPlatformImage}`} />
                      )}
                    </Card>
                  </div>
                </>
              )}
            />
          )}
        </div>
      </div>
      <div className="profile-platform-secondary-container">
        <h4 className="profile-game-preferences-secondary-title">{infoLabels.secondaryPlatforms}</h4>
        <div className="profile-game-preferences-secondary-subtitle">{infoLabels.secondaryPlatformsTitle}</div>
        {!isEdit && (
          <div className="profile-secondary-platform-show">
            <div className="profile-secondary-game-preferences-options">
              {platforms.map((item, index) => {
                item.selected = preferredSecondaryPlatforms.filter((e) => e.value === item.value).length > 0;
                return <SecPlatformCard key={`secondary-platform-card-${index}`} platform={item} />;
              })}
            </div>
          </div>
        )}
        {isEdit && (
          <Controller
            control={control}
            name="secondaryPlatforms"
            defaultValue={preferredSecondaryPlatforms}
            render={({ field }) => (
              <CheckboxCards
                {...field}
                items={platforms.map((item) => {
                  return {
                    ...item,
                    checked:
                      preferredSecondaryPlatforms &&
                      preferredSecondaryPlatforms.filter((values) => values.value === item.value).length > 0
                  };
                })}
              />
            )}
          />
        )}
      </div>
    </>
  );
};
export default PlatformPreferencesForm;

const SecPlatformCard: React.FC<SecPlatformCardProps> = ({ platform }) => {
  const cx = classNames.bind({});

  return (
    <div
      className={cx(
        "profile-secondary-platform-option-container",
        {
          "profile-secondary-platform-option-container-selected": platform.selected
        }
      )}
    >
      <div
        className={cx(
          "profile-secondary-platform-option",
          {
            "profile-secondary-platform-option-selected": platform.selected
          }
        )}
      >
        {platform.imageAsIcon ? (
          <img alt="Platform image" className="profile-secondary-platform-icon" src={`${platform.imageAsIcon}`} />
        ) : (
          <Icon icon={Nintendo} className="profile-secondary-platform-icon-default" />
        )}
      </div>
      <div className="profile-secondary-platform-option-text">{platform.label}</div>
    </div>
  );
};
