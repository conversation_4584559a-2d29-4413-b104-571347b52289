import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import Information from "./InformationPage";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { NextRouter } from "next/router";
import { CreatorProfile } from "@eait-playerexp-cn/creators-http-client";
import {
  aCommunicationPreferencesPayload,
  aConnectedAccountResponse,
  aCreatorCodeResponse,
  aLegalEntityInformationPayload,
  aMailingAddressPayload,
  anAdditionalInformationPayload,
  aProgramRequest
} from "@eait-playerexp-cn/creator-test-fixtures";

const meta: Meta<typeof Information> = {
  title: "Information Page",
  component: Information,
  parameters: {
    layout: "fullscreen"
  },
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof Information>;

export const InformationPage: Story = {
  args: {
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close",
      browse: "Browse"
    },
    errorHandler: () => {},
    infoLabels: {
      personalInformation: "Personal Information",
      mailingAddress: "Mailing Address",
      miscellaneous: "Miscellaneous",
      creatorSince: "Creator Since",
      legalEntityType: "Legal Entity & Address",
      legalEntityDescription:
        "This info will also be used for payment purposes. Changing your entity type or address may require you to update your payment/tax info in the Payment Information page.",
      header: {
        calendar: "Calender"
      },
      success: {
        updatedInformationHeader: "Information update successful",
        personalInformation: "You have successfully updated your Personal Information.",
        mailingAddress: "You have successfully updated your Mailing Address.",
        miscellaneous: "You have successfully updated your Miscellaneous Information.",
        legalEntityType: "You have successfully updated your Legal Entity Information."
      },
      labels: {
        none: "None",
        firstName: "First Name",
        lastName: "Last Name",
        EAID: "Electronic Arts ID",
        EAEmail: "Electronic Arts Account Email",
        dateOfBirth: "Date of Birth",
        country: "Country/Region",
        street: "Street",
        city: "City",
        state: "State or Province",
        zipCode: "Zip Code or Postal Code",
        tShirtSize: "T-Shirt Size",
        hardwarePartners: "Hardware Partners",
        entityType: "Entity Type",
        individual: "Individual",
        business: "Business",
        businessName: "Name of Business",
        legalAddressAsMailingAddress: "Address is the same as my mailing address."
      },
      messages: {
        firstName: "First Name is required",
        firstNameTooLong: "First Name is too long",
        lastName: "Last Name is required",
        lastNameTooLong: "Last Name is too long",
        dateOfBirth: "Date of Birth is required",
        dateOfBirthInvalid: "Date of Birth is invalid",
        ageMustBe18OrOlder: "Must be 18 or older",
        country: "Country/Region is required",
        street: "Street is required",
        streetTooLong: "Street is too long",
        city: "City is required",
        cityTooLong: "City is too long",
        state: "State or Province is required",
        stateTooLong: "State is too long",
        zipCode: "Zip Code or Postal Code is required",
        zipCodeTooLong: "Zip Code or Postal Code is too long",
        primaryPlatform: "Primary platform is required",
        tShirtSize: "T-Shirt Size is required",
        hardwarePartners: "Hardware Partners is required",
        entityType: "Entity Type is required",
        businessName: "Business Name is required",
        businessNameTooLong: "Business Name is too long",
        email: "Email Address is required",
        emailTooLong: "Email Address is too long",
        emailInvalid: "Email Address is invalid",
        url: "URL is required",
        invalidUrl: "URL provided is invalid",
        duplicateUrl: "Duplicate URLs not allowed",
        urlScanFailed: "You cannot submit content from this website or domain",
        followersMaxLength: "Maximum 18 digits allowed"
      },
      profilePictureLabels: {
        title: "Welcome back,",
        message: "Select an image from your computer (JPEG, PNG or GIF). Image should be square and less than 1MB.",
        termsAndConditionsFirst:
          'Respect the rights of others. Only upload images that you own or that you have written permission to freely distribute. Like most other online services, your avatar will be visible to other users of the service and associated with your Electronic Arts ID, even if your profile is set to "no one can see." Please read our ',
        termsAndConditionsMiddle: "User Agreement",
        termsAndConditionsLast: "for more information.",
        avatarRequired: "Please select an image",
        avatarInvalid: "Please select valid image",
        avatarMoreThanLimit: "Image size should be less than 1MB"
      },
      info: {
        businessName: "Only if you are contracting under a business entity; otherwise, leave blank."
      },
      profileLabels: {
        updateAvatar: "Update Avatar"
      }
    },
    user: {
      analyticsId: "xNIEuLMZCvlw+7q2oZaBuG0cTdqHTa2JP3l2eCZ2Sjg=",
      avatar: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png",
      creatorCode: "TestAFFILIATE20250",
      isFlagged: false,
      isPayable: true,
      needsMigration: false,
      programs: ["creator_network", "affiliate", "sims_creator_program"],
      status: "ACTIVE",
      tier: "CREATORNETWORK",
      type: "CREATOR",
      username: "245902"
    },
    updateCreator: () => {},
    hardwarePartners: [
      {
        value: "a0K4x000004kw1EEAQ",
        label: "PlayStation",
        isCustom: false
      },
      {
        value: "a0K4x000004kw1JEAQ",
        label: "Xbox",
        isCustom: false
      },
      {
        value: "a0K4x000004kw1OEAQ",
        label: "Corsair",
        isCustom: false
      }
    ],
    countries: [
      {
        value: "HU",
        label: "Hungary",
        name: "Hungary"
      },
      {
        value: "IS",
        label: "Iceland",
        name: "Iceland"
      },
      {
        value: "IN",
        label: "India",
        name: "India"
      }
    ],
    layout: {
      main: {
        unhandledError: "Oops! Something has gone wrong."
      },
      toolTip: {
        badge: "As a former Game Changer you’ve been awarded the Legacy Creator badge!"
      }
    },
    analytics: { updatedBasicInformation: () => {} },
    creator: {
      preferredSecondaryPlatforms: [],
      preferredSecondaryFranchises: [],
      id: "001VB00000DUe5PYAT",
      creatorTypes: ["YOUTUBER"],
      accountInformation: {
        defaultGamerTag: "245902",
        nucleusId: *************,
        firstName: "Mouli",
        lastName: "Nrusimhadri",
        originEmail: "<EMAIL>",
        dateOfBirth: {
          millisecondsEpoch: ************,
          format: () => "July 30, 1995",
          toDate: () => new Date(************)
        } as unknown as LocalizedDate,
        needsMigration: false,
        payable: true,
        flagged: false,
        disabled: false,
        preferredName: "Mouli",
        preferredPronouns: null
      },
      preferredPrimaryPlatform: null,
      preferredPrimaryFranchise: {
        id: "a0G7c000006GAZpEAO",
        name: "The Sims",
        type: "PRIMARY"
      },
      communicationPreferences: aCommunicationPreferencesPayload(),
      legalInformation: aLegalEntityInformationPayload(),
      mailingAddress: aMailingAddressPayload(),
      connectedAccounts: aConnectedAccountResponse(),
      additionalInformation: anAdditionalInformationPayload(),
      socialLinks: [],
      creatorCode: aCreatorCodeResponse(),
      joinedPrograms: ["creator_network", "affiliate", "sims_creator_program"],
      program: aProgramRequest(),
      formattedRegistrationDate: () => ""
    } as unknown as CreatorProfile,
    allCountries: [
      {
        value: "HU",
        label: "Hungary",
        name: "Hungary"
      },
      {
        value: "IS",
        label: "Iceland",
        name: "Iceland"
      },
      {
        value: "IN",
        label: "India",
        name: "India"
      }
    ],
    router: {
      query: {},
      locale: "en-us",
      pathname: "",
      push: () => Promise.resolve(true)
    } as unknown as NextRouter,
    configuration: {
      DEFAULT_AVATAR_IMAGE: "",
      PROGRAM_CODE: "",
      creatorsClient: {} as unknown as TraceableHttpClient
    },
    stableDispatch: () => {},
    state: {
      isValidationError: false,
      validationErrors: [],
      isError: false
    }
  }
};
