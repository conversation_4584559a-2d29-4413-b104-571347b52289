import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import Information from "./InformationPage";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { CreatorProfile } from "@eait-playerexp-cn/creators-http-client";
import {
  aCommunicationPreferencesPayload,
  aConnectedAccountResponse,
  aCreatorCodeResponse,
  aLegalEntityInformationPayload,
  aMailingAddressPayload,
  anAdditionalInformationPayload,
  aProgramRequest
} from "@eait-playerexp-cn/creator-test-fixtures";
import { InformationPageLabels } from "../Translations/InformationPageLabels";
import { aCountry } from "@eait-playerexp-cn/metadata-test-fixtures";

const meta: Meta<typeof Information> = {
  title: "Information Page",
  component: Information,
  parameters: {
    layout: "fullscreen"
  },
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof Information>;

export const InformationPage: Story = {
  args: {
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close",
      browse: "Browse"
    },
    errorHandler: () => {},
    infoLabels: InformationPageLabels.infoLabels,
    user: {
      analyticsId: "xNIEuLMZCvlw+7q2oZaBuG0cTdqHTa2JP3l2eCZ2Sjg=",
      avatar: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png",
      creatorCode: "TestAFFILIATE20250",
      isFlagged: false,
      isPayable: true,
      needsMigration: false,
      programs: ["creator_network", "affiliate", "sims_creator_program"],
      status: "ACTIVE",
      tier: "CREATORNETWORK",
      type: "CREATOR",
      username: "245902"
    },
    updateCreator: () => {},
    hardwarePartners: [
      {
        value: "a0K4x000004kw1EEAQ",
        label: "PlayStation",
        isCustom: false
      },
      {
        value: "a0K4x000004kw1JEAQ",
        label: "Xbox",
        isCustom: false
      },
      {
        value: "a0K4x000004kw1OEAQ",
        label: "Corsair",
        isCustom: false
      }
    ],
    countries: [
      aCountry({ value: "United States", label: "United States", name: "United States" }),
      aCountry({ value: "India", label: "India", name: "India" }),
      aCountry({ value: "Canada", label: "Canada", name: "Canada" })
    ],
    layout: {
      main: {
        unhandledError: "Oops! Something has gone wrong."
      },
      toolTip: {
        badge: "As a former Game Changer you’ve been awarded the Legacy Creator badge!"
      }
    },
    onSaveAccountInformation: () => {},
    creator: {
      preferredSecondaryPlatforms: [],
      preferredSecondaryFranchises: [],
      id: "001VB00000DUe5PYAT",
      creatorTypes: ["YOUTUBER"],
      accountInformation: {
        defaultGamerTag: "245902",
        nucleusId: *************,
        firstName: "Mouli",
        lastName: "Nrusimhadri",
        originEmail: "<EMAIL>",
        dateOfBirth: {
          millisecondsEpoch: ************,
          format: () => "July 30, 1995",
          toDate: () => new Date(************)
        } as unknown as LocalizedDate,
        needsMigration: false,
        payable: true,
        flagged: false,
        disabled: false,
        preferredName: "Mouli",
        preferredPronouns: null
      },
      preferredPrimaryPlatform: null,
      preferredPrimaryFranchise: {
        id: "a0G7c000006GAZpEAO",
        name: "The Sims",
        type: "PRIMARY"
      },
      communicationPreferences: aCommunicationPreferencesPayload(),
      legalInformation: aLegalEntityInformationPayload(),
      mailingAddress: aMailingAddressPayload({
        country: {
          code: "United States",
          name: "United States"
        }
      }),
      connectedAccounts: aConnectedAccountResponse(),
      additionalInformation: anAdditionalInformationPayload(),
      socialLinks: [],
      creatorCode: aCreatorCodeResponse(),
      joinedPrograms: ["creator_network", "affiliate", "sims_creator_program"],
      program: aProgramRequest(),
      formattedRegistrationDate: () => ""
    } as unknown as CreatorProfile,
    allCountries: [
      aCountry({ value: "United States", label: "United States", name: "United States" }),
      aCountry({ value: "India", label: "India", name: "India" }),
      aCountry({ value: "Canada", label: "Canada", name: "Canada" })
    ],
    locale: "en-us",
    configuration: {
      defaultAvatarImage: "",
      program: "",
      creatorsClient: {} as unknown as TraceableHttpClient
    },
    stableDispatch: () => {},
    state: {
      isValidationError: false,
      validationErrors: [],
      isError: false
    }
  }
};
