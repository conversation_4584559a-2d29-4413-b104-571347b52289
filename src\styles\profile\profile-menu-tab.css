.profile-menu-tab-container {
  @apply pb-meas20 text-black;
}
.profile-menu-tab-title {
  @apply mb-meas4 font-display-bold xs:text-mobile-h4 md:mb-meas12 md:text-tablet-h4 lg:text-desktop-h4;
}
.profile-menu-tab-rows {
  @apply grid grid-cols-1 gap-meas8 md:grid-cols-2 md:gap-meas16;
}
.profile-menu-tab-point-of-contact-card {
  @apply grid grid-cols-2 items-center rounded border border-gray-20 bg-white p-meas2 text-center font-text-regular;
}
.profile-menu-tab-point-of-contact-card-title {
  @apply ml-meas4 pb-meas4 text-left font-text-regular xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.profile-menu-tab-point-of-contact-card-tag {
  @apply w-[138px] rounded-[100px] bg-secondary pl-meas4 pr-meas2 text-white xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1;
}
.profile-menu-tab-point-of-contact-card-comm {
  @apply mb-meas4 ml-meas4 flex;
}
.profile-menu-tab-point-of-contact-card-comm:last-child {
  @apply mb-meas0;
}
.profile-menu-tab-point-of-contact-card-comm-text {
  @apply pl-meas4 text-black underline xs:text-mobile-caption2 md:text-tablet-caption1 lg:text-tablet-caption1;
}

.profile-menu-tab-dropdown-container .select-header {
  @apply h-meas30 rounded-[3px] border-white bg-page focus:border-2 focus:border-black;
}
.profile-menu-tab-dropdown-container .focused.select-header {
  @apply border-2 border-white;
}

/* Dark mode */

.select-box-dark.select-box button {
  @apply w-full;
}
.select-box-dark .select-header-title {
  @apply w-full;
}
.select-box-dark .select-header-title .select-option-icon {
  @apply text-black;
}
.select-box-dark .select-header-title .icon {
  @apply h-[12px] w-[20px] text-black;
}
.select-box-dark .select-option-icon {
  @apply ml-meas4 w-auto min-w-[16px] text-navy-80;
}
.select-box-dark .select-list-item .select-option-icon {
  @apply w-auto;
}
.select-box-dark .select-option-label {
  @apply ml-meas2 text-navy-80;
}
.select-box-dark .select-header {
  @apply h-meas30 rounded-[3px] border-black bg-page active:border-black;
  @apply rounded-[63px];
}
.select-box-dark .focused.select-header,
.select-box-dark .select-header:active {
  @apply border-2 border-info-50 outline-none;
}
.select-box-dark .select-header-label {
  @apply text-black;
}
.select-box-dark .selected.select-header-title {
  @apply text-black;
}
.select-box-dark .select-selected {
  @apply text-gray-90;
}
.select-box-dark .select-option-label-icon {
  @apply w-[92%];
}
.select-box-dark .select-list-item {
  @apply h-[50px] hover:bg-custom-2;
}
.select-box-dark .selected.select-list-item {
  @apply bg-white;
}
.select-box-dark .selected.list-item {
  @apply hover:bg-navy-60;
}
.select-box-dark .select-scroll-list,
.select-box-dark .select-list {
  @apply max-h-[308px];
}
