// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import { Controller, useFormContext } from "react-hook-form";
import React, { memo, useCallback, useEffect, useState } from "react";
import { Select } from "@eait-playerexp-cn/core-ui-kit";
import ProfileFormAction from "../ProfileFormAction/ProfileFormAction";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import FormTitle from "../FormTitle/FormTitle";
import { Input } from "@eait-playerexp-cn/core-ui-kit";
import { ButtonLabels, InfoLabels } from "../types";
import { Country, CountryResponse } from "@eait-playerexp-cn/metadata-types";
import { CreatorFormRules } from "@src/utils/FormRules/CreatorForm";
type MailingAddress = {
  state: string;
  street: string;
  city: string;
  zipCode: string;
  country?: CountryResponse;
};
interface MailingAddressFormProps {
  infoLabels: InfoLabels;
  rules: CreatorFormRules;
  mailingAddress: MailingAddress;
  allCountries: Country[];
  buttons: ButtonLabels;
  onChange: () => void;
  isSaved: boolean;
  isLoader: boolean;
}
const MailingAddressForm = ({
  infoLabels,
  rules,
  mailingAddress,
  allCountries,
  buttons,
  onChange,
  isSaved = stryMutAct_9fa48("644") ? true : (stryCov_9fa48("644"), false),
  isLoader
}: MailingAddressFormProps) => {
  if (stryMutAct_9fa48("645")) {
    {}
  } else {
    stryCov_9fa48("645");
    const {
      control,
      setValue
    } = useFormContext();
    const [isEdit, setIsEdit] = useState(stryMutAct_9fa48("646") ? true : (stryCov_9fa48("646"), false));
    const {
      success: successToast
    } = useToast();
    const timeToDisplay = stryMutAct_9fa48("647") ? Math.max(Math.max(infoLabels.success.mailingAddress.length * 50, 2000), 7000) : (stryCov_9fa48("647"), Math.min(stryMutAct_9fa48("648") ? Math.min(infoLabels.success.mailingAddress.length * 50, 2000) : (stryCov_9fa48("648"), Math.max(stryMutAct_9fa48("649") ? infoLabels.success.mailingAddress.length / 50 : (stryCov_9fa48("649"), infoLabels.success.mailingAddress.length * 50), 2000)), 7000));
    const onEditChange = useCallback(isChecked => {
      if (stryMutAct_9fa48("650")) {
        {}
      } else {
        stryCov_9fa48("650");
        setIsEdit(isChecked);
        if (stryMutAct_9fa48("652") ? false : stryMutAct_9fa48("651") ? true : (stryCov_9fa48("651", "652"), onChange)) onChange();
      }
    }, stryMutAct_9fa48("653") ? [] : (stryCov_9fa48("653"), [isEdit]));
    const SELECTED_COUNTRY = (mailingAddress.country as unknown as CountryResponse).code ? {
      value: (mailingAddress.country as unknown as CountryResponse).code,
      label: mailingAddress.country.name
    } as Country : mailingAddress.country as unknown as Country;
    useEffect(() => {
      if (stryMutAct_9fa48("654")) {
        {}
      } else {
        stryCov_9fa48("654");
        if (stryMutAct_9fa48("657") ? isEdit || isSaved : stryMutAct_9fa48("656") ? false : stryMutAct_9fa48("655") ? true : (stryCov_9fa48("655", "656", "657"), isEdit && isSaved)) setIsEdit(stryMutAct_9fa48("658") ? true : (stryCov_9fa48("658"), false));
      }
    }, stryMutAct_9fa48("659") ? [] : (stryCov_9fa48("659"), [isSaved]));
    useEffect(() => {
      if (stryMutAct_9fa48("660")) {
        {}
      } else {
        stryCov_9fa48("660");
        setValue(stryMutAct_9fa48("661") ? "" : (stryCov_9fa48("661"), "country"), stryMutAct_9fa48("664") ? SELECTED_COUNTRY && allCountries[0] : stryMutAct_9fa48("663") ? false : stryMutAct_9fa48("662") ? true : (stryCov_9fa48("662", "663", "664"), SELECTED_COUNTRY || allCountries[0]));
      }
    }, stryMutAct_9fa48("665") ? [] : (stryCov_9fa48("665"), [allCountries]));
    return <>
      {stryMutAct_9fa48("668") ? isSaved && isEdit || successToast(<Toast header={infoLabels.success.updatedInformationHeader} content={infoLabels.success.mailingAddress} />, {
        autoClose: {
          timetoDisplay: timeToDisplay
        }
      }) : stryMutAct_9fa48("667") ? false : stryMutAct_9fa48("666") ? true : (stryCov_9fa48("666", "667", "668"), (stryMutAct_9fa48("670") ? isSaved || isEdit : stryMutAct_9fa48("669") ? true : (stryCov_9fa48("669", "670"), isSaved && isEdit)) && successToast(<Toast header={infoLabels.success.updatedInformationHeader} content={infoLabels.success.mailingAddress} />, stryMutAct_9fa48("671") ? {} : (stryCov_9fa48("671"), {
        autoClose: stryMutAct_9fa48("672") ? {} : (stryCov_9fa48("672"), {
          timetoDisplay: timeToDisplay
        })
      })))}
      <div className="mailing-information">
        <div className="form-sub-title-and-action">
          <FormTitle subTitle={infoLabels.mailingAddress} />
          <ProfileFormAction {...stryMutAct_9fa48("673") ? {} : (stryCov_9fa48("673"), {
            buttons,
            action: onEditChange,
            isSaved,
            isLoader
          })} />
        </div>

        <div className="mailing-field-title">{infoLabels.labels.street}</div>
        <div className="mailing-field">
          {stryMutAct_9fa48("676") ? !isEdit || mailingAddress.street : stryMutAct_9fa48("675") ? false : stryMutAct_9fa48("674") ? true : (stryCov_9fa48("674", "675", "676"), (stryMutAct_9fa48("677") ? isEdit : (stryCov_9fa48("677"), !isEdit)) && mailingAddress.street)}
          {stryMutAct_9fa48("680") ? isEdit || <Controller control={control} name="street" rules={rules.street} defaultValue={mailingAddress.street} render={({
            field,
            fieldState: {
              error
            }
          }) => <Input id="mailing-address-street" errorMessage={error && error.message || ""} {...field} placeholder={infoLabels.labels.street} ariaLabel={infoLabels.labels.street} />} /> : stryMutAct_9fa48("679") ? false : stryMutAct_9fa48("678") ? true : (stryCov_9fa48("678", "679", "680"), isEdit && <Controller control={control} name="street" rules={rules.street} defaultValue={mailingAddress.street} render={stryMutAct_9fa48("681") ? () => undefined : (stryCov_9fa48("681"), ({
            field,
            fieldState: {
              error
            }
          }) => <Input id="mailing-address-street" errorMessage={stryMutAct_9fa48("684") ? error && error.message && "" : stryMutAct_9fa48("683") ? false : stryMutAct_9fa48("682") ? true : (stryCov_9fa48("682", "683", "684"), (stryMutAct_9fa48("686") ? error || error.message : stryMutAct_9fa48("685") ? false : (stryCov_9fa48("685", "686"), error && error.message)) || (stryMutAct_9fa48("687") ? "Stryker was here!" : (stryCov_9fa48("687"), "")))} {...field} placeholder={infoLabels.labels.street} ariaLabel={infoLabels.labels.street} />)} />)}
        </div>

        <div className="mailing-field-title">{infoLabels.labels.city}</div>
        <div className="mailing-field">
          {stryMutAct_9fa48("690") ? !isEdit || mailingAddress.city : stryMutAct_9fa48("689") ? false : stryMutAct_9fa48("688") ? true : (stryCov_9fa48("688", "689", "690"), (stryMutAct_9fa48("691") ? isEdit : (stryCov_9fa48("691"), !isEdit)) && mailingAddress.city)}
          {stryMutAct_9fa48("694") ? isEdit || <Controller control={control} name="city" rules={rules.city} defaultValue={mailingAddress.city} render={({
            field,
            fieldState: {
              error
            }
          }) => <Input id="mailing-address-city" errorMessage={error && error.message || ""} {...field} placeholder={infoLabels.labels.city} />} /> : stryMutAct_9fa48("693") ? false : stryMutAct_9fa48("692") ? true : (stryCov_9fa48("692", "693", "694"), isEdit && <Controller control={control} name="city" rules={rules.city} defaultValue={mailingAddress.city} render={stryMutAct_9fa48("695") ? () => undefined : (stryCov_9fa48("695"), ({
            field,
            fieldState: {
              error
            }
          }) => <Input id="mailing-address-city" errorMessage={stryMutAct_9fa48("698") ? error && error.message && "" : stryMutAct_9fa48("697") ? false : stryMutAct_9fa48("696") ? true : (stryCov_9fa48("696", "697", "698"), (stryMutAct_9fa48("700") ? error || error.message : stryMutAct_9fa48("699") ? false : (stryCov_9fa48("699", "700"), error && error.message)) || (stryMutAct_9fa48("701") ? "Stryker was here!" : (stryCov_9fa48("701"), "")))} {...field} placeholder={infoLabels.labels.city} />)} />)}
        </div>

        <div className="mailing-field-title">{infoLabels.labels.country}</div>
        <div className="mailing-field">
          {stryMutAct_9fa48("704") ? !isEdit || mailingAddress.country.name : stryMutAct_9fa48("703") ? false : stryMutAct_9fa48("702") ? true : (stryCov_9fa48("702", "703", "704"), (stryMutAct_9fa48("705") ? isEdit : (stryCov_9fa48("705"), !isEdit)) && mailingAddress.country.name)}
          {stryMutAct_9fa48("708") ? isEdit || <Controller control={control} name="country" rules={rules.country} render={({
            field,
            fieldState: {
              error
            }
          }) => <Select id="mailing-country" selectedOption={SELECTED_COUNTRY} errorMessage={error && error.message} options={allCountries} onChange={item => {
            field.onChange(item);
          }} />} /> : stryMutAct_9fa48("707") ? false : stryMutAct_9fa48("706") ? true : (stryCov_9fa48("706", "707", "708"), isEdit && <Controller control={control} name="country" rules={rules.country} render={stryMutAct_9fa48("709") ? () => undefined : (stryCov_9fa48("709"), ({
            field,
            fieldState: {
              error
            }
          }) => <Select id="mailing-country" selectedOption={SELECTED_COUNTRY} errorMessage={stryMutAct_9fa48("712") ? error || error.message : stryMutAct_9fa48("711") ? false : stryMutAct_9fa48("710") ? true : (stryCov_9fa48("710", "711", "712"), error && error.message)} options={allCountries} onChange={item => {
            if (stryMutAct_9fa48("713")) {
              {}
            } else {
              stryCov_9fa48("713");
              field.onChange(item);
            }
          }} />)} />)}
        </div>

        <div className="mailing-field-title">{infoLabels.labels.state}</div>
        <div className="mailing-field">
          {stryMutAct_9fa48("716") ? !isEdit || mailingAddress.state : stryMutAct_9fa48("715") ? false : stryMutAct_9fa48("714") ? true : (stryCov_9fa48("714", "715", "716"), (stryMutAct_9fa48("717") ? isEdit : (stryCov_9fa48("717"), !isEdit)) && mailingAddress.state)}
          {stryMutAct_9fa48("720") ? isEdit || <Controller control={control} name="state" rules={rules.state} defaultValue={mailingAddress.state} render={({
            field,
            fieldState: {
              error
            }
          }) => <Input id="mailing-address-state" errorMessage={error && error.message || ""} {...field} placeholder={infoLabels.labels.state} />} /> : stryMutAct_9fa48("719") ? false : stryMutAct_9fa48("718") ? true : (stryCov_9fa48("718", "719", "720"), isEdit && <Controller control={control} name="state" rules={rules.state} defaultValue={mailingAddress.state} render={stryMutAct_9fa48("721") ? () => undefined : (stryCov_9fa48("721"), ({
            field,
            fieldState: {
              error
            }
          }) => <Input id="mailing-address-state" errorMessage={stryMutAct_9fa48("724") ? error && error.message && "" : stryMutAct_9fa48("723") ? false : stryMutAct_9fa48("722") ? true : (stryCov_9fa48("722", "723", "724"), (stryMutAct_9fa48("726") ? error || error.message : stryMutAct_9fa48("725") ? false : (stryCov_9fa48("725", "726"), error && error.message)) || (stryMutAct_9fa48("727") ? "Stryker was here!" : (stryCov_9fa48("727"), "")))} {...field} placeholder={infoLabels.labels.state} />)} />)}
        </div>

        <div className="mailing-field-title">{infoLabels.labels.zipCode}</div>
        <div className="mailing-field">
          {stryMutAct_9fa48("730") ? !isEdit || mailingAddress.zipCode : stryMutAct_9fa48("729") ? false : stryMutAct_9fa48("728") ? true : (stryCov_9fa48("728", "729", "730"), (stryMutAct_9fa48("731") ? isEdit : (stryCov_9fa48("731"), !isEdit)) && mailingAddress.zipCode)}
          {stryMutAct_9fa48("734") ? isEdit || <Controller control={control} name="zipCode" rules={rules.zipCode} defaultValue={mailingAddress.zipCode} render={({
            field,
            fieldState: {
              error
            }
          }) => <Input id="mailing-address-zipcode" errorMessage={error && error.message || ""} {...field} placeholder={infoLabels.labels.zipCode} />} /> : stryMutAct_9fa48("733") ? false : stryMutAct_9fa48("732") ? true : (stryCov_9fa48("732", "733", "734"), isEdit && <Controller control={control} name="zipCode" rules={rules.zipCode} defaultValue={mailingAddress.zipCode} render={stryMutAct_9fa48("735") ? () => undefined : (stryCov_9fa48("735"), ({
            field,
            fieldState: {
              error
            }
          }) => <Input id="mailing-address-zipcode" errorMessage={stryMutAct_9fa48("738") ? error && error.message && "" : stryMutAct_9fa48("737") ? false : stryMutAct_9fa48("736") ? true : (stryCov_9fa48("736", "737", "738"), (stryMutAct_9fa48("740") ? error || error.message : stryMutAct_9fa48("739") ? false : (stryCov_9fa48("739", "740"), error && error.message)) || (stryMutAct_9fa48("741") ? "Stryker was here!" : (stryCov_9fa48("741"), "")))} {...field} placeholder={infoLabels.labels.zipCode} />)} />)}
        </div>
      </div>
    </>;
  }
};
export default memo(MailingAddressForm);