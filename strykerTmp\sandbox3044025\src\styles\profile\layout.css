.profile-base-container-web {
  @apply flex min-h-screen max-w-[1440px] px-meas15 pt-meas16 xl:flex;
}
.profile-base-container-tab {
  @apply block min-h-screen px-meas7 pt-meas32 md:pl-meas10 md:pr-meas10 md:pt-meas32 xl:hidden;
}
.profile-base-full-screen {
  @apply w-[790px] 2xl:w-[878px];
}
.form-action {
  @apply col-span-3 flex-none gap-x-meas4 md:col-span-1 md:text-right;
}
.form-sub-title-and-action {
  @apply col-span-3 grid grid-cols-3 py-meas4 md:h-meas22 md:pb-meas2 md:pt-meas0;
}
.success-message {
  @apply mb-meas4 flex flex-col items-center justify-center rounded bg-success-50 py-meas4 text-white;
}
.profile-information .form-action {
  @apply block justify-end md:flex;
}
.profile-creator-type-container .form-action {
  @apply block justify-end md:flex;
}
.profile-communication-preferences .form-action {
  @apply block justify-end md:flex;
}
.form-action .btn-tertiary {
  @apply mr-meas4;
}
