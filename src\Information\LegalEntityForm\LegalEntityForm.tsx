import { Controller, FieldValues, useFormContext } from "react-hook-form";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import { Select } from "@eait-playerexp-cn/core-ui-kit";
import ProfileFormAction from "../ProfileFormAction/ProfileFormAction";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import FormTitle from "../FormTitle/FormTitle";
import { Checkbox, Input, RadioButton } from "@eait-playerexp-cn/core-ui-kit";
import { ButtonLabels, InfoLabels, Overwrite } from "../types";
import { Country } from "@eait-playerexp-cn/metadata-types";
import { LegalEntityType, LegalInformationPayload } from "@eait-playerexp-cn/creator-types";
import { CreatorFormRules } from "@src/utils/FormRules/CreatorForm";

interface LegalEntityFormProps {
  infoLabels: InfoLabels;
  rules: CreatorFormRules;
  legalEntity: LegalInformationPayload;
  countries: Country[];
  buttons: ButtonLabels;
  onChangeAsMailingAddress: (data: FieldValues, isChecked: boolean) => void;
  onChange: () => void;
  isSaved: boolean;
  isLoader: boolean;
}

type LegalInformation = Overwrite<
  LegalInformationPayload,
  {
    country: Country;
    entityType: {
      value: LegalEntityType;
    };
  }
>;

const LegalEntityForm = ({
  infoLabels,
  rules,
  legalEntity,
  countries,
  buttons,
  onChange,
  isSaved = false,
  onChangeAsMailingAddress,
  isLoader
}: LegalEntityFormProps) => {
  const { control, reset, getValues, setValue } = useFormContext();
  const [isEdit, setIsEdit] = useState(false);
  const [asMailingAddress, setAsMailingAddress] = useState(false);
  const [entityType, setEntityType] = useState(null);
  const [legalEntityValue, setLegalEntityValue] = useState(null);
  const { success: successToast } = useToast();
  const timetoDisplay = Math.min(Math.max(infoLabels.success.legalEntityType.length * 50, 2000), 7000);

  const onEditChange = useCallback(
    (isChecked) => {
      setIsEdit(isChecked);
      setAsMailingAddress(!isChecked);
      if (onChange) onChange();

      if (!isChecked && legalEntityValue) {
        setEntityType(legalEntityValue.entityType);
      } else {
        const values = getValues();
        if (values) {
          setEntityType(values.entityType);
        }
      }
    },
    [getValues, legalEntityValue, onChange]
  );

  const LegalEntityOptions = useMemo(() => {
    return [
      { value: "INDIVIDUAL", label: infoLabels.labels.individual },
      { value: "BUSINESS", label: infoLabels.labels.business }
    ];
  }, [infoLabels.labels.business, infoLabels.labels.individual]);

  useEffect(() => {
    if (isEdit && isSaved) setIsEdit(false);
  }, [isSaved, isEdit]);

  const updateAsMailingAddress = useCallback(
    (event) => {
      if (onChangeAsMailingAddress) onChangeAsMailingAddress(getValues(), event.target.checked);
      setAsMailingAddress(event.target.checked);
    },
    [getValues, onChangeAsMailingAddress]
  );

  useEffect(() => {
    const values = { ...legalEntity } as unknown as LegalInformation;
    if (legalEntity?.country?.code) {
      values.country = { value: legalEntity.country.code, label: legalEntity.country.name } as unknown as Country;
    }
    if (typeof legalEntity?.entityType === "string") {
      values.entityType = { value: legalEntity.entityType };
    }
    if (values) {
      setEntityType(values.entityType);
      setLegalEntityValue(values);
      reset(values);
    }
  }, [legalEntity, reset]);

  const updateEntityType = useCallback(
    (item, field) => {
      setEntityType(item);
      field.onChange(item);
      if (item.value === "INDIVIDUAL") {
        const values = getValues();
        delete values.businessName;
        reset(values);
      }
    },
    [getValues, reset]
  );

  useEffect(() => {
    if (legalEntityValue && Array.isArray(countries)) {
      setValue("country", legalEntityValue?.country || countries[0]);
    }
  }, [legalEntityValue, countries]);

  return (
    legalEntityValue && (
      <>
        {isSaved &&
          isEdit &&
          successToast(
            <Toast header={infoLabels.success.updatedInformationHeader} content={infoLabels.success.legalEntityType} />,
            {
              autoClose: timetoDisplay
            }
          )}
        <div className="legal-entity-information">
          <div className="form-sub-title-and-action">
            <FormTitle subTitle={infoLabels.legalEntityType} />

            <ProfileFormAction {...{ buttons, action: onEditChange, isSaved, isLoader }} />
          </div>
          <div className="profile-legal-entity-description">{infoLabels.legalEntityDescription}</div>

          <div className="legal-entity-field-title legal-entity-type-title">{infoLabels.labels.entityType}</div>
          <div className="legal-entity-field">
            {!isEdit &&
              LegalEntityOptions.filter((item) => item.value === legalEntityValue.entityType?.value)[0]?.label}
            {isEdit && (
              <Controller
                control={control}
                name="entityType"
                rules={rules.entityType}
                render={({ field, fieldState: { error } }) => (
                  <RadioButton
                    errorMessage={(error && error.message) || ""}
                    {...field}
                    selectedOption={field.value}
                    onChange={(item) => updateEntityType(item, field)}
                    options={LegalEntityOptions}
                  />
                )}
              />
            )}
          </div>

          {entityType?.value === "BUSINESS" && (
            <>
              <div className="legal-entity-field-title inline">{infoLabels.labels.businessName}</div>
              <div className="legal-entity-field">
                {!isEdit && legalEntityValue.businessName}
                {isEdit && (
                  <Controller
                    control={control}
                    name="businessName"
                    rules={rules.businessName}
                    render={({ field, fieldState: { error } }) => (
                      <Input
                        id="legal-entity-businessname"
                        errorMessage={(error && error.message) || ""}
                        {...field}
                        helpText={infoLabels.info.businessName}
                        placeholder={infoLabels.labels.businessName}
                      />
                    )}
                  />
                )}
              </div>
            </>
          )}

          <div className="legal-entity-field-title">{infoLabels.labels.street}</div>
          <div className="legal-entity-field">
            {!isEdit || asMailingAddress
              ? legalEntityValue.street
              : !asMailingAddress && (
                  <Controller
                    control={control}
                    name="street"
                    rules={rules.street}
                    render={({ field, fieldState: { error } }) => (
                      <Input
                        id="legal-entity-street"
                        errorMessage={(error && error.message) || ""}
                        {...field}
                        placeholder={infoLabels.labels.street}
                      />
                    )}
                  />
                )}
          </div>

          <div className="legal-entity-field-title">{infoLabels.labels.city}</div>
          <div className="legal-entity-field">
            {!isEdit || asMailingAddress
              ? legalEntityValue.city
              : !asMailingAddress && (
                  <Controller
                    control={control}
                    name="city"
                    rules={rules.city}
                    render={({ field, fieldState: { error } }) => (
                      <Input
                        id="legal-entity-city"
                        errorMessage={(error && error.message) || ""}
                        {...field}
                        placeholder={infoLabels.labels.city}
                        ariaLabel={infoLabels.labels.city}
                      />
                    )}
                  />
                )}
          </div>

          <div className="legal-entity-field-title">{infoLabels.labels.country}</div>
          <div className="legal-entity-field">
            {!isEdit || asMailingAddress
              ? legalEntityValue.country?.label
              : !asMailingAddress && (
                  <Controller
                    control={control}
                    name="country"
                    rules={rules.country}
                    render={({ field, fieldState: { error } }) => (
                      <Select
                        id="legal-entity-country"
                        selectedOption={legalEntityValue.country}
                        errorMessage={error && error.message}
                        options={countries}
                        onChange={(item) => {
                          field.onChange(item);
                        }}
                      />
                    )}
                  />
                )}
          </div>

          <div className="legal-entity-field-title">{infoLabels.labels.state}</div>
          <div className="legal-entity-field">
            {!isEdit || asMailingAddress
              ? legalEntityValue.state
              : !asMailingAddress && (
                  <Controller
                    control={control}
                    name="state"
                    rules={rules.state}
                    render={({ field, fieldState: { error } }) => (
                      <Input
                        id="legal-entity-state"
                        errorMessage={(error && error.message) || ""}
                        {...field}
                        placeholder={infoLabels.labels.state}
                      />
                    )}
                  />
                )}
          </div>

          <div className="legal-entity-field-title">{infoLabels.labels.zipCode}</div>
          <div className="legal-entity-field">
            {!isEdit || asMailingAddress
              ? legalEntityValue.zipCode
              : !asMailingAddress && (
                  <Controller
                    control={control}
                    name="zipCode"
                    rules={rules.zipCode}
                    render={({ field, fieldState: { error } }) => (
                      <Input
                        id="legal-entity-zipcode"
                        errorMessage={(error && error.message) || ""}
                        {...field}
                        placeholder={infoLabels.labels.zipCode}
                      />
                    )}
                  />
                )}
          </div>

          {isEdit && (
            <>
              <div className="legal-entity-field-title">{""}</div>
              <div className="legal-entity-field">
                <Checkbox
                  options={[
                    {
                      id: "asMailingAddress",
                      label: infoLabels.labels.legalAddressAsMailingAddress,
                      isChecked: asMailingAddress,
                      onChange: updateAsMailingAddress,
                      dark: true
                    }
                  ]}
                />
              </div>
            </>
          )}
        </div>
      </>
    )
  );
};
export default memo(LegalEntityForm);
