// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import { CreatorCodeStatus, HoodieSize, LegalEntityType, PreferenceType, ProgramMembershipStatus } from "@eait-playerexp-cn/creator-types";
import { faker } from "@faker-js/faker";
const {
  location,
  company,
  datatype,
  date,
  git,
  helpers,
  image,
  lorem,
  number,
  person,
  internet,
  string,
  phone,
  system
} = faker;
export default class Random {
  static nonProductionEnvironment(): string {
    if (stryMutAct_9fa48("5")) {
      {}
    } else {
      stryCov_9fa48("5");
      return helpers.arrayElement(stryMutAct_9fa48("6") ? [] : (stryCov_9fa48("6"), [stryMutAct_9fa48("7") ? "" : (stryCov_9fa48("7"), "dev"), stryMutAct_9fa48("8") ? "" : (stryCov_9fa48("8"), "qa"), stryMutAct_9fa48("9") ? "" : (stryCov_9fa48("9"), "uat"), stryMutAct_9fa48("10") ? "" : (stryCov_9fa48("10"), "regress")]));
    }
  }
  static userStatus(): ProgramMembershipStatus {
    if (stryMutAct_9fa48("11")) {
      {}
    } else {
      stryCov_9fa48("11");
      return helpers.arrayElement(stryMutAct_9fa48("12") ? [] : (stryCov_9fa48("12"), [stryMutAct_9fa48("13") ? "" : (stryCov_9fa48("13"), "UNREGISTERED"), stryMutAct_9fa48("14") ? "" : (stryCov_9fa48("14"), "INACTIVE"), stryMutAct_9fa48("15") ? "" : (stryCov_9fa48("15"), "ACTIVE")]));
    }
  }
  static country(): string {
    if (stryMutAct_9fa48("16")) {
      {}
    } else {
      stryCov_9fa48("16");
      return helpers.arrayElement(stryMutAct_9fa48("17") ? [] : (stryCov_9fa48("17"), [stryMutAct_9fa48("18") ? "" : (stryCov_9fa48("18"), "Canada"), stryMutAct_9fa48("19") ? "" : (stryCov_9fa48("19"), "India"), stryMutAct_9fa48("20") ? "" : (stryCov_9fa48("20"), "United States"), stryMutAct_9fa48("21") ? "" : (stryCov_9fa48("21"), "Ukraine")]));
    }
  }
  static tShirtSize(): HoodieSize {
    if (stryMutAct_9fa48("22")) {
      {}
    } else {
      stryCov_9fa48("22");
      return helpers.arrayElement(stryMutAct_9fa48("23") ? [] : (stryCov_9fa48("23"), [stryMutAct_9fa48("24") ? "" : (stryCov_9fa48("24"), "S"), stryMutAct_9fa48("25") ? "" : (stryCov_9fa48("25"), "M"), stryMutAct_9fa48("26") ? "" : (stryCov_9fa48("26"), "XL"), stryMutAct_9fa48("27") ? "" : (stryCov_9fa48("27"), "XXL")]));
    }
  }
  static accountType(): string {
    if (stryMutAct_9fa48("28")) {
      {}
    } else {
      stryCov_9fa48("28");
      return helpers.arrayElement(stryMutAct_9fa48("29") ? [] : (stryCov_9fa48("29"), [stryMutAct_9fa48("30") ? "" : (stryCov_9fa48("30"), "TWITCH"), stryMutAct_9fa48("31") ? "" : (stryCov_9fa48("31"), "FACEBOOK"), stryMutAct_9fa48("32") ? "" : (stryCov_9fa48("32"), "INSTAGRAM"), stryMutAct_9fa48("33") ? "" : (stryCov_9fa48("33"), "YOUTUBE"), stryMutAct_9fa48("34") ? "" : (stryCov_9fa48("34"), "TIKTOK")]));
    }
  }
  static creatorTypeLabel(): string {
    if (stryMutAct_9fa48("35")) {
      {}
    } else {
      stryCov_9fa48("35");
      return helpers.arrayElement(stryMutAct_9fa48("36") ? [] : (stryCov_9fa48("36"), [stryMutAct_9fa48("37") ? "" : (stryCov_9fa48("37"), "Youtuber"), stryMutAct_9fa48("38") ? "" : (stryCov_9fa48("38"), "Live Streamer"), stryMutAct_9fa48("39") ? "" : (stryCov_9fa48("39"), "Podcaster"), stryMutAct_9fa48("40") ? "" : (stryCov_9fa48("40"), "Blogger")]));
    }
  }
  static creatorType(): string {
    if (stryMutAct_9fa48("41")) {
      {}
    } else {
      stryCov_9fa48("41");
      return helpers.arrayElement(stryMutAct_9fa48("42") ? [] : (stryCov_9fa48("42"), [stryMutAct_9fa48("43") ? "" : (stryCov_9fa48("43"), "YOUTUBER"), stryMutAct_9fa48("44") ? "" : (stryCov_9fa48("44"), "LIVE_STREAMER"), stryMutAct_9fa48("45") ? "" : (stryCov_9fa48("45"), "PODCASTER"), stryMutAct_9fa48("46") ? "" : (stryCov_9fa48("46"), "BLOGGER")]));
    }
  }
  static entityType(): LegalEntityType {
    if (stryMutAct_9fa48("47")) {
      {}
    } else {
      stryCov_9fa48("47");
      return helpers.arrayElement(stryMutAct_9fa48("48") ? [] : (stryCov_9fa48("48"), [stryMutAct_9fa48("49") ? "" : (stryCov_9fa48("49"), "INDIVIDUAL"), stryMutAct_9fa48("50") ? "" : (stryCov_9fa48("50"), "BUSINESS")]));
    }
  }
  static franchiseType(): PreferenceType {
    if (stryMutAct_9fa48("51")) {
      {}
    } else {
      stryCov_9fa48("51");
      return helpers.arrayElement(stryMutAct_9fa48("52") ? [] : (stryCov_9fa48("52"), [stryMutAct_9fa48("53") ? "" : (stryCov_9fa48("53"), "PRIMARY"), stryMutAct_9fa48("54") ? "" : (stryCov_9fa48("54"), "SECONDARY")]));
    }
  }
  static platformType(): PreferenceType {
    if (stryMutAct_9fa48("55")) {
      {}
    } else {
      stryCov_9fa48("55");
      return helpers.arrayElement(stryMutAct_9fa48("56") ? [] : (stryCov_9fa48("56"), [stryMutAct_9fa48("57") ? "" : (stryCov_9fa48("57"), "PRIMARY"), stryMutAct_9fa48("58") ? "" : (stryCov_9fa48("58"), "SECONDARY")]));
    }
  }
  static franchise(): string {
    if (stryMutAct_9fa48("59")) {
      {}
    } else {
      stryCov_9fa48("59");
      return helpers.arrayElement(stryMutAct_9fa48("60") ? [] : (stryCov_9fa48("60"), [stryMutAct_9fa48("61") ? "" : (stryCov_9fa48("61"), "Need for Speed"), stryMutAct_9fa48("62") ? "" : (stryCov_9fa48("62"), "Apex Legends"), stryMutAct_9fa48("63") ? "" : (stryCov_9fa48("63"), "Madden NFL"), stryMutAct_9fa48("64") ? "" : (stryCov_9fa48("64"), "The Sims"), stryMutAct_9fa48("65") ? "" : (stryCov_9fa48("65"), "FIFA")]));
    }
  }
  static region(): string {
    if (stryMutAct_9fa48("66")) {
      {}
    } else {
      stryCov_9fa48("66");
      return helpers.arrayElement(stryMutAct_9fa48("67") ? [] : (stryCov_9fa48("67"), [stryMutAct_9fa48("68") ? "" : (stryCov_9fa48("68"), "Asia"), stryMutAct_9fa48("69") ? "" : (stryCov_9fa48("69"), "Europe"), stryMutAct_9fa48("70") ? "" : (stryCov_9fa48("70"), "America")]));
    }
  }
  static applicationStatus(): string {
    if (stryMutAct_9fa48("71")) {
      {}
    } else {
      stryCov_9fa48("71");
      return helpers.arrayElement(stryMutAct_9fa48("72") ? [] : (stryCov_9fa48("72"), [stryMutAct_9fa48("73") ? "" : (stryCov_9fa48("73"), "UNREVIEWED"), stryMutAct_9fa48("74") ? "" : (stryCov_9fa48("74"), "APPROVED"), stryMutAct_9fa48("75") ? "" : (stryCov_9fa48("75"), "REJECTED")]));
    }
  }
  static applicationType(): string {
    if (stryMutAct_9fa48("76")) {
      {}
    } else {
      stryCov_9fa48("76");
      return helpers.arrayElement(stryMutAct_9fa48("77") ? [] : (stryCov_9fa48("77"), [stryMutAct_9fa48("78") ? "" : (stryCov_9fa48("78"), "Applicant"), stryMutAct_9fa48("79") ? "" : (stryCov_9fa48("79"), "Creator")]));
    }
  }
  static opportunityType(): string {
    if (stryMutAct_9fa48("80")) {
      {}
    } else {
      stryCov_9fa48("80");
      return helpers.arrayElement(stryMutAct_9fa48("81") ? [] : (stryCov_9fa48("81"), [stryMutAct_9fa48("82") ? "" : (stryCov_9fa48("82"), "support_a_creator"), stryMutAct_9fa48("83") ? "" : (stryCov_9fa48("83"), "marketing_opportunity")]));
    }
  }
  static opportunityVisibility(): string {
    if (stryMutAct_9fa48("84")) {
      {}
    } else {
      stryCov_9fa48("84");
      return helpers.arrayElement(stryMutAct_9fa48("85") ? [] : (stryCov_9fa48("85"), [stryMutAct_9fa48("86") ? "" : (stryCov_9fa48("86"), "PRIVATE"), stryMutAct_9fa48("87") ? "" : (stryCov_9fa48("87"), "PUBLIC")]));
    }
  }
  static participationStatus(): string {
    if (stryMutAct_9fa48("88")) {
      {}
    } else {
      stryCov_9fa48("88");
      return helpers.arrayElement(stryMutAct_9fa48("89") ? [] : (stryCov_9fa48("89"), [stryMutAct_9fa48("90") ? "" : (stryCov_9fa48("90"), "INVITED"), stryMutAct_9fa48("91") ? "" : (stryCov_9fa48("91"), "JOINED"), stryMutAct_9fa48("92") ? "" : (stryCov_9fa48("92"), "REJECTED"), stryMutAct_9fa48("93") ? "" : (stryCov_9fa48("93"), "PAST")]));
    }
  }
  static contentType(): string {
    if (stryMutAct_9fa48("94")) {
      {}
    } else {
      stryCov_9fa48("94");
      return helpers.arrayElement(stryMutAct_9fa48("95") ? [] : (stryCov_9fa48("95"), [stryMutAct_9fa48("96") ? "" : (stryCov_9fa48("96"), "video"), stryMutAct_9fa48("97") ? "" : (stryCov_9fa48("97"), "image"), stryMutAct_9fa48("98") ? "" : (stryCov_9fa48("98"), "blog")]));
    }
  }
  static fileType(): string {
    if (stryMutAct_9fa48("99")) {
      {}
    } else {
      stryCov_9fa48("99");
      return helpers.arrayElement(stryMutAct_9fa48("100") ? [] : (stryCov_9fa48("100"), [stryMutAct_9fa48("101") ? "" : (stryCov_9fa48("101"), ".mov"), stryMutAct_9fa48("102") ? "" : (stryCov_9fa48("102"), ".png"), stryMutAct_9fa48("103") ? "" : (stryCov_9fa48("103"), ".txt"), stryMutAct_9fa48("104") ? "" : (stryCov_9fa48("104"), ".jpeg")]));
    }
  }
  static transactionStatus(): string {
    if (stryMutAct_9fa48("105")) {
      {}
    } else {
      stryCov_9fa48("105");
      return helpers.arrayElement(stryMutAct_9fa48("106") ? [] : (stryCov_9fa48("106"), [stryMutAct_9fa48("107") ? "" : (stryCov_9fa48("107"), "PENDING"), stryMutAct_9fa48("108") ? "" : (stryCov_9fa48("108"), "PAID")]));
    }
  }
  static contentStatus(): string {
    if (stryMutAct_9fa48("109")) {
      {}
    } else {
      stryCov_9fa48("109");
      return helpers.arrayElement(stryMutAct_9fa48("110") ? [] : (stryCov_9fa48("110"), [stryMutAct_9fa48("111") ? "" : (stryCov_9fa48("111"), "APPROVED"), stryMutAct_9fa48("112") ? "" : (stryCov_9fa48("112"), "REJECTED"), stryMutAct_9fa48("113") ? "" : (stryCov_9fa48("113"), "UNREVIEWED"), stryMutAct_9fa48("114") ? "" : (stryCov_9fa48("114"), "IN_SCAN"), stryMutAct_9fa48("115") ? "" : (stryCov_9fa48("115"), "CHANGE_REQUESTED")]));
    }
  }
  static perkCode(): string {
    if (stryMutAct_9fa48("116")) {
      {}
    } else {
      stryCov_9fa48("116");
      return helpers.arrayElement(stryMutAct_9fa48("117") ? [] : (stryCov_9fa48("117"), [stryMutAct_9fa48("118") ? "" : (stryCov_9fa48("118"), "VIP_EVENT"), stryMutAct_9fa48("119") ? "" : (stryCov_9fa48("119"), "TRAVEL"), stryMutAct_9fa48("120") ? "" : (stryCov_9fa48("120"), "SWAG"), stryMutAct_9fa48("121") ? "" : (stryCov_9fa48("121"), "COLLAB")]));
    }
  }
  static perkLabel(): string {
    if (stryMutAct_9fa48("122")) {
      {}
    } else {
      stryCov_9fa48("122");
      return helpers.arrayElement(stryMutAct_9fa48("123") ? [] : (stryCov_9fa48("123"), [stryMutAct_9fa48("124") ? "" : (stryCov_9fa48("124"), "VIP Event"), stryMutAct_9fa48("125") ? "" : (stryCov_9fa48("125"), "Travel"), stryMutAct_9fa48("126") ? "" : (stryCov_9fa48("126"), "Swag"), stryMutAct_9fa48("127") ? "" : (stryCov_9fa48("127"), "Collab")]));
    }
  }
  static platform(): string {
    if (stryMutAct_9fa48("128")) {
      {}
    } else {
      stryCov_9fa48("128");
      return helpers.arrayElement(stryMutAct_9fa48("129") ? [] : (stryCov_9fa48("129"), [stryMutAct_9fa48("130") ? "" : (stryCov_9fa48("130"), "Nintendo Switch"), stryMutAct_9fa48("131") ? "" : (stryCov_9fa48("131"), "XBox"), stryMutAct_9fa48("132") ? "" : (stryCov_9fa48("132"), "PlayStation"), stryMutAct_9fa48("133") ? "" : (stryCov_9fa48("133"), "Mobile"), stryMutAct_9fa48("134") ? "" : (stryCov_9fa48("134"), "Mac")]));
    }
  }
  static notificationType(): string {
    if (stryMutAct_9fa48("135")) {
      {}
    } else {
      stryCov_9fa48("135");
      return helpers.arrayElement(stryMutAct_9fa48("136") ? [] : (stryCov_9fa48("136"), [stryMutAct_9fa48("137") ? "" : (stryCov_9fa48("137"), "OPPORTUNITY_INVITATION"), stryMutAct_9fa48("138") ? "" : (stryCov_9fa48("138"), "CHANNEL_EXPIRY"), stryMutAct_9fa48("139") ? "" : (stryCov_9fa48("139"), "CONTENT_SUBMISSION_START"), stryMutAct_9fa48("140") ? "" : (stryCov_9fa48("140"), "CONTENT_SUBMISSION_END"), stryMutAct_9fa48("141") ? "" : (stryCov_9fa48("141"), "CONTENT_REJECTED"), stryMutAct_9fa48("142") ? "" : (stryCov_9fa48("142"), "CONTENT_APPROVED"), stryMutAct_9fa48("143") ? "" : (stryCov_9fa48("143"), "CREATOR_PAYMENT"), stryMutAct_9fa48("144") ? "" : (stryCov_9fa48("144"), "CONTENT_FEEDBACK"), stryMutAct_9fa48("145") ? "" : (stryCov_9fa48("145"), "PROFILE_INCOMPLETE"), stryMutAct_9fa48("146") ? "" : (stryCov_9fa48("146"), "PROFILE_COMPLETE")]));
    }
  }
  static notificationStatus(): string {
    if (stryMutAct_9fa48("147")) {
      {}
    } else {
      stryCov_9fa48("147");
      return helpers.arrayElement(stryMutAct_9fa48("148") ? [] : (stryCov_9fa48("148"), [stryMutAct_9fa48("149") ? "" : (stryCov_9fa48("149"), "READ"), stryMutAct_9fa48("150") ? "" : (stryCov_9fa48("150"), "NEW"), stryMutAct_9fa48("151") ? "" : (stryCov_9fa48("151"), "DISMISSED")]));
    }
  }
  static gitBranch(): string {
    if (stryMutAct_9fa48("152")) {
      {}
    } else {
      stryCov_9fa48("152");
      return git.branch();
    }
  }
  static url(): string {
    if (stryMutAct_9fa48("153")) {
      {}
    } else {
      stryCov_9fa48("153");
      return internet.url();
    }
  }
  static password(): string {
    if (stryMutAct_9fa48("154")) {
      {}
    } else {
      stryCov_9fa48("154");
      return internet.password();
    }
  }
  static address(): string {
    if (stryMutAct_9fa48("155")) {
      {}
    } else {
      stryCov_9fa48("155");
      return location.streetAddress();
    }
  }
  static boolean(): boolean {
    if (stryMutAct_9fa48("156")) {
      {}
    } else {
      stryCov_9fa48("156");
      return datatype.boolean();
    }
  }
  static string(): string {
    if (stryMutAct_9fa48("157")) {
      {}
    } else {
      stryCov_9fa48("157");
      return string.alphanumeric();
    }
  }
  static phone(): string {
    if (stryMutAct_9fa48("158")) {
      {}
    } else {
      stryCov_9fa48("158");
      return phone.number();
    }
  }
  static uuid(): string {
    if (stryMutAct_9fa48("159")) {
      {}
    } else {
      stryCov_9fa48("159");
      return string.uuid();
    }
  }
  static userName(): string {
    if (stryMutAct_9fa48("160")) {
      {}
    } else {
      stryCov_9fa48("160");
      return internet.username();
    }
  }
  static sentence(wordCount: number): string {
    if (stryMutAct_9fa48("161")) {
      {}
    } else {
      stryCov_9fa48("161");
      return lorem.sentence(wordCount);
    }
  }
  static avatar(): string {
    if (stryMutAct_9fa48("162")) {
      {}
    } else {
      stryCov_9fa48("162");
      return image.avatar();
    }
  }
  static imageUrl(): string {
    if (stryMutAct_9fa48("163")) {
      {}
    } else {
      stryCov_9fa48("163");
      return image.url();
    }
  }
  static firstName(): string {
    if (stryMutAct_9fa48("164")) {
      {}
    } else {
      stryCov_9fa48("164");
      return person.firstName();
    }
  }
  static lastName(): string {
    if (stryMutAct_9fa48("165")) {
      {}
    } else {
      stryCov_9fa48("165");
      return person.lastName();
    }
  }
  static fullName(): string {
    if (stryMutAct_9fa48("166")) {
      {}
    } else {
      stryCov_9fa48("166");
      return person.fullName();
    }
  }
  static contentVersion(): string {
    if (stryMutAct_9fa48("167")) {
      {}
    } else {
      stryCov_9fa48("167");
      return system.semver();
    }
  }
  static email(): string {
    if (stryMutAct_9fa48("168")) {
      {}
    } else {
      stryCov_9fa48("168");
      return internet.email();
    }
  }
  static number(range?: {
    min: number;
    max: number;
  }): number {
    if (stryMutAct_9fa48("169")) {
      {}
    } else {
      stryCov_9fa48("169");
      return number.int(range);
    }
  }
  static countryCode(): string {
    if (stryMutAct_9fa48("170")) {
      {}
    } else {
      stryCov_9fa48("170");
      return location.countryCode();
    }
  }
  static state(): string {
    if (stryMutAct_9fa48("171")) {
      {}
    } else {
      stryCov_9fa48("171");
      return location.state();
    }
  }
  static city(): string {
    if (stryMutAct_9fa48("172")) {
      {}
    } else {
      stryCov_9fa48("172");
      return location.city();
    }
  }
  static streetAddress(): string {
    if (stryMutAct_9fa48("173")) {
      {}
    } else {
      stryCov_9fa48("173");
      return location.streetAddress();
    }
  }
  static zipCode(): string {
    if (stryMutAct_9fa48("174")) {
      {}
    } else {
      stryCov_9fa48("174");
      return location.zipCode();
    }
  }
  static date(): string {
    if (stryMutAct_9fa48("175")) {
      {}
    } else {
      stryCov_9fa48("175");
      return date.recent.toString();
    }
  }
  static locale(): string {
    if (stryMutAct_9fa48("176")) {
      {}
    } else {
      stryCov_9fa48("176");
      return helpers.arrayElement(stryMutAct_9fa48("177") ? [] : (stryCov_9fa48("177"), [stryMutAct_9fa48("178") ? "" : (stryCov_9fa48("178"), "es-mx"), stryMutAct_9fa48("179") ? "" : (stryCov_9fa48("179"), "en-us"), stryMutAct_9fa48("180") ? "" : (stryCov_9fa48("180"), "fr-ca"), stryMutAct_9fa48("181") ? "" : (stryCov_9fa48("181"), "it-it"), stryMutAct_9fa48("182") ? "" : (stryCov_9fa48("182"), "ja-jp")]));
    }
  }
  static nucleusId(): number {
    if (stryMutAct_9fa48("183")) {
      {}
    } else {
      stryCov_9fa48("183");
      return stryMutAct_9fa48("184") ? -string.numeric(13) : (stryCov_9fa48("184"), +string.numeric(13));
    }
  }
  static subscribersCount(): number {
    if (stryMutAct_9fa48("185")) {
      {}
    } else {
      stryCov_9fa48("185");
      return number.int(stryMutAct_9fa48("186") ? {} : (stryCov_9fa48("186"), {
        min: 3_000_000
      }));
    }
  }
  static companyName(): string {
    if (stryMutAct_9fa48("187")) {
      {}
    } else {
      stryCov_9fa48("187");
      return company.name();
    }
  }
  static amount(): string {
    if (stryMutAct_9fa48("188")) {
      {}
    } else {
      stryCov_9fa48("188");
      return string.numeric(13);
    }
  }
  static deliverableStatus(): string {
    if (stryMutAct_9fa48("189")) {
      {}
    } else {
      stryCov_9fa48("189");
      return helpers.arrayElement(stryMutAct_9fa48("190") ? [] : (stryCov_9fa48("190"), [stryMutAct_9fa48("191") ? "" : (stryCov_9fa48("191"), "APPROVED"), stryMutAct_9fa48("192") ? "" : (stryCov_9fa48("192"), "REJECTED"), stryMutAct_9fa48("193") ? "" : (stryCov_9fa48("193"), "PENDING"), stryMutAct_9fa48("194") ? "" : (stryCov_9fa48("194"), "CHANGE_REQUESTED"), stryMutAct_9fa48("195") ? "" : (stryCov_9fa48("195"), "AWAITING_SUBMISSION"), stryMutAct_9fa48("196") ? "" : (stryCov_9fa48("196"), "UNLIMITED_CONTENT")]));
    }
  }
  static deliverableFormat(): string {
    if (stryMutAct_9fa48("197")) {
      {}
    } else {
      stryCov_9fa48("197");
      return helpers.arrayElement(stryMutAct_9fa48("198") ? [] : (stryCov_9fa48("198"), [stryMutAct_9fa48("199") ? "" : (stryCov_9fa48("199"), "SOCIAL"), stryMutAct_9fa48("200") ? "" : (stryCov_9fa48("200"), "WEBSITE"), stryMutAct_9fa48("201") ? "" : (stryCov_9fa48("201"), "FILE")]));
    }
  }
  static deliverableType(): string {
    if (stryMutAct_9fa48("202")) {
      {}
    } else {
      stryCov_9fa48("202");
      return helpers.arrayElement(stryMutAct_9fa48("203") ? [] : (stryCov_9fa48("203"), [stryMutAct_9fa48("204") ? "" : (stryCov_9fa48("204"), "SINGLE"), stryMutAct_9fa48("205") ? "" : (stryCov_9fa48("205"), "UNLIMITED")]));
    }
  }
  static accessToken(): string {
    if (stryMutAct_9fa48("206")) {
      {}
    } else {
      stryCov_9fa48("206");
      return string.alphanumeric(20);
    }
  }
  static scale(): string {
    if (stryMutAct_9fa48("207")) {
      {}
    } else {
      stryCov_9fa48("207");
      return helpers.arrayElement(stryMutAct_9fa48("208") ? [] : (stryCov_9fa48("208"), [stryMutAct_9fa48("209") ? "" : (stryCov_9fa48("209"), "DAYS"), stryMutAct_9fa48("210") ? "" : (stryCov_9fa48("210"), "MONTHS"), stryMutAct_9fa48("211") ? "" : (stryCov_9fa48("211"), "YEARS")]));
    }
  }
  static creatorCodeStatus(): CreatorCodeStatus {
    if (stryMutAct_9fa48("212")) {
      {}
    } else {
      stryCov_9fa48("212");
      return helpers.arrayElement(stryMutAct_9fa48("213") ? [] : (stryCov_9fa48("213"), [stryMutAct_9fa48("214") ? "" : (stryCov_9fa48("214"), "ACTIVE"), stryMutAct_9fa48("215") ? "" : (stryCov_9fa48("215"), "INACTIVE"), stryMutAct_9fa48("216") ? "" : (stryCov_9fa48("216"), "PENDING"), stryMutAct_9fa48("217") ? "" : (stryCov_9fa48("217"), "READYFORAPPROVAL")]));
    }
  }
  static programCode(): string {
    if (stryMutAct_9fa48("218")) {
      {}
    } else {
      stryCov_9fa48("218");
      return helpers.arrayElement(stryMutAct_9fa48("219") ? [] : (stryCov_9fa48("219"), [stryMutAct_9fa48("220") ? "" : (stryCov_9fa48("220"), "creator_network"), stryMutAct_9fa48("221") ? "" : (stryCov_9fa48("221"), "affiliate"), stryMutAct_9fa48("222") ? "" : (stryCov_9fa48("222"), "the_sims")]));
    }
  }
  static pronouns(): string {
    if (stryMutAct_9fa48("223")) {
      {}
    } else {
      stryCov_9fa48("223");
      return helpers.arrayElement(stryMutAct_9fa48("224") ? [] : (stryCov_9fa48("224"), [stryMutAct_9fa48("225") ? "" : (stryCov_9fa48("225"), "Mr"), stryMutAct_9fa48("226") ? "" : (stryCov_9fa48("226"), "Mrs"), stryMutAct_9fa48("227") ? "" : (stryCov_9fa48("227"), "Miss")]));
    }
  }
}