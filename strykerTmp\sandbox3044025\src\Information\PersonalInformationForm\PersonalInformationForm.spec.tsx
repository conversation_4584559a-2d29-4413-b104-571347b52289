// @ts-nocheck
import { act, render, screen, waitFor, within, fireEvent } from "@testing-library/react";
import PersonalInformationForm from "./PersonalInformationForm";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import React from "react";
import { NextRouter } from "next/router";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { clearValueFor, enterValueFor, clearDateFor, enterDateFor } from "@src/Helpers/Forms";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import Random from "@src/Factories/Random";
import { onToastClose } from "@src/utils";
import formsRules from "@src/utils/FormRules/CreatorForm";
import { isAdult } from "@src/utils";

jest.mock("@src/utils", () => ({
  ...jest.requireActual("@src/utils"),
  isAdult: jest.fn(),
  onToastClose: jest.fn()
}));

const FormWrapper = ({ children }) => {
  const methods = useForm({ 
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("PersonalInformationForm", () => {
  const validDate = new Date(1995, 6, 30); // July 30, 1995
  const accountInformation = {
    defaultGamerTag: "245902",
    nucleusId: *************,
    firstName: Random.firstName(),
    lastName: Random.lastName(),
    originEmail: Random.email(),
    dateOfBirth: {
      millisecondsEpoch: ************,
      format: (_format: string, _locale?: string) => "July 30, 1995",
      toDate: () => validDate
    } as unknown as LocalizedDate,
    needsMigration: false,
    payable: true,
    flagged: false,
    disabled: false,
    preferredName: Random.firstName(),
    preferredPronouns: null
  };

  const infoLabels = {
      personalInformation: "Personal Information",
      creatorSince: "Creator Since",
      legalEntityType: "Legal Entity Type",
      legalEntityDescription: "Legal Entity Description",
      mailingAddress: "Mailing Address",
      miscellaneous: "Miscellaneous",
      success: {
        updatedInformationHeader: "Information update successful",
        personalInformation: "You have successfully updated your Personal Information.",
        legalEntityType: "You have successfully updated your Legal Entity Type.",
        mailingAddress: "You have successfully updated your Mailing Address.",
        miscellaneous: "You have successfully updated your Miscellaneous Information."
      },
      header: {
        calendar: "Calendar"
      },
      labels: {
        none: "None",
        firstName: "First Name",
        lastName: "Last Name",
        EAID: "Electronic Arts ID",
        EAEmail: "Electronic Arts Account Email",
        dateOfBirth: "Date of Birth",
        country: "Country/Region",
        street: "Street",
        city: "City",
        state: "State or Province",
        zipCode: "Zip Code or Postal Code",
        tShirtSize: "T-Shirt Size",
        hardwarePartners: "Hardware Partners",
        entityType: "Entity Type",
        individual: "Individual",
        business: "Business",
        businessName: "Name of Business",
        legalAddressAsMailingAddress: "Address is the same as my mailing address."
      },
      messages: {
        firstName: "First Name is required",
        firstNameTooLong: "First Name is too long",
        lastName: "Last Name is required",
        lastNameTooLong: "Last Name is too long",
        dateOfBirth: "Date of Birth is required",
        dateOfBirthInvalid: "Date of Birth is invalid",
        ageMustBe18OrOlder: "Age must be 18 years or older",
        country: "Country is required",
        street: "Street is required",
        streetTooLong: "Street is too long",
        city: "City is required",
        cityTooLong: "City is too long",
        state: "State is required",
        stateTooLong: "State is too long",
        zipCode: "Zip Code is required",
        zipCodeTooLong: "Zip Code is too long",
        primaryPlatform: "Primary Platform is required",
        tShirtSize: "T-Shirt Size is required",
        hardwarePartners: "Hardware Partners is required",
        entityType: "Entity Type is required",
        businessName: "Business Name is required",
        businessNameTooLong: "Business Name is too long",
        email: "Email is required",
        emailTooLong: "Email is too long",
        emailInvalid: "Email is invalid",
        url: "URL is required",
        invalidUrl: "Invalid URL",
        duplicateUrl: "Duplicate URL",
        urlScanFailed: "URL scan failed",
        followersMaxLength: "Followers max length exceeded"
      },
      info: {
        businessName: "Only if you are contracting under a business entity; otherwise, leave blank."
      },
      profileLabels: {
        updateAvatar: "Update Avatar"
      },
      profilePictureLabels: {
        title: "Change My Avatar",
        message: "Select an image from your computer (JPEG, PNG or GIF). Image should be square and less than 1MB.",
        avatarRequired: "Please select an image",
        avatarInvalid: "Please select valid image",
        avatarMoreThanLimit: "Image size should be less than 1MB",
        termsAndConditionsFirst: "Respect the rights of others. Only upload images that you own or that you have written permission to freely distribute.",
        termsAndConditionsMiddle: "User Agreement",
        termsAndConditionsLast: "for more information."
      }
  };

  const personalInformationFormProps = {
    infoLabels,
    rules: formsRules.rules(infoLabels),
    accountInformation,
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close",
      ok: "Ok"
    },
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false,
    router: {
      query: {},
      locale: "en-us",
      pathname: "",
      push: jest.fn()
    } as unknown as NextRouter
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Personal Information Form", () => {
    it("shows values in view mode by default", () => {
      renderWithToast(
        <FormWrapper>
          <PersonalInformationForm {...personalInformationFormProps} />
        </FormWrapper>
      );

      expect(screen.getByText("First Name")).toBeInTheDocument();
      expect(screen.getByText(accountInformation.firstName)).toBeInTheDocument();
      expect(screen.getByText("Edit")).toBeInTheDocument();
    });

    it("switches to edit mode and shows input fields", async () => {
      renderWithToast(
        <FormWrapper>
          <PersonalInformationForm {...personalInformationFormProps} />
        </FormWrapper>
      );
      
      await userEvent.click(screen.getByRole("button", { name: /edit/i }));

      expect(await screen.findByPlaceholderText("First Name")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /save/i })).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /save/i })).toBeEnabled();
    });

    it("shows validation error when required fields are empty", async () => {
      render(
        <FormWrapper>
          <PersonalInformationForm {...personalInformationFormProps} />
        </FormWrapper>
      );
      await userEvent.click(screen.getByRole("button", { name: /edit/i }));
      
      await clearValueFor(/First Name/i);
      
      await waitFor(() => {
        expect(screen.getByText("First Name is required")).toBeInTheDocument();
      });
    });

    it("shows toast on successful save", async () => {
      const propsWithSaved = {
        ...personalInformationFormProps,
        isSaved: true
      };
      const { unmount } = renderWithToast(
        <FormWrapper>
          <PersonalInformationForm {...propsWithSaved} />
        </FormWrapper>
      );

      await userEvent.click(screen.getByRole("button", { name: /edit/i }));

      await waitFor(() => {
        expect(screen.getAllByText("Information update successful")[0]).toBeInTheDocument();
        expect(screen.getAllByText("You have successfully updated your Personal Information.")[0]).toBeInTheDocument();
      });
      
      unmount();
    });

    it("displays error if under 18", async () => {
      (isAdult as jest.Mock).mockReturnValue(true);
      renderWithToast(
        <FormWrapper>
          <PersonalInformationForm {...personalInformationFormProps} />
        </FormWrapper>
      );
      await userEvent.click(screen.getByRole("button", { name: /edit/i }));
      const dateInput = screen.getByPlaceholderText(/Date of Birth/i);
      const underageDate = new Date();
      underageDate.setFullYear(underageDate.getFullYear() - 10);
      
      await userEvent.clear(dateInput);
      await userEvent.type(dateInput, underageDate.toISOString().split('T')[0]);

      await waitFor(() => {
        expect(screen.getByText("Age must be 18 years or older")).toBeInTheDocument();
      });
    });
  });
});