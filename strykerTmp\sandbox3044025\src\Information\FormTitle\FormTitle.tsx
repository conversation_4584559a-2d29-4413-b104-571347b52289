// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import React from "react";
type FormTitleProps = {
  title?: string;
  subTitle?: string;
  className?: string;
};
const FormTitle = ({
  title = stryMutAct_9fa48("274") ? "Stryker was here!" : (stryCov_9fa48("274"), ""),
  subTitle = stryMutAct_9fa48("275") ? "Stryker was here!" : (stryCov_9fa48("275"), ""),
  className = stryMutAct_9fa48("276") ? "Stryker was here!" : (stryCov_9fa48("276"), "")
}: FormTitleProps): JSX.Element => {
  if (stryMutAct_9fa48("277")) {
    {}
  } else {
    stryCov_9fa48("277");
    const additionalClasses = className ? className : stryMutAct_9fa48("278") ? "Stryker was here!" : (stryCov_9fa48("278"), "");
    return stryMutAct_9fa48("281") ? title && <h3 className={`heading-title ${additionalClasses}`}>{title}</h3> && subTitle && <h4 className={`heading-subtitle ${additionalClasses}`}>{subTitle}</h4> : stryMutAct_9fa48("280") ? false : stryMutAct_9fa48("279") ? true : (stryCov_9fa48("279", "280", "281"), (stryMutAct_9fa48("283") ? title || <h3 className={`heading-title ${additionalClasses}`}>{title}</h3> : stryMutAct_9fa48("282") ? false : (stryCov_9fa48("282", "283"), title && <h3 className={stryMutAct_9fa48("284") ? `` : (stryCov_9fa48("284"), `heading-title ${additionalClasses}`)}>{title}</h3>)) || (stryMutAct_9fa48("286") ? subTitle || <h4 className={`heading-subtitle ${additionalClasses}`}>{subTitle}</h4> : stryMutAct_9fa48("285") ? false : (stryCov_9fa48("285", "286"), subTitle && <h4 className={stryMutAct_9fa48("287") ? `` : (stryCov_9fa48("287"), `heading-subtitle ${additionalClasses}`)}>{subTitle}</h4>)));
  }
};
export default FormTitle;