import { CreatorProfile } from "@eait-playerexp-cn/creators-http-client";
import { Platform } from "@eait-playerexp-cn/metadata-types";
import { AxiosError } from "axios";

export type ErrorHandling = (dispatch: Dispatch, error: Error | AxiosError) => void;

export type Action = { type: string; data: string | number | boolean | null | object | [] };

export type Dispatch = (action: Action) => void;

export type ValidationError = {
  propertyName?: string;
  errorMessages?: string[] | string;
};

export type FunctionArgumentType = string | number | boolean | null | object | [];
export type CreatorType = "CREATOR" | "INTERESTED_CREATOR";

export type AuthenticatedUser = {
  analyticsId: string | undefined;
  needsMigration?: boolean;
  username: string;
  status?: string;
  avatar?: string;
  tier?: string;
  isPayable?: boolean;
  type?: CreatorType;
  isFlagged?: boolean;
  programs?: string[];
  creatorCode?: string | null;
};

export type State = {
  isValidationError: boolean;
  validationErrors: ValidationError[];
  isError: boolean;
};

type CreatorIdentity = {
  locale: string;
};

export type BrowserAnalytics = {
  updatedBasicInformation: (identity: CreatorIdentity) => void;
  canceledOnboardingFlow?: ({ locale, page }: { page: string; locale: string }) => void;
  confirmedFranchise?: ({ locale, creator }: { creator: Record<string, unknown>; locale: string }) => void;
  confirmedCreatorType?: ({ locale, creator }: { creator: Record<string, unknown>; locale: string }) => void;
  signedTermsAndConditions?: ({ locale, accepted }: { locale: string; accepted: boolean }) => void;
  completedOnboardingFlow?: ({ locale }: { locale: string }) => void;
  startedOnboardingFlow?: ({ locale }: { locale: string }) => void;
  updatedPrimaryFranchise?: ({ locale, creator }: { creator: CreatorProfile; locale: string }) => void;
  updatedSecondaryFranchises?: ({ locale, creator }: { creator: CreatorProfile; locale: string }) => void;
  updatedPrimaryPlatformInProfile?: ({ locale, creator }: { creator: CreatorProfile; locale: string }) => void;
  updatedSecondaryPlatformsInProfile?: ({
    locale,
    creator,
    selectedPlatforms
  }: {
    creator: CreatorProfile;
    locale: string;
    selectedPlatforms: Platform[];
  }) => void;
  confirmedCommunicationPreferences?: ({
    locale,
    contentLanguages
  }: {
    locale: string;
    contentLanguages: string;
  }) => void;
};

export type Layout = {
  main: {
    unhandledError: string;
  };
  toolTip?: {
    badge?: string;
  };
};
