import { act, screen, waitFor, within } from "@testing-library/react";
import InformationPage, { InformationProps } from "./InformationPage";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import {
  aCommunicationPreferencesPayload,
  aConnectedAccountResponse,
  aCreatorCodeResponse,
  aLegalEntityInformationPayload,
  aMailingAddressPayload,
  anAccountInformationResponse,
  anAdditionalInformationPayload,
  aPreferredFranchiseResponse,
  aProgramRequest
} from "@eait-playerexp-cn/creator-test-fixtures";
import { CreatorProfile } from "@eait-playerexp-cn/creators-http-client";
import React from "react";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { renderWithToast, triggerAnimationEnd } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { clearValueFor, enterValueFor, selectOption } from "@src/Helpers/Forms";
import { axe } from "jest-axe";
import Random from "@src/Factories/Random";
import { onToastClose } from "@src/utils";
import { Buttons, InformationPageLabels } from "@src/Translations/InformationPageLabels";
import { aCountry, aHardwarePartner } from "@eait-playerexp-cn/metadata-test-fixtures";

const updateCreator = jest.fn().mockResolvedValue({});
const creatorsServiceInstance = { updateCreator };

jest.mock("@eait-playerexp-cn/creators-http-client", () => {
  return {
    ...jest.requireActual("@eait-playerexp-cn/creators-http-client"),
    CreatorsService: jest.fn().mockImplementation(() => creatorsServiceInstance)
  };
});
jest.mock("@src/utils", () => ({
  ...jest.requireActual("@src/utils"),
  onToastClose: jest.fn()
}));

describe("InformationPage", () => {
  const countries = [aCountry(), aCountry(), aCountry()];
  const accountInformation = anAccountInformationResponse();
  const infoLabels = InformationPageLabels.infoLabels;
  const informationPageProps: InformationProps = {
    creator: {
      preferredSecondaryPlatforms: [],
      preferredSecondaryFranchises: [],
      id: "001VB00000DUe5PYAT",
      creatorTypes: ["YOUTUBER"],
      accountInformation: {
        ...accountInformation,
        dateOfBirth: {
          millisecondsEpoch: ************,
          format: () => "30 July, 1995",
          toDate: () => new Date(************)
        } as unknown as LocalizedDate
      },
      preferredPrimaryPlatform: null,
      preferredPrimaryFranchise: aPreferredFranchiseResponse(),
      communicationPreferences: aCommunicationPreferencesPayload(),
      legalInformation: aLegalEntityInformationPayload(),
      mailingAddress: aMailingAddressPayload(),
      connectedAccounts: [aConnectedAccountResponse()],
      additionalInformation: anAdditionalInformationPayload(),
      socialLinks: [],
      creatorCode: aCreatorCodeResponse(),
      joinedPrograms: ["creator_network", "affiliate", "sims_creator_program"],
      program: aProgramRequest(),
      formattedRegistrationDate: () => ""
    } as unknown as CreatorProfile,
    buttons: Buttons,
    errorHandler: jest.fn(),
    infoLabels,
    user: {
      analyticsId: "xNIEuLMZCvlw+7q2oZaBuG0cTdqHTa2JP3l2eCZ2Sjg=",
      avatar: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png",
      creatorCode: "TestAFFILIATE20250",
      isFlagged: false,
      isPayable: true,
      needsMigration: false,
      programs: ["creator_network", "affiliate", "sims_creator_program"],
      status: "ACTIVE",
      tier: "CREATORNETWORK",
      type: "CREATOR",
      username: "245902"
    },
    updateCreator: jest.fn(),
    hardwarePartners: [aHardwarePartner(), aHardwarePartner(), aHardwarePartner()],
    countries,
    layout: {
      main: {
        unhandledError: "Oops! Something has gone wrong."
      },
      toolTip: {
        badge: "As a former Game Changer you’ve been awarded the Legacy Creator badge!"
      }
    },
    onSaveAccountInformation: jest.fn(),
    allCountries: countries,
    locale: "en-us",
    configuration: {
      defaultAvatarImage: "",
      program: Random.programCode(),
      creatorsClient: jest.fn() as unknown as TraceableHttpClient
    },
    stableDispatch: jest.fn(),
    state: {
      isValidationError: false,
      validationErrors: [],
      isError: false
    }
  };
  const { onSaveAccountInformation } = informationPageProps;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("logs 'Update Basic Information' event when creator updates their basic information", async () => {
    const updatedFirstName = Random.firstName();
    const { unmount } = renderWithToast(<InformationPage {...informationPageProps} />);
    const editButtons = await waitFor(expect4EditButtons);
    await userEvent.click(editButtons[0]);
    const saveButton = await waitFor(expectSaveButton);
    await clearValueFor(/^First Name/i);
    await enterValueFor(/^First Name/i, updatedFirstName);

    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(onSaveAccountInformation).toHaveBeenCalledTimes(1);
      expect(onSaveAccountInformation).toHaveBeenCalledWith({ locale: "en-us" });
      expect(updateCreator).toHaveBeenCalledTimes(1);
      expect(updateCreator).toHaveBeenCalledWith({
        accountInformation: {
          ...informationPageProps.creator.accountInformation,
          dateOfBirth: "1995-07-30",
          firstName: updatedFirstName
        },
        program: { code: informationPageProps.configuration.program }
      });
    });
    unmount();
  });

  it("logs 'Update Basic Information' event when creator updates their mailing address", async () => {
    updateCreator.mockResolvedValue({
      data: informationPageProps.creator
    });
    const { unmount } = renderWithToast(<InformationPage {...informationPageProps} />);
    const editButtons = await waitFor(expect4EditButtons);
    await userEvent.click(editButtons[1]);
    const saveButton = await waitFor(expectSaveButton);
    await clearValueFor(/^Street/i);
    await enterValueFor(/^Street/i, "Main St. 123");

    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(onSaveAccountInformation).toHaveBeenCalledTimes(1);
      expect(onSaveAccountInformation).toHaveBeenCalledWith({ locale: "en-us" });
      expect(updateCreator).toHaveBeenCalledTimes(1);
    });
    unmount(); // Remove toast
  });

  it("logs 'Update Basic Information' event when creator updates their legal entity", async () => {
    updateCreator.mockResolvedValue({
      data: informationPageProps.creator
    });
    const { unmount } = renderWithToast(<InformationPage {...informationPageProps} />);
    const editButtons = await waitFor(expect4EditButtons);
    await userEvent.click(editButtons[2]);
    const saveButton = await waitFor(expectSaveButton);
    await clearValueFor(/^City/i);
    await enterValueFor(/^City/i, "San Antonio");

    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(onSaveAccountInformation).toHaveBeenCalledTimes(1);
      expect(onSaveAccountInformation).toHaveBeenCalledWith({ locale: "en-us" });
      expect(updateCreator).toHaveBeenCalledTimes(1);
    });
    unmount(); // Remove toast
  });

  it("logs 'Update Basic Information' event when creator updates their miscellaneous information", async () => {
    updateCreator.mockResolvedValue({
      data: informationPageProps.creator
    });
    const { unmount, container } = renderWithToast(<InformationPage {...informationPageProps} />);
    const editButtons = await waitFor(expect4EditButtons);
    await userEvent.click(editButtons[3]);
    const saveButton = await waitFor(expectSaveButton);
    await selectOption({ option: "M", container, label: informationPageProps.infoLabels.labels.tShirtSize });

    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(onSaveAccountInformation).toHaveBeenCalledTimes(1);
      expect(onSaveAccountInformation).toHaveBeenCalledWith({ locale: "en-us" });
      expect(updateCreator).toHaveBeenCalledTimes(1);
    });
    unmount(); // Remove toast
  });

  it("closes error toast message on clicking the close button", async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    const errorMessage = "ERROR";
    const informationPropsWithError = {
      ...informationPageProps,
      state: { ...informationPageProps.state, isError: true }
    };
    updateCreator.mockResolvedValue({
      data: informationPageProps.creator
    });
    const { unmount } = renderWithToast(
      <InformationPage {...informationPropsWithError} layout={informationPageProps.layout} />
    );
    const { getByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent("Oops! Something has gone wrong.");
    expect(getByRole("button", { name: /close/i })).toBeInTheDocument();

    await user.click(screen.getByRole("button", { name: /Close/i }));

    triggerAnimationEnd(screen.getByText("Oops! Something has gone wrong."));
    await waitFor(() => {
      expect(onToastClose).toHaveBeenCalledTimes(1);
      expect(onToastClose).toHaveBeenCalledWith(errorMessage, informationPageProps.stableDispatch);
    });
    unmount();
    jest.useRealTimers();
  });

  it("is accessible", async () => {
    let results;
    const { container } = renderWithToast(
      <InformationPage {...informationPageProps} layout={informationPageProps.layout} />
    );
    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  function expect4EditButtons() {
    const editButtons = screen.getAllByRole("button", { name: "Edit" });
    expect(editButtons).toHaveLength(4);
    return editButtons;
  }

  function expectSaveButton() {
    const saveButton = screen.getByRole("button", { name: "Save" });
    expect(saveButton).toBeEnabled();
    return saveButton;
  }
});