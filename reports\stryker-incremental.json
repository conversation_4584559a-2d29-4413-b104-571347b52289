{"files": {"src/Example/Example.tsx": {"language": "typescript", "mutants": [{"id": "0", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/Example/Example.tsx(7,7): error TS2322: Type '({ greeting }: ExampleProps) => void' is not assignable to type 'FC<ExampleProps>'.\n  Type 'void' is not assignable to type 'ReactNode'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["0", "1"], "location": {"end": {"column": 2, "line": 12}, "start": {"column": 53, "line": 6}}}], "source": "import React, { FC } from \"react\";\nimport { Button } from \"@eait-playerexp-cn/core-ui-kit\";\n\nexport type ExampleProps = { greeting: string };\n\nconst Example: FC<ExampleProps> = ({ greeting }) => {\n  return (\n    <div className=\"example-container\">\n      <Button variant=\"primary\">{greeting}</Button>\n    </div>\n  );\n};\n\nexport default Example;\n"}}, "schemaVersion": "1.0", "thresholds": {"high": 90, "low": 70, "break": 100}, "testFiles": {"src/Example/Example.spec.tsx": {"tests": [{"id": "0", "name": "Example shows button with a given greeting message", "location": {"start": {"column": 4, "line": 9}}}, {"id": "1", "name": "Example is accessible", "location": {"start": {"column": 4, "line": 15}}}], "source": "import { render, screen } from \"@testing-library/react\";\nimport React from \"react\";\nimport Example from \"./Example\";\nimport { axe } from \"jest-axe\";\n\ndescribe(\"Example\", () => {\n  const greeting = \"Hello World!\";\n\n  it(\"shows button with a given greeting message\", () => {\n    render(<Example greeting={greeting} />);\n\n    expect(screen.getByRole(\"button\", { name: greeting })).toBeInTheDocument();\n  });\n\n  it(\"is accessible\", async () => {\n    const { container } = render(<Example greeting={greeting} />);\n\n    const results = await axe(container);\n\n    expect(results).toHaveNoViolations();\n  });\n});\n"}}, "projectRoot": "/Users/<USER>/WebstormProjects/cn/component-library", "config": {"$schema": "./node_modules/@stryker-mutator/core/schema/stryker-schema.json", "_comment": "Please see the guide for more information: https://stryker-mutator.io/docs/stryker-js/guides/react", "testRunner": "jest", "allowEmpty": true, "incremental": true, "jest": {"projectType": "custom", "configFile": "jest.config.js", "enableFindRelatedTests": false}, "ignorePatterns": ["reports", "src/index.tx", "src/**/*.stories.{js,jsx,ts,tsx}"], "thresholds": {"high": 90, "low": 70, "break": 100}, "coverageAnalysis": "perTest", "ignoreStatic": true, "reporters": ["progress", "clear-text", "html"], "checkers": ["typescript"], "tsconfigFile": "tsconfig.json", "concurrency": 5, "timeoutMS": 120000, "tempDirName": "strykerTmp", "cleanTempDir": "always", "allowConsoleColors": true, "checkerNodeArgs": [], "commandRunner": {"command": "npm test"}, "clearTextReporter": {"allowColor": true, "allowEmojis": false, "logTests": true, "maxTestsToLog": 3, "reportTests": true, "reportMutants": true, "reportScoreTable": true, "skipFull": false}, "dashboard": {"baseUrl": "https://dashboard.stryker-mutator.io/api/reports", "reportType": "full"}, "dryRunOnly": false, "eventReporter": {"baseDir": "reports/mutation/events"}, "incrementalFile": "reports/stryker-incremental.json", "force": false, "fileLogLevel": "off", "inPlace": false, "logLevel": "info", "maxConcurrentTestRunners": 9007199254740991, "maxTestRunnerReuse": 0, "mutate": ["{src,lib}/**/!(*.+(s|S)pec|*.+(t|T)est).+(cjs|mjs|js|ts|mts|cts|jsx|tsx|html|vue|svelte)", "!{src,lib}/**/__tests__/**/*.+(cjs|mjs|js|ts|mts|cts|jsx|tsx|html|vue|svelte)"], "mutator": {"plugins": null, "excludedMutations": []}, "plugins": ["@stryker-mutator/*"], "appendPlugins": [], "htmlReporter": {"fileName": "reports/mutation/mutation.html"}, "jsonReporter": {"fileName": "reports/mutation/mutation.json"}, "disableTypeChecks": true, "symlinkNodeModules": true, "testRunnerNodeArgs": [], "timeoutFactor": 1.5, "dryRunTimeoutMinutes": 5, "warnings": true, "disableBail": false, "ignorers": [], "typescriptChecker": {"prioritizePerformanceOverAccuracy": true}}, "framework": {"name": "StrykerJS", "version": "8.5.0", "branding": {"homepageUrl": "https://stryker-mutator.io", "imageUrl": "data:image/svg+xml;utf8,%3Csvg viewBox='0 0 1458 1458' xmlns='http://www.w3.org/2000/svg' fill-rule='evenodd' clip-rule='evenodd' stroke-linejoin='round' stroke-miterlimit='2'%3E%3Cpath fill='none' d='M0 0h1458v1458H0z'/%3E%3CclipPath id='a'%3E%3Cpath d='M0 0h1458v1458H0z'/%3E%3C/clipPath%3E%3Cg clip-path='url(%23a)'%3E%3Cpath d='M1458 729c0 402.655-326.345 729-729 729S0 1131.655 0 729C0 326.445 326.345 0 729 0s729 326.345 729 729' fill='%23e74c3c' fill-rule='nonzero'/%3E%3Cpath d='M778.349 1456.15L576.6 1254.401l233-105 85-78.668v-64.332l-257-257-44-187-50-208 251.806-82.793L1076.6 389.401l380.14 379.15c-19.681 367.728-311.914 663.049-678.391 687.599z' fill-opacity='.3'/%3E%3Cpath d='M753.4 329.503c41.79 0 74.579 7.83 97.925 25.444 23.571 18.015 41.69 43.956 55.167 77.097l11.662 28.679 165.733-58.183-14.137-32.13c-26.688-60.655-64.896-108.61-114.191-144.011-49.329-35.423-117.458-54.302-204.859-54.302-50.78 0-95.646 7.376-134.767 21.542-40.093 14.671-74.09 34.79-102.239 60.259-28.84 26.207-50.646 57.06-65.496 92.701-14.718 35.052-22.101 72.538-22.101 112.401 0 72.536 20.667 133.294 61.165 182.704 38.624 47.255 98.346 88.037 179.861 121.291 42.257 17.475 78.715 33.125 109.227 46.994 27.193 12.361 49.294 26.124 66.157 41.751 15.309 14.186 26.497 30.584 33.63 49.258 7.721 20.214 11.16 45.69 11.16 76.402 0 28.021-4.251 51.787-13.591 71.219-8.832 18.374-20.171 33.178-34.523 44.219-14.787 11.374-31.193 19.591-49.393 24.466-19.68 5.359-39.14 7.993-58.69 7.993-29.359 0-54.387-3.407-75.182-10.747-20.112-7.013-37.144-16.144-51.259-27.486-13.618-11.009-24.971-23.766-33.744-38.279-9.64-15.8-17.272-31.924-23.032-48.408l-10.965-31.376-161.669 60.585 10.734 30.124c10.191 28.601 24.197 56.228 42.059 82.748 18.208 27.144 41.322 51.369 69.525 72.745 27.695 21.075 60.904 38.218 99.481 51.041 37.777 12.664 82.004 19.159 132.552 19.159 49.998 0 95.818-8.321 137.611-24.622 42.228-16.471 78.436-38.992 108.835-67.291 30.719-28.597 54.631-62.103 71.834-100.642 17.263-38.56 25.923-79.392 25.923-122.248 0-54.339-8.368-100.37-24.208-138.32-16.29-38.759-38.252-71.661-65.948-98.797-26.965-26.418-58.269-48.835-93.858-67.175-33.655-17.241-69.196-33.11-106.593-47.533-35.934-13.429-65.822-26.601-89.948-39.525-22.153-11.868-40.009-24.21-53.547-37.309-11.429-11.13-19.83-23.678-24.718-37.664-5.413-15.49-7.98-33.423-7.98-53.577 0-40.883 11.293-71.522 37.086-90.539 28.443-20.825 64.985-30.658 109.311-30.658z' fill='%23f1c40f' fill-rule='nonzero'/%3E%3Cpath d='M720 0h18v113h-18zM1458 738v-18h-113v18h113zM720 1345h18v113h-18zM113 738v-18H0v18h113z'/%3E%3C/g%3E%3C/svg%3E"}, "dependencies": {"@stryker-mutator/jest-runner": "8.5.0", "@stryker-mutator/typescript-checker": "8.5.0", "jest": "29.7.0", "react-scripts": "5.0.1", "typescript": "5.5.3", "webpack": "5.95.0", "ts-jest": "29.2.5"}}}