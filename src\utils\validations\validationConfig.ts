export const REGEX_PATTERN = {
  EMAIL:
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
};

const validationConfig = {
  MAXLENGTH: 50,
  MAXLENGTH_20: 20,
  MAXLENGTH_40: 40,
  MAXLENGTH_80: 80,
  MAXLENGTH_255: 255,
  DATE_MAXLENGTH: 10,
  regex: {
    EMAIL: REGEX_PATTERN.EMAIL
  },
  MAX_FILE_SIZE: 524288000
};
export default validationConfig;
