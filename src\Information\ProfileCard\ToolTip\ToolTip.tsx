import React from "react";
import Tooltip from "rc-tooltip";
import { memo, ReactNode } from "react";
import { useDetectScreen } from "../../../utils";
import classNames from "classnames";

type ToolTip = {
  children: ReactNode;
  id?: string;
  directionForDesktop?: string;
  directionForMobile?: string;
  overlay: string | ReactNode;
  classes?: string;
};

export default memo(function ToolTip({
  children,
  id = "tooltip-element",
  directionForDesktop = "top",
  directionForMobile = "bottom",
  overlay,
  classes
}: ToolTip) {
  const isMobile = useDetectScreen(767);

  return (
    <Tooltip
      overlayClassName={classNames("tooltip-overlay-content", classes)}
      placement={!isMobile ? directionForDesktop : directionForMobile}
      trigger={["hover"]}
      overlay={<div>{overlay}</div>}
    >
      <div data-testid={id} className="tooltip-element">
        {children}
      </div>
    </Tooltip>
  );
});
