import { <PERSON>a, StoryObj } from "@storybook/react";
import Example from "./Example";

/**
 * This is an example that includes a button with a label
 */
const meta: Meta<typeof Example> = {
  title: "Component Library/Example",
  component: Example
};

export default meta;

type Story = StoryObj<typeof Example>;

/**
 * Default state with `Hello!` message
 */
export const Default: Story = { args: { greeting: "Hello!" } };
