module.exports = {
  setupFilesAfterEnv: ["<rootDir>/jest.setup.ts"],
  testEnvironment: "jsdom",
  bail: 1,
  verbose: true,
  transform: {
    "^.+\\.tsx?$": "ts-jest"
  },
  testPathIgnorePatterns: ["/node_modules/", ".storybook", "reports"],
  transformIgnorePatterns: ["/node_modules/"],
  collectCoverageFrom: [
    "src/**/*.{js,jsx,ts,tsx}",
    "!src/**/*.stories.{js,jsx,ts,tsx}",
    "!src/index.ts",
    "!**/node_modules/**",
    "!**/*.d.ts"
  ],
  coveragePathIgnorePatterns: ["/node_modules/"],
  coverageDirectory: "reports/coverage",
  coverageReporters: ["text", "text-summary", "html"],
  coverageThreshold: {
    global: {
      statements: 100,
      branches: 100,
      functions: 100,
      lines: 100
    }
  },
  reporters: [
    "default",
    [
      "jest-html-reporters",
      { inlineSource: true, publicPath: "reports/tests", filename: "report.html", enableMergeData: true }
    ]
  ],
  moduleNameMapper: {
    // Force module uuid to resolve with the CJS entry point, because Jest does not support package.json.exports. See https://github.com/uuidjs/uuid/issues/451
    uuid: require.resolve("uuid"),
    "^@src/(.*)$": "<rootDir>/src/$1"
  }
};
