// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import { Controller, useFormContext } from "react-hook-form";
import React, { memo, useCallback, useEffect, useState } from "react";
import ProfileFormAction from "../ProfileFormAction/ProfileFormAction";
import FormTitle from "../FormTitle/FormTitle";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { DateInput, Input } from "@eait-playerexp-cn/core-ui-kit";
import { isAdult } from "../../utils";
import { ButtonLabels, InfoLabels, Overwrite } from "../types";
import { NextRouter } from "next/router";
import { AccountInformationResponse } from "@eait-playerexp-cn/creator-types";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { CreatorFormRules } from "@src/utils/FormRules/CreatorForm";
type AccountInformation = Overwrite<AccountInformationResponse, {
  dateOfBirth: LocalizedDate;
}>;
interface PersonalInformationFormProps {
  infoLabels: InfoLabels;
  rules: CreatorFormRules;
  accountInformation: AccountInformation;
  buttons: ButtonLabels;
  onChange: () => void;
  isSaved: boolean;
  isLoader: boolean;
  router: NextRouter;
}
const PersonalInformationForm = ({
  infoLabels,
  rules,
  accountInformation,
  buttons,
  onChange,
  isSaved = stryMutAct_9fa48("848") ? true : (stryCov_9fa48("848"), false),
  isLoader,
  router
}: PersonalInformationFormProps) => {
  if (stryMutAct_9fa48("849")) {
    {}
  } else {
    stryCov_9fa48("849");
    const methods = useFormContext();
    const {
      control,
      setValue,
      setError
    } = methods;
    const [isEdit, setIsEdit] = useState(stryMutAct_9fa48("850") ? true : (stryCov_9fa48("850"), false));
    const {
      success: successToast
    } = useToast();
    const timeToDisplay = stryMutAct_9fa48("851") ? Math.max(Math.max(infoLabels.success.personalInformation.length * 50, 2000), 7000) : (stryCov_9fa48("851"), Math.min(stryMutAct_9fa48("852") ? Math.min(infoLabels.success.personalInformation.length * 50, 2000) : (stryCov_9fa48("852"), Math.max(stryMutAct_9fa48("853") ? infoLabels.success.personalInformation.length / 50 : (stryCov_9fa48("853"), infoLabels.success.personalInformation.length * 50), 2000)), 7000));
    const onEditChange = useCallback((isChecked: boolean) => {
      if (stryMutAct_9fa48("854")) {
        {}
      } else {
        stryCov_9fa48("854");
        setIsEdit(isChecked);
        if (stryMutAct_9fa48("856") ? false : stryMutAct_9fa48("855") ? true : (stryCov_9fa48("855", "856"), onChange)) onChange();
      }
    }, stryMutAct_9fa48("857") ? [] : (stryCov_9fa48("857"), [isEdit]));
    useEffect(() => {
      if (stryMutAct_9fa48("858")) {
        {}
      } else {
        stryCov_9fa48("858");
        if (stryMutAct_9fa48("861") ? isEdit || isSaved : stryMutAct_9fa48("860") ? false : stryMutAct_9fa48("859") ? true : (stryCov_9fa48("859", "860", "861"), isEdit && isSaved)) setIsEdit(stryMutAct_9fa48("862") ? true : (stryCov_9fa48("862"), false));
      }
    }, stryMutAct_9fa48("863") ? [] : (stryCov_9fa48("863"), [isSaved]));
    return <>
      {stryMutAct_9fa48("866") ? isSaved && isEdit || successToast(<Toast header={infoLabels.success.updatedInformationHeader} content={infoLabels.success.personalInformation} />, {
        autoClose: timeToDisplay
      }) : stryMutAct_9fa48("865") ? false : stryMutAct_9fa48("864") ? true : (stryCov_9fa48("864", "865", "866"), (stryMutAct_9fa48("868") ? isSaved || isEdit : stryMutAct_9fa48("867") ? true : (stryCov_9fa48("867", "868"), isSaved && isEdit)) && successToast(<Toast header={infoLabels.success.updatedInformationHeader} content={infoLabels.success.personalInformation} />, stryMutAct_9fa48("869") ? {} : (stryCov_9fa48("869"), {
        autoClose: timeToDisplay
      })))}
      <div className="personal-information">
        <div className="form-sub-title-and-action">
          <FormTitle subTitle={infoLabels.personalInformation} />
          <ProfileFormAction {...stryMutAct_9fa48("870") ? {} : (stryCov_9fa48("870"), {
            buttons,
            action: onEditChange,
            isSaved,
            isLoader
          })} />
        </div>

        <div className="personal-field-title">{infoLabels.labels.firstName}</div>

        <div className="personal-field">
          {stryMutAct_9fa48("873") ? !isEdit || accountInformation.firstName : stryMutAct_9fa48("872") ? false : stryMutAct_9fa48("871") ? true : (stryCov_9fa48("871", "872", "873"), (stryMutAct_9fa48("874") ? isEdit : (stryCov_9fa48("874"), !isEdit)) && accountInformation.firstName)}
          {stryMutAct_9fa48("877") ? isEdit || <Controller control={control} name="firstName" rules={rules.firstName} defaultValue={accountInformation.firstName} render={({
            field,
            fieldState: {
              error
            }
          }) => <Input id="profile-personal-information-firstName" errorMessage={error && error.message || ""} {...field} placeholder={infoLabels.labels.firstName} ariaLabel={infoLabels.labels.firstName} />} /> : stryMutAct_9fa48("876") ? false : stryMutAct_9fa48("875") ? true : (stryCov_9fa48("875", "876", "877"), isEdit && <Controller control={control} name="firstName" rules={rules.firstName} defaultValue={accountInformation.firstName} render={stryMutAct_9fa48("878") ? () => undefined : (stryCov_9fa48("878"), ({
            field,
            fieldState: {
              error
            }
          }) => <Input id="profile-personal-information-firstName" errorMessage={stryMutAct_9fa48("881") ? error && error.message && "" : stryMutAct_9fa48("880") ? false : stryMutAct_9fa48("879") ? true : (stryCov_9fa48("879", "880", "881"), (stryMutAct_9fa48("883") ? error || error.message : stryMutAct_9fa48("882") ? false : (stryCov_9fa48("882", "883"), error && error.message)) || (stryMutAct_9fa48("884") ? "Stryker was here!" : (stryCov_9fa48("884"), "")))} {...field} placeholder={infoLabels.labels.firstName} ariaLabel={infoLabels.labels.firstName} />)} />)}
        </div>

        <div className="personal-field-title">{infoLabels.labels.lastName}</div>
        <div className="personal-field">
          {stryMutAct_9fa48("887") ? !isEdit || accountInformation.lastName : stryMutAct_9fa48("886") ? false : stryMutAct_9fa48("885") ? true : (stryCov_9fa48("885", "886", "887"), (stryMutAct_9fa48("888") ? isEdit : (stryCov_9fa48("888"), !isEdit)) && accountInformation.lastName)}
          {stryMutAct_9fa48("891") ? isEdit || <Controller control={control} name="lastName" rules={rules.lastName} defaultValue={accountInformation.lastName} render={({
            field,
            fieldState: {
              error
            }
          }) => <Input id="profile-personal-information-lastName" errorMessage={error && error.message || ""} {...field} placeholder={infoLabels.labels.lastName} />} /> : stryMutAct_9fa48("890") ? false : stryMutAct_9fa48("889") ? true : (stryCov_9fa48("889", "890", "891"), isEdit && <Controller control={control} name="lastName" rules={rules.lastName} defaultValue={accountInformation.lastName} render={stryMutAct_9fa48("892") ? () => undefined : (stryCov_9fa48("892"), ({
            field,
            fieldState: {
              error
            }
          }) => <Input id="profile-personal-information-lastName" errorMessage={stryMutAct_9fa48("895") ? error && error.message && "" : stryMutAct_9fa48("894") ? false : stryMutAct_9fa48("893") ? true : (stryCov_9fa48("893", "894", "895"), (stryMutAct_9fa48("897") ? error || error.message : stryMutAct_9fa48("896") ? false : (stryCov_9fa48("896", "897"), error && error.message)) || (stryMutAct_9fa48("898") ? "Stryker was here!" : (stryCov_9fa48("898"), "")))} {...field} placeholder={infoLabels.labels.lastName} />)} />)}
        </div>

        <div className="personal-field-title">{infoLabels.labels.EAID}</div>
        <div className="personal-field">{accountInformation.defaultGamerTag}</div>

        <div className="personal-field-title">{infoLabels.labels.EAEmail}</div>
        <div className="personal-field">{accountInformation.originEmail}</div>

        <div className="personal-field-title">{infoLabels.labels.dateOfBirth}</div>
        <div className="personal-field">
          {stryMutAct_9fa48("901") ? !isEdit || accountInformation.dateOfBirth.format("MMMM D, YYYY", router.locale) : stryMutAct_9fa48("900") ? false : stryMutAct_9fa48("899") ? true : (stryCov_9fa48("899", "900", "901"), (stryMutAct_9fa48("902") ? isEdit : (stryCov_9fa48("902"), !isEdit)) && accountInformation.dateOfBirth.format(stryMutAct_9fa48("903") ? "" : (stryCov_9fa48("903"), "MMMM D, YYYY"), router.locale))}
          {stryMutAct_9fa48("906") ? isEdit || <Controller control={control} name="dateOfBirth" rules={rules.dateOfBirth} defaultValue={accountInformation.dateOfBirth.toDate()} render={({
            field,
            fieldState: {
              error
            }
          }) => <DateInput errorMessage={error && error.message || ""} {...field} placeholder={infoLabels.labels.dateOfBirth} locale={router.locale} maxDate={new Date()} title={infoLabels.header.calendar} cancelText={buttons.cancel} okText={buttons.ok} onCancel={date => {
            if (isAdult(date.toString())) {
              setError("dateOfBirth", {
                type: "manual",
                message: infoLabels.messages.ageMustBe18OrOlder
              }, {
                shouldFocus: true
              });
            } else {
              setError("dateOfBirth", null);
            }
            setValue("dateOfBirth", date);
          }} />} /> : stryMutAct_9fa48("905") ? false : stryMutAct_9fa48("904") ? true : (stryCov_9fa48("904", "905", "906"), isEdit && <Controller control={control} name="dateOfBirth" rules={rules.dateOfBirth} defaultValue={accountInformation.dateOfBirth.toDate()} render={stryMutAct_9fa48("907") ? () => undefined : (stryCov_9fa48("907"), ({
            field,
            fieldState: {
              error
            }
          }) => <DateInput errorMessage={stryMutAct_9fa48("910") ? error && error.message && "" : stryMutAct_9fa48("909") ? false : stryMutAct_9fa48("908") ? true : (stryCov_9fa48("908", "909", "910"), (stryMutAct_9fa48("912") ? error || error.message : stryMutAct_9fa48("911") ? false : (stryCov_9fa48("911", "912"), error && error.message)) || (stryMutAct_9fa48("913") ? "Stryker was here!" : (stryCov_9fa48("913"), "")))} {...field} placeholder={infoLabels.labels.dateOfBirth} locale={router.locale} maxDate={new Date()} title={infoLabels.header.calendar} cancelText={buttons.cancel} okText={buttons.ok} onCancel={date => {
            if (stryMutAct_9fa48("914")) {
              {}
            } else {
              stryCov_9fa48("914");
              if (stryMutAct_9fa48("916") ? false : stryMutAct_9fa48("915") ? true : (stryCov_9fa48("915", "916"), isAdult(date.toString()))) {
                if (stryMutAct_9fa48("917")) {
                  {}
                } else {
                  stryCov_9fa48("917");
                  setError(stryMutAct_9fa48("918") ? "" : (stryCov_9fa48("918"), "dateOfBirth"), stryMutAct_9fa48("919") ? {} : (stryCov_9fa48("919"), {
                    type: stryMutAct_9fa48("920") ? "" : (stryCov_9fa48("920"), "manual"),
                    message: infoLabels.messages.ageMustBe18OrOlder
                  }), stryMutAct_9fa48("921") ? {} : (stryCov_9fa48("921"), {
                    shouldFocus: stryMutAct_9fa48("922") ? false : (stryCov_9fa48("922"), true)
                  }));
                }
              } else {
                if (stryMutAct_9fa48("923")) {
                  {}
                } else {
                  stryCov_9fa48("923");
                  setError(stryMutAct_9fa48("924") ? "" : (stryCov_9fa48("924"), "dateOfBirth"), null);
                }
              }
              setValue(stryMutAct_9fa48("925") ? "" : (stryCov_9fa48("925"), "dateOfBirth"), date);
            }
          }} />)} />)}
        </div>
      </div>
    </>;
  }
};
export default memo(PersonalInformationForm);