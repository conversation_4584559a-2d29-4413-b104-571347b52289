// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import React, { memo, useCallback, useMemo, useRef, useState } from "react";
import { Button, edit, Icon, ModalBody, ModalCloseButton, ModalFooter, ModalHeader, ModalTitle, ModalV2 } from "@eait-playerexp-cn/core-ui-kit";
import { SESSION_USER } from "../../../utils";
import { CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import { AuthenticatedUser, Dispatch } from "../../../utils/types";
import { NextRouter } from "next/router";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { FormDataBody } from "@eait-playerexp-cn/http";
import { ButtonLabels, ProfileLabels, ProfilePictureLabels } from "@src/Information/types";
type Labels = {
  buttons: ButtonLabels;
  profilePictureLabels: ProfilePictureLabels;
  profileLabels: ProfileLabels;
};
type PicturePlaceholderProps = {
  showModal: () => void;
  placeholderRef: (node: HTMLButtonElement | null) => void;
  src: string;
  updateAvatarLabel: string;
};
type FooterButtonsProps = {
  buttons: ButtonLabels;
  onClose: () => void;
  onSave: (event: React.FormEvent) => Promise<void>;
  cancelButtonRef: React.RefObject<HTMLButtonElement>;
  disabled: boolean;
  isLoader: boolean;
};
type ModalBodyContentProps = {
  translate: {
    buttons: ButtonLabels;
    messages: ProfilePictureLabels;
  };
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  selector: string;
  previewSrc: string | null;
  size: number;
  invalidImage: string | null;
  router: NextRouter;
};
type UpdateProfilePictureProps = {
  selector?: string;
  size?: number;
  src: string;
  labels: Labels;
  isPlaceholderDefault?: boolean;
  user: AuthenticatedUser;
  stableDispatch: Dispatch;
  creatorsClient: TraceableHttpClient;
  DEFAULT_AVATAR_IMAGE: string;
  router: NextRouter;
};
type ErrorResponse = {
  response?: {
    status?: number;
    data?: {
      errors?: {
        avatar?: string;
      };
    };
  };
  message?: string;
};
type AvatarProps = {
  showModal: () => void;
  placeholderRef: (node: HTMLButtonElement | null) => void;
  src: string;
  updateAvatarLabel: string;
};
const Avatar = ({
  showModal,
  placeholderRef,
  src,
  updateAvatarLabel
}: AvatarProps) => {
  if (stryMutAct_9fa48("952")) {
    {}
  } else {
    stryCov_9fa48("952");
    return <>
      <div className="profile-card-logo">
        <img src={src} alt="" className="profile-card-avatar" />
      </div>
      <button type="button" className="profile-card-logo-edit" ref={placeholderRef} onClick={showModal} aria-label={updateAvatarLabel}>
        <Icon icon={edit} />
      </button>
    </>;
  }
};
const PicturePlaceholder = ({
  showModal,
  placeholderRef,
  src,
  updateAvatarLabel
}: PicturePlaceholderProps) => {
  if (stryMutAct_9fa48("953")) {
    {}
  } else {
    stryCov_9fa48("953");
    return <section className="update-profile-picture-placeholder" ref={placeholderRef}>
      <Avatar {...stryMutAct_9fa48("954") ? {} : (stryCov_9fa48("954"), {
        showModal,
        placeholderRef,
        src,
        updateAvatarLabel
      })} />
    </section>;
  }
};
const FooterButtons = memo(function FooterButtons({
  buttons: {
    save,
    cancel
  },
  onClose,
  onSave,
  cancelButtonRef,
  disabled,
  isLoader
}: FooterButtonsProps) {
  if (stryMutAct_9fa48("955")) {
    {}
  } else {
    stryCov_9fa48("955");
    return <>
      <Button variant="tertiary" dark size="sm" ref={cancelButtonRef} onClick={onClose}>
        {cancel}
      </Button>
      <Button disabled={disabled} spinner={isLoader} size="sm" type="submit" onClick={onSave}>
        {save}
      </Button>
    </>;
  }
});
const ModalBodyContent = memo(function ModalBodyContent({
  translate,
  onChange,
  selector,
  previewSrc,
  size,
  invalidImage,
  router
}: ModalBodyContentProps) {
  if (stryMutAct_9fa48("956")) {
    {}
  } else {
    stryCov_9fa48("956");
    return <div className="update-profile-picture-container">
      <div className="update-profile-picture-upload-section">
        <div className="update-profile-picture-placeholder-title">{translate.messages.message}</div>
        <button className="btn btn-secondary btn-dark">
          <input type="file" id={selector} name={selector} hidden onChange={onChange} size={size} />
          <label htmlFor={selector} className="update-profile-picture-label">
            {translate.buttons.browse}
          </label>
        </button>
        {stryMutAct_9fa48("959") ? invalidImage || <div className="form-error-message">{invalidImage}</div> : stryMutAct_9fa48("958") ? false : stryMutAct_9fa48("957") ? true : (stryCov_9fa48("957", "958", "959"), invalidImage && <div className="form-error-message">{invalidImage}</div>)}
        <div className="update-profile-picture-placeholder-terms">
          {translate.messages.termsAndConditionsFirst}{stryMutAct_9fa48("960") ? "" : (stryCov_9fa48("960"), " ")}
          <a target="_blank" rel="noreferrer" href={stryMutAct_9fa48("961") ? `` : (stryCov_9fa48("961"), `${(stryMutAct_9fa48("964") ? router.locale !== "en-us" : stryMutAct_9fa48("963") ? false : stryMutAct_9fa48("962") ? true : (stryCov_9fa48("962", "963", "964"), router.locale === (stryMutAct_9fa48("965") ? "" : (stryCov_9fa48("965"), "en-us")))) ? stryMutAct_9fa48("966") ? "" : (stryCov_9fa48("966"), "https://tos.ea.com/legalapp/WEBTERMS/US/en/PC/") : stryMutAct_9fa48("967") ? "" : (stryCov_9fa48("967"), "https://tos.ea.com/legalapp/WEBTERMS/US/ja/PC/")}`)} className="update-profile-picture-agreement-link">
            {translate.messages.termsAndConditionsMiddle}
          </a>{stryMutAct_9fa48("968") ? "" : (stryCov_9fa48("968"), " ")}
          {translate.messages.termsAndConditionsLast}
        </div>
      </div>
      <div className="update-profile-picture-image-placeholder">
        <img src={stryMutAct_9fa48("971") ? `${previewSrc}` && `/img/migrations/default.png` : stryMutAct_9fa48("970") ? false : stryMutAct_9fa48("969") ? true : (stryCov_9fa48("969", "970", "971"), (stryMutAct_9fa48("972") ? `` : (stryCov_9fa48("972"), `${previewSrc}`)) || (stryMutAct_9fa48("973") ? `` : (stryCov_9fa48("973"), `/img/migrations/default.png`)))} className="update-profile-picture-preview" alt="Avatar Preview" />
      </div>
    </div>;
  }
});
const UpdateProfilePicture = ({
  selector = stryMutAct_9fa48("974") ? "" : (stryCov_9fa48("974"), "upload"),
  size = 1048576,
  src,
  labels,
  isPlaceholderDefault = stryMutAct_9fa48("975") ? false : (stryCov_9fa48("975"), true),
  user,
  stableDispatch,
  creatorsClient,
  DEFAULT_AVATAR_IMAGE,
  router
}: UpdateProfilePictureProps): JSX.Element => {
  if (stryMutAct_9fa48("976")) {
    {}
  } else {
    stryCov_9fa48("976");
    const [isShown, setIsShown] = useState<boolean>(stryMutAct_9fa48("977") ? true : (stryCov_9fa48("977"), false));
    const [profilePicturePlaceholder, setProfilePicturePlaceholder] = useState<HTMLButtonElement | null>(null);
    const [previewSrc, setPreviewSrc] = useState<string | null>(null);
    const [invalidImage, setInvalidImage] = useState<string | null>(null);
    const [uploadedSrc, setUploadedSrc] = useState<string>(src);
    const [isFilePicked, setIsFilePicked] = useState<boolean>(stryMutAct_9fa48("978") ? true : (stryCov_9fa48("978"), false));
    const [selectedFile, setSelectedFile] = useState<File | undefined>();
    const {
      translate,
      updateAvatarLabel
    } = useMemo(() => {
      if (stryMutAct_9fa48("979")) {
        {}
      } else {
        stryCov_9fa48("979");
        return stryMutAct_9fa48("980") ? {} : (stryCov_9fa48("980"), {
          translate: stryMutAct_9fa48("981") ? {} : (stryCov_9fa48("981"), {
            buttons: labels.buttons,
            messages: labels.profilePictureLabels
          }),
          updateAvatarLabel: labels.profileLabels.updateAvatar
        });
      }
    }, stryMutAct_9fa48("982") ? [] : (stryCov_9fa48("982"), [labels]));

    // We'll use the actual service without custom typing since it has specific requirements
    const creatorService = useMemo(stryMutAct_9fa48("983") ? () => undefined : (stryCov_9fa48("983"), () => new CreatorsService(creatorsClient, DEFAULT_AVATAR_IMAGE)), stryMutAct_9fa48("984") ? [] : (stryCov_9fa48("984"), [creatorsClient]));
    const showModal = () => {
      if (stryMutAct_9fa48("985")) {
        {}
      } else {
        stryCov_9fa48("985");
        setIsShown(stryMutAct_9fa48("986") ? false : (stryCov_9fa48("986"), true));
        setPreviewSrc(uploadedSrc);
      }
    };
    const [isLoader, setIsLoader] = useState<boolean>(stryMutAct_9fa48("987") ? true : (stryCov_9fa48("987"), false));
    const closeModal = () => {
      if (stryMutAct_9fa48("988")) {
        {}
      } else {
        stryCov_9fa48("988");
        setIsShown(stryMutAct_9fa48("989") ? true : (stryCov_9fa48("989"), false));
        setIsFilePicked(stryMutAct_9fa48("990") ? true : (stryCov_9fa48("990"), false));
        setPreviewSrc(null);
        setInvalidImage(null);
        stryMutAct_9fa48("991") ? profilePicturePlaceholder.focus() : (stryCov_9fa48("991"), profilePicturePlaceholder?.focus());
      }
    };
    const changeHandler = (event: React.ChangeEvent<HTMLInputElement>) => {
      if (stryMutAct_9fa48("992")) {
        {}
      } else {
        stryCov_9fa48("992");
        const file = stryMutAct_9fa48("993") ? event.target.files[0] : (stryCov_9fa48("993"), event.target.files?.[0]);
        if (stryMutAct_9fa48("996") ? file !== undefined : stryMutAct_9fa48("995") ? false : stryMutAct_9fa48("994") ? true : (stryCov_9fa48("994", "995", "996"), file === undefined)) {
          if (stryMutAct_9fa48("997")) {
            {}
          } else {
            stryCov_9fa48("997");
            setInvalidImage(translate.messages.avatarRequired);
            return stryMutAct_9fa48("998") ? true : (stryCov_9fa48("998"), false);
          }
        }
        if (stryMutAct_9fa48("1001") ? false : stryMutAct_9fa48("1000") ? true : stryMutAct_9fa48("999") ? file?.type === "image/jpeg" || file?.type === "image/png" || file?.type === "image/gif" : (stryCov_9fa48("999", "1000", "1001"), !(stryMutAct_9fa48("1004") ? (file?.type === "image/jpeg" || file?.type === "image/png") && file?.type === "image/gif" : stryMutAct_9fa48("1003") ? false : stryMutAct_9fa48("1002") ? true : (stryCov_9fa48("1002", "1003", "1004"), (stryMutAct_9fa48("1006") ? file?.type === "image/jpeg" && file?.type === "image/png" : stryMutAct_9fa48("1005") ? false : (stryCov_9fa48("1005", "1006"), (stryMutAct_9fa48("1008") ? file?.type !== "image/jpeg" : stryMutAct_9fa48("1007") ? false : (stryCov_9fa48("1007", "1008"), (stryMutAct_9fa48("1009") ? file.type : (stryCov_9fa48("1009"), file?.type)) === (stryMutAct_9fa48("1010") ? "" : (stryCov_9fa48("1010"), "image/jpeg")))) || (stryMutAct_9fa48("1012") ? file?.type !== "image/png" : stryMutAct_9fa48("1011") ? false : (stryCov_9fa48("1011", "1012"), (stryMutAct_9fa48("1013") ? file.type : (stryCov_9fa48("1013"), file?.type)) === (stryMutAct_9fa48("1014") ? "" : (stryCov_9fa48("1014"), "image/png")))))) || (stryMutAct_9fa48("1016") ? file?.type !== "image/gif" : stryMutAct_9fa48("1015") ? false : (stryCov_9fa48("1015", "1016"), (stryMutAct_9fa48("1017") ? file.type : (stryCov_9fa48("1017"), file?.type)) === (stryMutAct_9fa48("1018") ? "" : (stryCov_9fa48("1018"), "image/gif")))))))) {
          if (stryMutAct_9fa48("1019")) {
            {}
          } else {
            stryCov_9fa48("1019");
            setInvalidImage(translate.messages.avatarInvalid);
            return stryMutAct_9fa48("1020") ? true : (stryCov_9fa48("1020"), false);
          }
        }
        if (stryMutAct_9fa48("1024") ? file?.size <= size : stryMutAct_9fa48("1023") ? file?.size >= size : stryMutAct_9fa48("1022") ? false : stryMutAct_9fa48("1021") ? true : (stryCov_9fa48("1021", "1022", "1023", "1024"), (stryMutAct_9fa48("1025") ? file.size : (stryCov_9fa48("1025"), file?.size)) > size)) {
          if (stryMutAct_9fa48("1026")) {
            {}
          } else {
            stryCov_9fa48("1026");
            setInvalidImage(translate.messages.avatarMoreThanLimit);
            return stryMutAct_9fa48("1027") ? true : (stryCov_9fa48("1027"), false);
          }
        }
        setInvalidImage(null);
        setSelectedFile(file);
        setPreviewSrc(URL.createObjectURL(file));
        setIsFilePicked(stryMutAct_9fa48("1028") ? false : (stryCov_9fa48("1028"), true));
      }
    };
    const submitHandler = async (event: React.FormEvent) => {
      if (stryMutAct_9fa48("1029")) {
        {}
      } else {
        stryCov_9fa48("1029");
        event.preventDefault();
        setIsLoader(stryMutAct_9fa48("1030") ? false : (stryCov_9fa48("1030"), true));
        if (stryMutAct_9fa48("1033") ? previewSrc === null : stryMutAct_9fa48("1032") ? false : stryMutAct_9fa48("1031") ? true : (stryCov_9fa48("1031", "1032", "1033"), previewSrc !== null)) {
          if (stryMutAct_9fa48("1034")) {
            {}
          } else {
            stryCov_9fa48("1034");
            const formData = new FormData();
            formData.append(stryMutAct_9fa48("1035") ? "" : (stryCov_9fa48("1035"), "avatar"), selectedFile as Blob, selectedFile?.name as string);
            creatorService.updateAvatar(formData as unknown as FormDataBody).then(() => {
              if (stryMutAct_9fa48("1036")) {
                {}
              } else {
                stryCov_9fa48("1036");
                setUploadedSrc(previewSrc);
                setIsLoader(stryMutAct_9fa48("1037") ? true : (stryCov_9fa48("1037"), false));
                closeModal();
                // Update the user prop. So that the new avatar gets rendered across components
                const extension = stryMutAct_9fa48("1038") ? selectedFile.name.split(".").pop() : (stryCov_9fa48("1038"), selectedFile?.name.split(stryMutAct_9fa48("1039") ? "" : (stryCov_9fa48("1039"), ".")).pop());
                user.avatar = user.avatar ? (stryMutAct_9fa48("1041") ? user.avatar.split(".").join(".").slice(0) : stryMutAct_9fa48("1040") ? user.avatar.split(".").slice(0, -1).join(".") : (stryCov_9fa48("1040", "1041"), user.avatar.split(stryMutAct_9fa48("1042") ? "" : (stryCov_9fa48("1042"), ".")).slice(0, stryMutAct_9fa48("1043") ? +1 : (stryCov_9fa48("1043"), -1)).join(stryMutAct_9fa48("1044") ? "" : (stryCov_9fa48("1044"), ".")).slice(0))) + (stryMutAct_9fa48("1045") ? `` : (stryCov_9fa48("1045"), `.${extension}?t=${new Date().getTime()}`)) : null;
                stableDispatch(stryMutAct_9fa48("1046") ? {} : (stryCov_9fa48("1046"), {
                  type: SESSION_USER,
                  data: user
                }));
              }
            }).catch((e: ErrorResponse) => {
              if (stryMutAct_9fa48("1047")) {
                {}
              } else {
                stryCov_9fa48("1047");
                setIsLoader(stryMutAct_9fa48("1048") ? true : (stryCov_9fa48("1048"), false));
                if (stryMutAct_9fa48("1051") ? e?.response?.status !== 422 : stryMutAct_9fa48("1050") ? false : stryMutAct_9fa48("1049") ? true : (stryCov_9fa48("1049", "1050", "1051"), (stryMutAct_9fa48("1053") ? e.response?.status : stryMutAct_9fa48("1052") ? e?.response.status : (stryCov_9fa48("1052", "1053"), e?.response?.status)) === 422)) {
                  if (stryMutAct_9fa48("1054")) {
                    {}
                  } else {
                    stryCov_9fa48("1054");
                    setInvalidImage(e.response.data.errors.avatar);
                  }
                } else {
                  if (stryMutAct_9fa48("1055")) {
                    {}
                  } else {
                    stryCov_9fa48("1055");
                    setInvalidImage(e.message);
                  }
                }
              }
            });
          }
        }
      }
    };
    const isInvalidFormatPicked = useCallback(stryMutAct_9fa48("1056") ? () => undefined : (stryCov_9fa48("1056"), () => stryMutAct_9fa48("1059") ? isFilePicked || !!invalidImage : stryMutAct_9fa48("1058") ? false : stryMutAct_9fa48("1057") ? true : (stryCov_9fa48("1057", "1058", "1059"), isFilePicked && (stryMutAct_9fa48("1060") ? !invalidImage : (stryCov_9fa48("1060"), !(stryMutAct_9fa48("1061") ? invalidImage : (stryCov_9fa48("1061"), !invalidImage)))))), stryMutAct_9fa48("1062") ? [] : (stryCov_9fa48("1062"), [isFilePicked, invalidImage]));
    const cancelButtonRef = useRef<HTMLButtonElement | null>(null);
    return <>
      {stryMutAct_9fa48("1065") ? isPlaceholderDefault || <PicturePlaceholder {...{
        showModal,
        src: uploadedSrc,
        placeholderRef: n => setProfilePicturePlaceholder(n),
        updateAvatarLabel
      }} /> : stryMutAct_9fa48("1064") ? false : stryMutAct_9fa48("1063") ? true : (stryCov_9fa48("1063", "1064", "1065"), isPlaceholderDefault && <PicturePlaceholder {...stryMutAct_9fa48("1066") ? {} : (stryCov_9fa48("1066"), {
        showModal,
        src: uploadedSrc,
        placeholderRef: stryMutAct_9fa48("1067") ? () => undefined : (stryCov_9fa48("1067"), n => setProfilePicturePlaceholder(n)),
        updateAvatarLabel
      })} />)}
      {stryMutAct_9fa48("1070") ? !isPlaceholderDefault || <div className="profile-card-logo-container">
          <Avatar {...{
          showModal,
          src: uploadedSrc,
          placeholderRef: n => setProfilePicturePlaceholder(n),
          updateAvatarLabel
        }} />
        </div> : stryMutAct_9fa48("1069") ? false : stryMutAct_9fa48("1068") ? true : (stryCov_9fa48("1068", "1069", "1070"), (stryMutAct_9fa48("1071") ? isPlaceholderDefault : (stryCov_9fa48("1071"), !isPlaceholderDefault)) && <div className="profile-card-logo-container">
          <Avatar {...stryMutAct_9fa48("1072") ? {} : (stryCov_9fa48("1072"), {
          showModal,
          src: uploadedSrc,
          placeholderRef: stryMutAct_9fa48("1073") ? () => undefined : (stryCov_9fa48("1073"), n => setProfilePicturePlaceholder(n)),
          updateAvatarLabel
        })} />
        </div>)}
      {stryMutAct_9fa48("1076") ? isShown || <ModalV2 closeButtonRef={cancelButtonRef}>
          <ModalHeader>
            <ModalTitle>{translate.messages.title}</ModalTitle>
            <ModalCloseButton ariaLabel={translate.buttons.close} closeButtonRef={cancelButtonRef}></ModalCloseButton>
          </ModalHeader>
          <ModalBody>
            <ModalBodyContent {...{
            selector,
            translate,
            onChange: changeHandler,
            previewSrc,
            size,
            invalidImage,
            router
          }} />
          </ModalBody>
          <ModalFooter showDivider>
            <FooterButtons {...{
            buttons: translate.buttons,
            onClose: closeModal,
            onSave: submitHandler,
            disabled: !isFilePicked || isLoader || isInvalidFormatPicked(),
            cancelButtonRef,
            isLoader
          }} />
          </ModalFooter>
        </ModalV2> : stryMutAct_9fa48("1075") ? false : stryMutAct_9fa48("1074") ? true : (stryCov_9fa48("1074", "1075", "1076"), isShown && <ModalV2 closeButtonRef={cancelButtonRef}>
          <ModalHeader>
            <ModalTitle>{translate.messages.title}</ModalTitle>
            <ModalCloseButton ariaLabel={translate.buttons.close} closeButtonRef={cancelButtonRef}></ModalCloseButton>
          </ModalHeader>
          <ModalBody>
            <ModalBodyContent {...stryMutAct_9fa48("1077") ? {} : (stryCov_9fa48("1077"), {
            selector,
            translate,
            onChange: changeHandler,
            previewSrc,
            size,
            invalidImage,
            router
          })} />
          </ModalBody>
          <ModalFooter showDivider>
            <FooterButtons {...stryMutAct_9fa48("1078") ? {} : (stryCov_9fa48("1078"), {
            buttons: translate.buttons,
            onClose: closeModal,
            onSave: submitHandler,
            disabled: stryMutAct_9fa48("1081") ? (!isFilePicked || isLoader) && isInvalidFormatPicked() : stryMutAct_9fa48("1080") ? false : stryMutAct_9fa48("1079") ? true : (stryCov_9fa48("1079", "1080", "1081"), (stryMutAct_9fa48("1083") ? !isFilePicked && isLoader : stryMutAct_9fa48("1082") ? false : (stryCov_9fa48("1082", "1083"), (stryMutAct_9fa48("1084") ? isFilePicked : (stryCov_9fa48("1084"), !isFilePicked)) || isLoader)) || isInvalidFormatPicked()),
            cancelButtonRef,
            isLoader
          })} />
          </ModalFooter>
        </ModalV2>)}
    </>;
  }
};
export default UpdateProfilePicture;