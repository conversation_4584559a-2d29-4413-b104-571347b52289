import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import Form from "../utils/Form";
import { CreatorProfile, CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import MailingAddressForm from "./MailingAddressForm/MailingAddressForm";
import PersonalInformationForm from "./PersonalInformationForm/PersonalInformationForm";
import MiscellaneousForm from "./MiscellaneousForm/MiscellaneousForm";
import ProfileCard from "./ProfileCard/ProfileCard";
import CreatorForm from "../utils/FormRules/CreatorForm";
import LegalEntityForm from "./LegalEntityForm/LegalEntityForm";
import Loading from "../components/Loading/Loading";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { ERROR, onToastClose, toastContent, useAsync, VALIDATION_ERROR } from "../utils";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { Buttons, InfoLabels, Overwrite } from "./types";
import {
  AccountInformationResponse,
  AdditionalInformationPayload,
  LegalInformationPayload,
  MailingAddressPayload,
  UpdateCreatorRequest
} from "@eait-playerexp-cn/creator-types";
import { AuthenticatedUser, BrowserAnalytics, Dispatch, ErrorHandling, Layout, State } from "@src/utils/types";
import { Country, HardwarePartner } from "@eait-playerexp-cn/metadata-types";
import { NextRouter } from "next/router";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

export type Configuration = {
  creatorsClient: TraceableHttpClient;
  DEFAULT_AVATAR_IMAGE: string;
  PROGRAM_CODE: string;
};

export type AccountInformationWithLocalizedDate = Overwrite<
  AccountInformationResponse,
  {
    dateOfBirth: LocalizedDate;
  }
>;

export type InformationProps = {
  infoLabels: InfoLabels;
  buttons: Buttons;
  user: AuthenticatedUser;
  creator: CreatorProfile;
  updateCreator: (creator: UpdateCreatorRequest) => void;
  hardwarePartners: HardwarePartner[];
  countries: Country[];
  layout: Layout;
  analytics: BrowserAnalytics;
  allCountries: Country[];
  router: NextRouter;
  errorHandler: ErrorHandling;
  configuration: Configuration;
  stableDispatch: Dispatch;
  state: State;
};

export default memo(function Information({
  infoLabels,
  buttons,
  user,
  creator,
  updateCreator,
  hardwarePartners,
  countries,
  layout,
  analytics,
  allCountries,
  router,
  errorHandler,
  configuration,
  stableDispatch,
  state
}: InformationProps) {
  const { creatorsClient, DEFAULT_AVATAR_IMAGE, PROGRAM_CODE } = configuration;
  const {
    main: { unhandledError }
  } = layout;
  const { isValidationError, validationErrors, isError } = state;
  const { error: errorToast } = useToast();
  const [isAccountInformationSaved, setIsAccountInformationSaved] = useState(false);
  const [isMailingAddressSaved, setIsMailingAddressSaved] = useState(false);
  const [isAdditionalInformationSaved, setIsAdditionalInformationSaved] = useState(false);
  const [isLegalEntitySaved, setIsLegalEntitySaved] = useState(false);
  const [accountInformation, setAccountInformation] = useState<AccountInformationWithLocalizedDate>(null);
  const [mailingAddress, setMailingAddress] = useState<MailingAddressPayload>(null);
  const [registrationDate, setRegistrationDate] = useState<string | null>(null);
  const [legalEntity, setLegalEntity] = useState<LegalInformationPayload>(null);
  const [additionalInformation, setAdditionalInformation] = useState<AdditionalInformationPayload>(null);
  const rules = useMemo(() => CreatorForm.rules(infoLabels), [infoLabels]);
  const creatorService = useMemo(() => new CreatorsService(creatorsClient, DEFAULT_AVATAR_IMAGE), [creatorsClient]);

  const submitAccountInformation = useCallback(
    async (data) => {
      try {
        const formData = { ...accountInformation, ...data };
        const updatedAccountInformation = {
          ...formData,
          dateOfBirth: LocalizedDate.format(formData.dateOfBirth, "YYYY-MM-DD")
        };
        await creatorService.updateCreator({
          accountInformation: updatedAccountInformation,
          program: { code: PROGRAM_CODE }
        } as unknown as UpdateCreatorRequest);
        analytics.updatedBasicInformation({ locale: router.locale || "" });
        formData.dateOfBirth = LocalizedDate.fromFormattedDate(formData.dateOfBirth);
        setAccountInformation(formData);
        const updatedCreator = { ...creator, accountInformation: formData };
        updateCreator(updatedCreator as unknown as UpdateCreatorRequest);
        setIsAccountInformationSaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [isAccountInformationSaved, accountInformation, stableDispatch]
  );
  const submitMailingAddress = useCallback(
    async (data) => {
      try {
        const updatedMailingAddress = {
          ...data,
          country: {
            code: data.country.value,
            name: data.country.name
          }
        };
        await creatorService.updateCreator({
          mailingAddress: updatedMailingAddress,
          program: { code: PROGRAM_CODE }
        } as UpdateCreatorRequest);

        analytics.updatedBasicInformation({ locale: router.locale || "" });
        setMailingAddress(data);
        const updatedCreator = { ...creator, mailingAddress: data };
        updateCreator(updatedCreator as unknown as UpdateCreatorRequest);
        setIsMailingAddressSaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [isMailingAddressSaved, stableDispatch]
  );
  const submitAdditionalInformation = useCallback(
    async (data) => {
      try {
        const updatedAdditionalInformation = {
          hardwarePartners: data.hardwarePartners.map((item) => {
            return {
              id: item.value,
              name: item.label
            };
          }),
          hoodieSize: data.hoodieSize.value
        };
        await creatorService.updateCreator({
          additionalInformation: updatedAdditionalInformation,
          program: { code: PROGRAM_CODE }
        } as UpdateCreatorRequest);

        analytics.updatedBasicInformation({ locale: router.locale || "" });
        data.hardwarePartners = data.hardwarePartners.map((item) => {
          return {
            ...item,
            id: item.value,
            name: item.label
          };
        });
        data.hoodieSize = data.hoodieSize.value;
        setAdditionalInformation(data);
        const updatedCreator = { ...creator, additionalInformation: data };
        updateCreator(updatedCreator as unknown as UpdateCreatorRequest);
        setIsAdditionalInformationSaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [isAdditionalInformationSaved, stableDispatch]
  );
  const submitLegalEntity = useCallback(
    async (data) => {
      try {
        const updatedLegalInformation = {
          ...data,
          country: {
            code: data.country.value,
            name: data.country.name
          },
          entityType: data.entityType.value
        };
        await creatorService.updateCreator({
          legalInformation: updatedLegalInformation,
          program: { code: PROGRAM_CODE }
        } as UpdateCreatorRequest);

        analytics.updatedBasicInformation({ locale: router.locale || "" });
        setLegalEntity(data);
        const updatedCreator = { ...creator, legalInformation: data };
        updateCreator(updatedCreator as unknown as UpdateCreatorRequest);
        setIsLegalEntitySaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [isLegalEntitySaved, stableDispatch]
  );
  const { pending: pendingAccUpd, execute: onSubmitAccountInformation } = useAsync(submitAccountInformation, false);
  const { pending: pendingMailAddUpd, execute: onSubmitMailingAddress } = useAsync(submitMailingAddress, false);
  const { pending: pendingAddInfoUpd, execute: onSubmitAdditionalInformation } = useAsync(
    submitAdditionalInformation,
    false
  );
  const { pending: pendingLegAddUpd, execute: onSubmitLegalEntity } = useAsync(submitLegalEntity, false);

  const accountInformationOnChange = useCallback(() => setIsAccountInformationSaved(false), []);
  const mailingAddressOnChange = useCallback(() => setIsMailingAddressSaved(false), []);
  const additionalInformationOnChange = useCallback(() => setIsAdditionalInformationSaved(false), []);
  const legalEntityOnChange = useCallback(() => setIsLegalEntitySaved(false), []);

  useEffect(() => {
    setAccountInformation(creator.accountInformation as unknown as AccountInformationWithLocalizedDate);
    setRegistrationDate(creator.formattedRegistrationDate(router.locale || ""));
    setMailingAddress(creator.mailingAddress as unknown as MailingAddressPayload);
    setAdditionalInformation(creator.additionalInformation as AdditionalInformationPayload);
    setLegalEntity(creator.legalInformation as unknown as LegalInformationPayload);
  }, [creator]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(<Toast header={unhandledError} content={isError ? isError : toastContent(validationErrors)} />, {
        onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
      });
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  const onChangeAsMailingAddress = useCallback(
    (data, isChecked: boolean) => {
      if (isChecked) setLegalEntity({ ...data, ...mailingAddress });
    },
    [mailingAddress]
  );

  return (
    (!legalEntity && (
      <div className="loader">
        <Loading />
      </div>
    )) ||
    (legalEntity && (
      <div className="profile-information">
        {accountInformation && (
          <ProfileCard
            labels={{
              profileLabels: infoLabels.profileLabels,
              profilePictureLabels: infoLabels.profilePictureLabels,
              buttons: buttons,
              creatorSince: infoLabels.creatorSince
            }}
            user={user}
            registrationDate={registrationDate || ""}
            accountInformation={accountInformation}
            data={layout?.toolTip?.badge}
            stableDispatch={stableDispatch}
            DEFAULT_AVATAR_IMAGE={DEFAULT_AVATAR_IMAGE}
            creatorsClient={creatorsClient}
            router={router}
          />
        )}
        {rules && accountInformation && (
          <Form key="personal" mode="onChange" onSubmit={onSubmitAccountInformation}>
            <PersonalInformationForm
              {...{
                infoLabels,
                rules,
                accountInformation,
                buttons,
                onChange: accountInformationOnChange,
                isSaved: isAccountInformationSaved,
                isLoader: pendingAccUpd,
                router
              }}
            />
          </Form>
        )}
        {allCountries && mailingAddress && rules && (
          <Form key="mailing" mode="onChange" onSubmit={onSubmitMailingAddress}>
            <MailingAddressForm
              {...{
                infoLabels,
                rules,
                mailingAddress,
                allCountries,
                buttons,
                onChange: mailingAddressOnChange,
                isSaved: isMailingAddressSaved,
                isLoader: pendingMailAddUpd
              }}
            />
          </Form>
        )}
        {countries && legalEntity && rules && (
          <Form key="legalEntity" mode="onChange" revalidate="onChange" onSubmit={onSubmitLegalEntity}>
            <LegalEntityForm
              {...{
                infoLabels,
                rules,
                legalEntity,
                countries,
                buttons,
                onChangeAsMailingAddress,
                onChange: legalEntityOnChange,
                isSaved: isLegalEntitySaved,
                isLoader: pendingLegAddUpd
              }}
            />
          </Form>
        )}
        {hardwarePartners && additionalInformation && rules && (
          <Form key="miscellaneous" mode="onChange" onSubmit={onSubmitAdditionalInformation}>
            <MiscellaneousForm
              {...{
                infoLabels,
                rules,
                additionalInformation,
                hardwarePartners,
                buttons,
                onChange: additionalInformationOnChange,
                isSaved: isAdditionalInformationSaved,
                isLoader: pendingAddInfoUpd
              }}
            />
          </Form>
        )}
      </div>
    ))
  );
});