import { Controller, useFormContext } from "react-hook-form";
import React, { memo, useCallback, useEffect, useState } from "react";
import { Select } from "@eait-playerexp-cn/core-ui-kit";
import ProfileFormAction from "../ProfileFormAction/ProfileFormAction";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import FormTitle from "../FormTitle/FormTitle";
import { Input } from "@eait-playerexp-cn/core-ui-kit";
import { ButtonLabels, InfoLabels } from "../types";
import { Country, CountryResponse } from "@eait-playerexp-cn/metadata-types";
import { CreatorFormRules } from "@src/utils/FormRules/CreatorForm";

type MailingAddress = {
  state: string;
  street: string;
  city: string;
  zipCode: string;
  country?: CountryResponse;
};

interface MailingAddressFormProps {
  infoLabels: InfoLabels;
  rules: CreatorFormRules;
  mailingAddress: MailingAddress;
  allCountries: Country[];
  buttons: ButtonLabels;
  onChange: () => void;
  isSaved: boolean;
  isLoader: boolean;
}

const MailingAddressForm = ({
  infoLabels,
  rules,
  mailingAddress,
  allCountries,
  buttons,
  onChange,
  isSaved = false,
  isLoader
}: MailingAddressFormProps) => {
  const { control, setValue } = useFormContext();
  const [isEdit, setIsEdit] = useState(false);
  const { success: successToast } = useToast();
  const timeToDisplay = Math.min(Math.max(infoLabels.success.mailingAddress.length * 50, 2000), 7000);
  const onEditChange = useCallback(
    (isChecked) => {
      setIsEdit(isChecked);
      if (onChange) onChange();
    },
    [isEdit]
  );
  const SELECTED_COUNTRY = (mailingAddress.country as unknown as CountryResponse).code
    ? ({
        value: (mailingAddress.country as unknown as CountryResponse).code,
        label: mailingAddress.country.name
      } as Country)
    : (mailingAddress.country as unknown as Country);

  useEffect(() => {
    if (isEdit && isSaved) setIsEdit(false);
  }, [isSaved]);

  useEffect(() => {
    setValue("country", SELECTED_COUNTRY || allCountries[0]);
  }, [allCountries]);

  return (
    <>
      {isSaved &&
        isEdit &&
        successToast(
          <Toast header={infoLabels.success.updatedInformationHeader} content={infoLabels.success.mailingAddress} />,
          {
            autoClose: { timetoDisplay: timeToDisplay }
          }
        )}
      <div className="mailing-information">
        <div className="form-sub-title-and-action">
          <FormTitle subTitle={infoLabels.mailingAddress} />
          <ProfileFormAction {...{ buttons, action: onEditChange, isSaved, isLoader }} />
        </div>

        <div className="mailing-field-title">{infoLabels.labels.street}</div>
        <div className="mailing-field">
          {!isEdit && mailingAddress.street}
          {isEdit && (
            <Controller
              control={control}
              name="street"
              rules={rules.street}
              defaultValue={mailingAddress.street}
              render={({ field, fieldState: { error } }) => (
                <Input
                  id="mailing-address-street"
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  placeholder={infoLabels.labels.street}
                  ariaLabel={infoLabels.labels.street}
                />
              )}
            />
          )}
        </div>

        <div className="mailing-field-title">{infoLabels.labels.city}</div>
        <div className="mailing-field">
          {!isEdit && mailingAddress.city}
          {isEdit && (
            <Controller
              control={control}
              name="city"
              rules={rules.city}
              defaultValue={mailingAddress.city}
              render={({ field, fieldState: { error } }) => (
                <Input
                  id="mailing-address-city"
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  placeholder={infoLabels.labels.city}
                />
              )}
            />
          )}
        </div>

        <div className="mailing-field-title">{infoLabels.labels.country}</div>
        <div className="mailing-field">
          {!isEdit && mailingAddress.country.name}
          {isEdit && (
            <Controller
              control={control}
              name="country"
              rules={rules.country}
              render={({ field, fieldState: { error } }) => (
                <Select
                  id="mailing-country"
                  selectedOption={SELECTED_COUNTRY}
                  errorMessage={error && error.message}
                  options={allCountries}
                  onChange={(item) => {
                    field.onChange(item);
                  }}
                />
              )}
            />
          )}
        </div>

        <div className="mailing-field-title">{infoLabels.labels.state}</div>
        <div className="mailing-field">
          {!isEdit && mailingAddress.state}
          {isEdit && (
            <Controller
              control={control}
              name="state"
              rules={rules.state}
              defaultValue={mailingAddress.state}
              render={({ field, fieldState: { error } }) => (
                <Input
                  id="mailing-address-state"
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  placeholder={infoLabels.labels.state}
                />
              )}
            />
          )}
        </div>

        <div className="mailing-field-title">{infoLabels.labels.zipCode}</div>
        <div className="mailing-field">
          {!isEdit && mailingAddress.zipCode}
          {isEdit && (
            <Controller
              control={control}
              name="zipCode"
              rules={rules.zipCode}
              defaultValue={mailingAddress.zipCode}
              render={({ field, fieldState: { error } }) => (
                <Input
                  id="mailing-address-zipcode"
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  placeholder={infoLabels.labels.zipCode}
                />
              )}
            />
          )}
        </div>
      </div>
    </>
  );
};
export default memo(MailingAddressForm);
