// @ts-nocheck
// 
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import React, { memo, ReactNode } from "react";
import { FieldValues, FormProvider, useForm, ValidationMode } from "react-hook-form";
export type FormProps = {
  children: ReactNode;
  defaultValues?: unknown;
  mode?: keyof ValidationMode;
  onSubmit: (data: FieldValues) => void;
  key?: string;
  revalidate?: string;
};
const Form = memo(function Form({
  children,
  defaultValues = {},
  mode = stryMutAct_9fa48("1309") ? "" : (stryCov_9fa48("1309"), "onSubmit"),
  onSubmit = () => {},
  ...props
}: FormProps) {
  if (stryMutAct_9fa48("1310")) {
    {}
  } else {
    stryCov_9fa48("1310");
    const methods = useForm(stryMutAct_9fa48("1311") ? {} : (stryCov_9fa48("1311"), {
      mode,
      defaultValues,
      ...props
    }));
    const {
      handleSubmit
    } = methods;
    return <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>{children}</form>
    </FormProvider>;
  }
});
export default Form;