import React from "react";

type FormTitleProps = {
  title?: string;
  subTitle?: string;
  className?: string;
};

const FormTitle = ({ title = "", subTitle = "", className = "" }: FormTitleProps): JSX.Element => {
  const additionalClasses = className ? className : "";
  return (
    (title && <h3 className={`heading-title ${additionalClasses}`}>{title}</h3>) ||
    (subTitle && <h4 className={`heading-subtitle ${additionalClasses}`}>{subTitle}</h4>)
  );
};

export default FormTitle;
