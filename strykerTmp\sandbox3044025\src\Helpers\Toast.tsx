// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import { withToastProvider } from "@eait-playerexp-cn/core-ui-kit";
import { act, fireEvent, render, RenderResult } from "@testing-library/react";
import { ReactNode } from "react";
import React from "react";
export const triggerAnimationEnd = (node: Node[] | Node): void => {
  if (stryMutAct_9fa48("263")) {
    {}
  } else {
    stryCov_9fa48("263");
    jest.spyOn(window, stryMutAct_9fa48("264") ? "" : (stryCov_9fa48("264"), "requestAnimationFrame")).mockImplementation(callback => {
      if (stryMutAct_9fa48("265")) {
        {}
      } else {
        stryCov_9fa48("265");
        callback(1);
        return 1;
      }
    });
    act(() => {
      if (stryMutAct_9fa48("266")) {
        {}
      } else {
        stryCov_9fa48("266");
        jest.runAllTimers();
        if (stryMutAct_9fa48("268") ? false : stryMutAct_9fa48("267") ? true : (stryCov_9fa48("267", "268"), Array.isArray(node))) {
          if (stryMutAct_9fa48("269")) {
            {}
          } else {
            stryCov_9fa48("269");
            node.forEach(el => {
              if (stryMutAct_9fa48("270")) {
                {}
              } else {
                stryCov_9fa48("270");
                fireEvent.animationEnd(el?.parentNode as Node);
              }
            });
          }
        } else {
          if (stryMutAct_9fa48("271")) {
            {}
          } else {
            stryCov_9fa48("271");
            fireEvent.animationEnd(node.parentNode as Node);
          }
        }
        jest.runAllTimers();
      }
    });
    (window.requestAnimationFrame as jest.Mock).mockRestore();
  }
};
export const renderWithToast = (component: ReactNode): RenderResult => {
  if (stryMutAct_9fa48("272")) {
    {}
  } else {
    stryCov_9fa48("272");
    const TestWrapper = withToastProvider(stryMutAct_9fa48("273") ? () => undefined : (stryCov_9fa48("273"), () => component));
    return render(<TestWrapper />);
  }
};