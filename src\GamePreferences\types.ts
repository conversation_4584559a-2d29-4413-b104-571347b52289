export interface Platform {
  value: string;
  label: string;
  imageAsIcon?: string;
  selected?: boolean;
}

export interface Franchise {
  value: string;
  label: string;
  image?: string;
}

export interface Creator {
  preferredPrimaryFranchise?: Franchise | null;
  preferredSecondaryFranchises: Franchise[];
  preferredPrimaryPlatform?: Platform | null;
  preferredSecondaryPlatforms: Platform[];
}

export interface FranchisesYouPlayLabels {
  title: string;
  description: string;
  primaryFranchiseTitle: string;
  primaryFranchiseSubTitle: string;
  secondaryFranchiseTitle: string;
  secondaryFranchiseSubTitle: string;
  labels: {
    primaryFranchise: string;
  };
  messages: {
    success: {
      header: string;
      content: string;
    };
  };
}

export interface InfoLabels {
  platformPreferences: string;
  primaryPlatform: string;
  platformPreferencesTitle: string;
  secondaryPlatforms: string;
  secondaryPlatformsTitle: string;
  success: {
    updatedInformationHeader: string;
    platformPreferences: string;
  };
}

export interface Buttons {
  edit: string;
  cancel: string;
  save: string;
  close: string;
}

export interface Layout {
  main: {
    unhandledError: string;
  };
}

export interface Analytics {
  track: (event: string, properties?: Record<string, any>) => void;
}

export interface PlatformPreferencesFormProps {
  infoLabels: InfoLabels;
  buttons: Buttons;
  onChange?: () => void;
  isSaved?: boolean;
  platforms: Platform[];
  preferredPrimaryPlatforms?: Platform | null;
  preferredSecondaryPlatforms: Platform[];
  isLoader?: boolean;
}

export interface FranchiseYouPlayFormProps {
  franchisesYouPlayLabels: FranchisesYouPlayLabels;
  buttons: Buttons;
  onChange?: () => void;
  isSaved?: boolean;
  franchises: Franchise[];
  preferredPrimaryFranchises?: Franchise | null;
  preferredSecondaryFranchises: Franchise[];
  isLoader?: boolean;
}

export interface GamePreferencesPageProps {
  franchisesYouPlayLabels: FranchisesYouPlayLabels;
  infoLabels: InfoLabels;
  buttons: Buttons;
  creator: Creator;
  updateCreator: (creator: Creator) => void;
  franchises: Franchise[];
  platforms: Platform[];
  layout: Layout;
  analytics: Analytics;
}

export interface SecPlatformCardProps {
  platform: Platform;
}

export interface SecFranchiseCardProps {
  franchise: Franchise;
}
