.profile-creator-type {
  @apply font-text-regular text-black;
}
.profile-creator-type-title-actions {
  @apply mb-meas10 border-b border-gray-40 border-opacity-[0.33];
}
.profile-creator-type-heading {
  @apply mb-meas6 font-display-bold xs:text-mobile-h4 md:mb-meas12 md:text-tablet-h4 lg:text-desktop-h4 xl:mb-meas10;
}
.profile-creator-type-title {
  @apply mb-meas6 font-display-bold xs:text-mobile-h4 md:mb-meas0 md:text-tablet-h4 lg:text-desktop-h4 xl:mb-meas4;
}
.profile-creator-type-subtitle {
  @apply mb-meas10  xs:text-mobile-body-large md:mb-meas16 md:text-tablet-body-large lg:text-desktop-body-large xl:mb-meas34;
}
.profile-creator-type-show {
  @apply mt-meas20 pb-meas35 md:pb-meas34 md:pl-meas12 md:pr-meas12 xl:pb-[360px] xl:pl-meas34 xl:pr-meas34;
}
.profile-creator-type-show .card-container {
  @apply flex flex-wrap gap-x-meas10 md:justify-start;
}
.profile-creator-type-readonly .checkmark-box {
  @apply hidden;
}
.profile-creator-type-readonly > .card-container .checkmark-layout.selected-card {
  @apply hidden;
}
.profile-creator-type-readonly {
  @apply opacity-40;
}
.profile-creator-type-show .card-col {
  @apply xl:h-meas40 xl:w-[130px];
}
.profile-creator-type-readonly .card-col {
  @apply pointer-events-none;
}
.profile-creator-type-show .checkmark {
  @apply xl:h-[130px] xl:w-[130px];
}
.profile-creator-type-show .checkbox-title > .icon-block .icon {
  @apply xl:mt-[6px] xl:h-[11.25px]  xl:w-[11.25px];
}
.profile-creator-type-show .card-col .image-as-icon {
  @apply h-[122px] px-meas2 pt-meas16;
}
