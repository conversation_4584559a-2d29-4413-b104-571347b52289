import { render, screen, waitFor } from "@testing-library/react";
import MailingAddressForm, { MailingAddress } from "./MailingAddressForm";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import { aMailingAddressPayload } from "@eait-playerexp-cn/creator-test-fixtures";
import { Country } from "@eait-playerexp-cn/metadata-types";
import { Buttons, InformationPageLabels } from "@src/Translations/InformationPageLabels";
import { aCountry } from "@eait-playerexp-cn/metadata-test-fixtures";

const successToast = jest.fn();
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: successToast
  })
}));

const FormWrapper = ({
  children,
  defaultValues = {} as MailingAddress
}: {
  children: React.ReactNode;
  defaultValues?: MailingAddress;
}) => {
  const methods = useForm({
    defaultValues,
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("MailingAddressForm", () => {
  const mailingAddress = aMailingAddressPayload({
    country: {
      code: "US",
      name: "United States"
    }
  });
  const allCountries: Country[] = [
    aCountry({ value: "US", label: "United States" }),
    aCountry({ value: "Canada", label: "Canada" }),
    aCountry({ value: "India", label: "India" })
  ];
  const infoLabels = InformationPageLabels.infoLabels;
  const props = {
    labels: { infoLabels, buttons: Buttons },
    mailingAddress,
    allCountries,
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show mailing address form correctly", () => {
    render(
      <FormWrapper>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByText(infoLabels.mailingAddress)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.street)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.city)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.country)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.state)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.zipCode)).toBeInTheDocument();
    expect(screen.getByText(mailingAddress.street)).toBeInTheDocument();
    expect(screen.getByText(mailingAddress.city)).toBeInTheDocument();
    expect(screen.getByText(mailingAddress.country.name)).toBeInTheDocument();
    expect(screen.getByText(mailingAddress.state)).toBeInTheDocument();
    expect(screen.getByText(mailingAddress.zipCode)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
  });

  it("should enable edit mode when edit button is clicked", async () => {
    render(
      <FormWrapper>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
    expect(screen.getAllByRole("textbox").length).toBe(4);
    expect(screen.getByDisplayValue(mailingAddress.city)).toBeInTheDocument();
    expect(screen.getByDisplayValue(mailingAddress.street)).toBeInTheDocument();
    expect(screen.getByDisplayValue(mailingAddress.state)).toBeInTheDocument();
    expect(screen.getByDisplayValue(mailingAddress.zipCode)).toBeInTheDocument();
    expect(props.onChange).toHaveBeenCalledTimes(1);
    expect(screen.getByRole("button", { name: "United States" })).toBeInTheDocument();
  });

  it("should exit edit mode when cancel button is clicked", async () => {
    render(
      <FormWrapper>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    await userEvent.click(screen.getByRole("button", { name: "Cancel" }));

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Cancel" })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Save" })).not.toBeInTheDocument();
  });

  it("should exit edit mode when isSaved becomes true", async () => {
    const { rerender } = render(
      <FormWrapper>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();

    const propsWithSaved = { ...props, isSaved: true };
    rerender(
      <FormWrapper>
        <MailingAddressForm {...propsWithSaved} />
      </FormWrapper>
    );

    await waitFor(() => {
      expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    });
  });

  it("should handle country selection change", async () => {
    render(
      <FormWrapper>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    const countrySelect = screen.getByTestId("mailing-country");
    await userEvent.click(countrySelect);
    await userEvent.click(screen.getByText("Canada"));

    expect(screen.getByText("Canada")).toBeInTheDocument();
  });

  it("should handle input field changes in edit mode", async () => {
    render(
      <FormWrapper>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    const streetInput = screen.getByLabelText("Street");
    await userEvent.clear(streetInput);
    await userEvent.type(streetInput, "789 New Street");

    expect(streetInput).toHaveValue("789 New Street");
  });

  it("should show success toast when information is saved", async () => {
    const propsWithSaved = {
      ...props,
      isSaved: true
    };

    const { unmount } = renderWithToast(
      <FormWrapper>
        <MailingAddressForm {...propsWithSaved} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(successToast).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          header: infoLabels.success.updatedInformationHeader,
          content: infoLabels.success.mailingAddress
        })
      }),
      expect.objectContaining({
        autoClose: expect.objectContaining({
          timetoDisplay: expect.any(Number)
        })
      })
    );

    unmount();
  });

  it("should be accessible", async () => {
    const { container } = render(
      <FormWrapper>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    expect(await axe(container)).toHaveNoViolations();
  });

  it("should handle missing onChange prop gracefully", () => {
    const propsWithoutOnChange = {
      ...props,
      onChange: undefined,
      isSaved: undefined,
      mailingAddress: { ...mailingAddress, country: { code: undefined, name: "" } }
    };

    expect(() => {
      render(
        <FormWrapper>
          <MailingAddressForm {...propsWithoutOnChange} />
        </FormWrapper>
      );
    }).not.toThrow();
  });

  it("should handle empty countries array gracefully", () => {
    const propsWithEmptyCountries = {
      ...props,
      allCountries: []
    };

    expect(() => {
      render(
        <FormWrapper>
          <MailingAddressForm {...propsWithEmptyCountries} />
        </FormWrapper>
      );
    }).not.toThrow();
  });

  it("should handle country validation with empty object properties", async () => {
    // Test the specific validation logic: if (!label && !name && !value && isObj(countryVal))
    const emptyCountryObject = {} as any;
    const defaultValues = {
      ...mailingAddress,
      country: emptyCountryObject
    } as MailingAddress;

    render(
      <FormWrapper defaultValues={defaultValues}>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    // The component should handle empty country objects gracefully
    expect(screen.getByText("United States")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
  });

  it("should handle country validation with empty string", async () => {
    // Test the specific validation logic: else if (isString(countryVal) && !countryVal)
    const defaultValues = {
      ...mailingAddress,
      country: "" as any
    } as MailingAddress;

    render(
      <FormWrapper defaultValues={defaultValues}>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    // The component should handle empty string country values gracefully
    expect(screen.getByText("United States")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
  });

  it("should handle error message display with proper fallback", async () => {
    // Test the specific error message logic: errorMessage={(error && error.message) || ""}
    render(
      <FormWrapper>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    // Verify that input fields are rendered with proper error message handling
    const streetInput = screen.getByPlaceholderText(infoLabels.labels.street);
    const cityInput = screen.getByPlaceholderText(infoLabels.labels.city);
    const stateInput = screen.getByPlaceholderText(infoLabels.labels.state);
    const zipCodeInput = screen.getByPlaceholderText(infoLabels.labels.zipCode);

    // These inputs should be present and handle error messages properly
    expect(streetInput).toBeInTheDocument();
    expect(cityInput).toBeInTheDocument();
    expect(stateInput).toBeInTheDocument();
    expect(zipCodeInput).toBeInTheDocument();
  });

  it("should trigger country validation for empty object with no properties", async () => {
    // This test specifically targets: if (!label && !name && !value && isObj(countryVal))
    const emptyCountryObject = {};
    const defaultValues = {
      ...mailingAddress,
      country: emptyCountryObject as any
    };

    const { container } = render(
      <FormWrapper defaultValues={defaultValues}>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    // Clear all required fields to force validation
    const streetInput = screen.getByPlaceholderText(infoLabels.labels.street);
    const cityInput = screen.getByPlaceholderText(infoLabels.labels.city);
    const stateInput = screen.getByPlaceholderText(infoLabels.labels.state);
    const zipCodeInput = screen.getByPlaceholderText(infoLabels.labels.zipCode);

    await userEvent.clear(streetInput);
    await userEvent.clear(cityInput);
    await userEvent.clear(stateInput);
    await userEvent.clear(zipCodeInput);

    // Try to submit - this should trigger all validation including country validation
    const form = container.querySelector('form');
    if (form) {
      await userEvent.click(screen.getByRole("button", { name: "Save" }));
    }

    // The form should still be in edit mode due to validation errors
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
  });

  it("should trigger country validation for empty string", async () => {
    // This test specifically targets: else if (isString(countryVal) && !countryVal)
    const defaultValues = {
      ...mailingAddress,
      country: "" as any
    };

    const { container } = render(
      <FormWrapper defaultValues={defaultValues}>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    // Clear all required fields to force validation
    const streetInput = screen.getByPlaceholderText(infoLabels.labels.street);
    const cityInput = screen.getByPlaceholderText(infoLabels.labels.city);
    const stateInput = screen.getByPlaceholderText(infoLabels.labels.state);
    const zipCodeInput = screen.getByPlaceholderText(infoLabels.labels.zipCode);

    await userEvent.clear(streetInput);
    await userEvent.clear(cityInput);
    await userEvent.clear(stateInput);
    await userEvent.clear(zipCodeInput);

    // Try to submit - this should trigger all validation including country validation
    const form = container.querySelector('form');
    if (form) {
      await userEvent.click(screen.getByRole("button", { name: "Save" }));
    }

    // The form should still be in edit mode due to validation errors
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
  });

  it("should handle validation errors and display error messages correctly", async () => {
    // This test targets the error message handling: errorMessage={(error && error.message) || ""}
    const defaultValues = {
      ...mailingAddress,
      street: "",
      city: "",
      state: "",
      zipCode: "",
      country: {} as any
    };

    const { container } = render(
      <FormWrapper defaultValues={defaultValues}>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    // Try to submit with all empty fields - this should trigger validation
    const form = container.querySelector('form');
    if (form) {
      await userEvent.click(screen.getByRole("button", { name: "Save" }));
    }

    // The form should still be in edit mode due to validation errors
    // This tests that the error message logic is being executed
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
  });
});