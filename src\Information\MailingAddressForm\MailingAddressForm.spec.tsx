import { render, screen, waitFor } from "@testing-library/react";
import MailingAddressForm, { MailingAddress } from "./MailingAddressForm";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import { aMailingAddressPayload } from "@eait-playerexp-cn/creator-test-fixtures";
import { Country } from "@eait-playerexp-cn/metadata-types";
import formsRules from "@src/utils/FormRules/CreatorForm";
import { Buttons, InformationPageLabels } from "@src/Translations/InformationPageLabels";
import Countries from "@src/Factories/Countries";

const successToast = jest.fn();
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: successToast
  })
}));

const FormWrapper = ({
  children,
  defaultValues = {} as MailingAddress
}: {
  children: React.ReactNode;
  defaultValues?: MailingAddress;
}) => {
  const methods = useForm({
    defaultValues,
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("MailingAddressForm", () => {
  const mailingAddress = aMailingAddressPayload({
    country: {
      code: "US",
      name: "United States"
    }
  });
  const allCountries: Country[] = Countries;
  const infoLabels = InformationPageLabels.infoLabels;
  const props = {
    infoLabels,
    rules: formsRules.rules(infoLabels),
    mailingAddress,
    allCountries,
    buttons: Buttons,
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show mailing address form correctly", () => {
    render(
      <FormWrapper>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByText(infoLabels.mailingAddress)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.street)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.city)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.country)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.state)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.zipCode)).toBeInTheDocument();
    expect(screen.getByText(mailingAddress.street)).toBeInTheDocument();
    expect(screen.getByText(mailingAddress.city)).toBeInTheDocument();
    expect(screen.getByText(mailingAddress.country.name)).toBeInTheDocument();
    expect(screen.getByText(mailingAddress.state)).toBeInTheDocument();
    expect(screen.getByText(mailingAddress.zipCode)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
  });

  it("should enable edit mode when edit button is clicked", async () => {
    render(
      <FormWrapper>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
    expect(screen.getAllByRole("textbox").length).toBe(4);
    expect(screen.getByDisplayValue(mailingAddress.city)).toBeInTheDocument();
    expect(screen.getByDisplayValue(mailingAddress.street)).toBeInTheDocument();
    expect(screen.getByDisplayValue(mailingAddress.state)).toBeInTheDocument();
    expect(screen.getByDisplayValue(mailingAddress.zipCode)).toBeInTheDocument();
    expect(props.onChange).toHaveBeenCalledTimes(1);
    expect(screen.getByRole("button", { name: "United States" })).toBeInTheDocument();
  });

  it("should exit edit mode when cancel button is clicked", async () => {
    render(
      <FormWrapper>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    await userEvent.click(screen.getByRole("button", { name: "Cancel" }));

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Cancel" })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Save" })).not.toBeInTheDocument();
  });

  it("should exit edit mode when isSaved becomes true", async () => {
    const { rerender } = render(
      <FormWrapper>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();

    const propsWithSaved = { ...props, isSaved: true };
    rerender(
      <FormWrapper>
        <MailingAddressForm {...propsWithSaved} />
      </FormWrapper>
    );

    await waitFor(() => {
      expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    });
  });

  it("should handle country selection change", async () => {
    render(
      <FormWrapper>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    const countrySelect = screen.getByTestId("mailing-country");
    await userEvent.click(countrySelect);
    await userEvent.click(screen.getByText("Canada"));

    expect(screen.getByText("Canada")).toBeInTheDocument();
  });

  it("should handle input field changes in edit mode", async () => {
    render(
      <FormWrapper>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    const streetInput = screen.getByLabelText("Street");
    await userEvent.clear(streetInput);
    await userEvent.type(streetInput, "789 New Street");

    expect(streetInput).toHaveValue("789 New Street");
  });

  it("should show success toast when information is saved", async () => {
    const propsWithSaved = {
      ...props,
      isSaved: true
    };

    const { unmount } = renderWithToast(
      <FormWrapper>
        <MailingAddressForm {...propsWithSaved} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(successToast).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          header: infoLabels.success.updatedInformationHeader,
          content: infoLabels.success.mailingAddress
        })
      }),
      expect.objectContaining({
        autoClose: expect.objectContaining({
          timetoDisplay: expect.any(Number)
        })
      })
    );

    unmount();
  });

  it("should be accessible", async () => {
    const { container } = render(
      <FormWrapper>
        <MailingAddressForm {...props} />
      </FormWrapper>
    );

    expect(await axe(container)).toHaveNoViolations();
  });

  it("should handle missing onChange prop gracefully", () => {
    const propsWithoutOnChange = {
      ...props,
      onChange: undefined,
      isSaved: undefined,
      mailingAddress: { ...mailingAddress, country: { code: undefined, name: "" } }
    };

    expect(() => {
      render(
        <FormWrapper>
          <MailingAddressForm {...propsWithoutOnChange} />
        </FormWrapper>
      );
    }).not.toThrow();
  });

  it("should handle empty countries array gracefully", () => {
    const propsWithEmptyCountries = {
      ...props,
      allCountries: []
    };

    expect(() => {
      render(
        <FormWrapper>
          <MailingAddressForm {...propsWithEmptyCountries} />
        </FormWrapper>
      );
    }).not.toThrow();
  });
});