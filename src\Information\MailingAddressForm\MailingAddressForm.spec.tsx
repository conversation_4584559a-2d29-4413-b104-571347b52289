import { act, render, screen, waitFor } from "@testing-library/react";
import MailingAddressForm from "./MailingAddressForm";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import { aMailingAddressPayload } from "@eait-playerexp-cn/creator-test-fixtures";
import { MailingAddressPayload } from "@eait-playerexp-cn/creator-types";
import { Country } from "@eait-playerexp-cn/metadata-types";
import formsRules from "@src/utils/FormRules/CreatorForm";
import { InformationPageLabels } from "@src/Translations/InformationPageLabels";

const successToast = jest.fn();
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: successToast
  })
}));

const MockFormProvider = ({ children, defaultValues = {} }: { children: React.ReactNode; defaultValues?: any }) => {
  const methods = useForm({
    defaultValues: {
      street: defaultValues.street || "",
      city: defaultValues.city || "",
      country: defaultValues.country || null,
      state: defaultValues.state || "",
      zipCode: defaultValues.zipCode || "",
      ...defaultValues
    },
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("MailingAddressForm - Tests for mailing address form functionality including rendering, edit mode, address fields, country selection, validation, toast messages, accessibility, and error handling", () => {
  const mailingAddress: MailingAddressPayload = {
    ...aMailingAddressPayload(),
    street: "456 Oak Avenue",
    city: "Sample City",
    country: {
      code: "US",
      name: "United States"
    },
    state: "New York",
    zipCode: "10001"
  };

  const mockCountries: Country[] = [
    {
      value: "US",
      label: "United States",
      name: "United States"
    },
    {
      value: "CA",
      label: "Canada",
      name: "Canada"
    },
    {
      value: "UK",
      label: "United Kingdom",
      name: "United Kingdom"
    }
  ];

  const infoLabels = InformationPageLabels.infoLabels;

  const mockProps = {
    infoLabels,
    rules: formsRules.rules(infoLabels),
    mailingAddress,
    allCountries: mockCountries,
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close",
      browse: "Browse"
    },
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render mailing address form correctly", () => {
    render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    expect(screen.getByText(infoLabels.mailingAddress)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.street)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.city)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.country)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.state)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.zipCode)).toBeInTheDocument();
  });

  it("should display address fields in read-only mode initially", () => {
    render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    expect(screen.getByText("456 Oak Avenue")).toBeInTheDocument();
    expect(screen.getByText("Sample City")).toBeInTheDocument();
    expect(screen.getByText("United States")).toBeInTheDocument();
    expect(screen.getByText("New York")).toBeInTheDocument();
    expect(screen.getByText("10001")).toBeInTheDocument();
  });

  it("should show edit button initially", () => {
    render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
  });

  it("should enable edit mode when edit button is clicked", async () => {
    render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
  });

  it("should show form inputs in edit mode", async () => {
    render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByPlaceholderText(infoLabels.labels.street)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(infoLabels.labels.city)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(infoLabels.labels.state)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(infoLabels.labels.zipCode)).toBeInTheDocument();
  });

  it("should call onChange when edit mode is toggled", async () => {
    render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(mockProps.onChange).toHaveBeenCalledTimes(1);
  });

  it("should exit edit mode when cancel button is clicked", async () => {
    render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    await userEvent.click(cancelButton);

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Cancel" })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Save" })).not.toBeInTheDocument();
  });

  it("should exit edit mode when isSaved becomes true", async () => {
    const { rerender } = render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();

    const propsWithSaved = { ...mockProps, isSaved: true };
    rerender(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...propsWithSaved} />
      </MockFormProvider>
    );

    await waitFor(() => {
      expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    });
  });

  it("should display country select in edit mode", async () => {
    render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const countrySelect = screen.getByRole("button", { name: "United States" });
    expect(countrySelect).toBeInTheDocument();
  });

  it("should handle country selection change", async () => {
    render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const countrySelect = screen.getByRole("button", { name: "United States" });
    await userEvent.click(countrySelect);
    await userEvent.click(screen.getByText("Canada"));

    expect(screen.getByText("Canada")).toBeInTheDocument();
  });

  it("should handle input field changes in edit mode", async () => {
    render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const streetInput = screen.getByPlaceholderText(infoLabels.labels.street);
    await userEvent.clear(streetInput);
    await userEvent.type(streetInput, "789 New Street");

    expect(streetInput).toHaveValue("789 New Street");
  });

  it("should show default values in form inputs when in edit mode", async () => {
    render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByDisplayValue("456 Oak Avenue")).toBeInTheDocument();
    expect(screen.getByDisplayValue("Sample City")).toBeInTheDocument();
    expect(screen.getByDisplayValue("New York")).toBeInTheDocument();
    expect(screen.getByDisplayValue("10001")).toBeInTheDocument();
  });

  it("should show success toast when information is saved", async () => {
    const propsWithSaved = {
      ...mockProps,
      isSaved: true
    };

    const { unmount } = renderWithToast(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...propsWithSaved} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(successToast).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          header: infoLabels.success.updatedInformationHeader,
          content: infoLabels.success.mailingAddress
        })
      }),
      expect.objectContaining({
        autoClose: expect.objectContaining({
          timetoDisplay: expect.any(Number)
        })
      })
    );

    unmount();
  });

  it("should calculate toast display time based on message length", async () => {
    const longMessage = "A".repeat(200);
    const propsWithLongMessage = {
      ...mockProps,
      isSaved: true,
      infoLabels: {
        ...mockProps.infoLabels,
        success: {
          ...mockProps.infoLabels.success,
          mailingAddress: longMessage
        }
      }
    };

    const { unmount } = renderWithToast(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...propsWithLongMessage} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(successToast).toHaveBeenCalledWith(
      expect.anything(),
      expect.objectContaining({
        autoClose: expect.objectContaining({
          timetoDisplay: 7000
        })
      })
    );

    unmount();
  });

  it("should show loading state when isLoader is true", () => {
    const propsWithLoader = {
      ...mockProps,
      isLoader: true
    };

    render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...propsWithLoader} />
      </MockFormProvider>
    );

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
  });

  it("should be accessible", async () => {
    let results: any;
    const { container } = render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  it("should have proper aria-label for input fields", async () => {
    render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const streetInput = screen.getByPlaceholderText(infoLabels.labels.street);
    expect(streetInput).toHaveAttribute("aria-label", infoLabels.labels.street);
  });

  it("should handle missing onChange prop gracefully", () => {
    const propsWithoutOnChange = {
      ...mockProps,
      onChange: undefined
    };

    expect(() => {
      render(
        <MockFormProvider defaultValues={mailingAddress}>
          <MailingAddressForm {...propsWithoutOnChange} />
        </MockFormProvider>
      );
    }).not.toThrow();
  });

  it("should handle empty countries array gracefully", () => {
    const propsWithEmptyCountries = {
      ...mockProps,
      allCountries: []
    };

    expect(() => {
      render(
        <MockFormProvider defaultValues={mailingAddress}>
          <MailingAddressForm {...propsWithEmptyCountries} />
        </MockFormProvider>
      );
    }).not.toThrow();
  });

  it("should handle null/undefined mailing address properties gracefully", () => {
    const propsWithNullValues = {
      ...mockProps,
      mailingAddress: {
        ...mailingAddress,
        street: "",
        city: "",
        state: "",
        zipCode: ""
      }
    };

    expect(() => {
      render(
        <MockFormProvider>
          <MailingAddressForm {...propsWithNullValues} />
        </MockFormProvider>
      );
    }).not.toThrow();
  });

  it("should update default values when mailingAddress changes", () => {
    const { rerender } = render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    const updatedMailingAddress = {
      ...mailingAddress,
      street: "999 Updated Street",
      city: "Updated City"
    };

    const updatedProps = {
      ...mockProps,
      mailingAddress: updatedMailingAddress
    };

    rerender(
      <MockFormProvider defaultValues={updatedMailingAddress}>
        <MailingAddressForm {...updatedProps} />
      </MockFormProvider>
    );

    expect(screen.getByText("999 Updated Street")).toBeInTheDocument();
    expect(screen.getByText("Updated City")).toBeInTheDocument();
    expect(screen.queryByText("456 Oak Avenue")).not.toBeInTheDocument();
  });

  it("should be memoized and not re-render unnecessarily", () => {
    const { rerender } = render(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    rerender(
      <MockFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...mockProps} />
      </MockFormProvider>
    );

    expect(screen.getByText(infoLabels.mailingAddress)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.street)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.city)).toBeInTheDocument();
  });
});