import { act, render, screen, waitFor } from "@testing-library/react";
import MailingAddressForm from "./MailingAddressForm";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import { aMailingAddressPayload } from "@eait-playerexp-cn/creator-test-fixtures";
import { Country } from "@eait-playerexp-cn/metadata-types";
import formsRules from "@src/utils/FormRules/CreatorForm";
import { InformationPageLabels } from "@src/Translations/InformationPageLabels";

const successToast = jest.fn();
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: successToast
  })
}));

const TestFormProvider = ({ children, defaultValues = {} }: { children: React.ReactNode; defaultValues?: any }) => {
  const methods = useForm({
    defaultValues: {
      street: defaultValues.street || "",
      city: defaultValues.city || "",
      country: defaultValues.country || null,
      state: defaultValues.state || "",
      zipCode: defaultValues.zipCode || "",
      ...defaultValues
    },
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("MailingAddressForm - Tests for mailing address form functionality including rendering, edit mode, address fields, country selection, validation, toast messages, accessibility, and error handling", () => {
  const mailingAddress = {
    ...aMailingAddressPayload(),
    street: "456 Oak Avenue",
    city: "Sample City",
    country: {
      code: "US",
      name: "United States"
    },
    state: "New York",
    zipCode: "10001"
  };

  const allCountries: Country[] = [
    {
      value: "US",
      label: "United States",
      name: "United States"
    },
    {
      value: "CA",
      label: "Canada",
      name: "Canada"
    },
    {
      value: "UK",
      label: "United Kingdom",
      name: "United Kingdom"
    }
  ];

  const infoLabels = InformationPageLabels.infoLabels;

  const props = {
    infoLabels,
    rules: formsRules.rules(infoLabels),
    mailingAddress,
    allCountries,
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close",
      browse: "Browse"
    },
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show mailing address form correctly", () => {
    render(
      <TestFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...props} />
      </TestFormProvider>
    );

    expect(screen.getByText(infoLabels.mailingAddress)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.street)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.city)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.country)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.state)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.zipCode)).toBeInTheDocument();
  });

  it("should display address fields in read-only mode initially", () => {
    render(
      <TestFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...props} />
      </TestFormProvider>
    );

    expect(screen.getByText("456 Oak Avenue")).toBeInTheDocument();
    expect(screen.getByText("Sample City")).toBeInTheDocument();
    expect(screen.getByText("United States")).toBeInTheDocument();
    expect(screen.getByText("New York")).toBeInTheDocument();
    expect(screen.getByText("10001")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
  });

  it("should enable edit mode when edit button is clicked", async () => {
    render(
      <TestFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...props} />
      </TestFormProvider>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
    expect(screen.getByDisplayValue("456 Oak Avenue")).toBeInTheDocument();
    expect(screen.getByDisplayValue("Sample City")).toBeInTheDocument();
    expect(screen.getByDisplayValue("New York")).toBeInTheDocument();
    expect(screen.getByDisplayValue("10001")).toBeInTheDocument();
    expect(props.onChange).toHaveBeenCalledTimes(1);
    expect(screen.getByRole("button", { name: "United States" })).toBeInTheDocument();
  });

  it("should exit edit mode when cancel button is clicked", async () => {
    render(
      <TestFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...props} />
      </TestFormProvider>
    );
    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    await userEvent.click(screen.getByRole("button", { name: "Cancel" }));

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Cancel" })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Save" })).not.toBeInTheDocument();
  });

  it("should exit edit mode when isSaved becomes true", async () => {
    const { rerender } = render(
      <TestFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...props} />
      </TestFormProvider>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();

    const propsWithSaved = { ...props, isSaved: true };
    rerender(
      <TestFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...propsWithSaved} />
      </TestFormProvider>
    );

    await waitFor(() => {
      expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    });
  });

  it("should handle country selection change", async () => {
    render(
      <TestFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...props} />
      </TestFormProvider>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    const countrySelect = screen.getByTestId("mailing-country");
    await userEvent.click(countrySelect);
    await userEvent.click(screen.getByText("Canada"));

    expect(screen.getByText("Canada")).toBeInTheDocument();
  });

  it("should handle input field changes in edit mode", async () => {
    render(
      <TestFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...props} />
      </TestFormProvider>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    const streetInput = screen.getByLabelText("Street");
    await userEvent.clear(streetInput);
    await userEvent.type(streetInput, "789 New Street");

    expect(streetInput).toHaveValue("789 New Street");
  });

  it("should show success toast when information is saved", async () => {
    const propsWithSaved = {
      ...props,
      isSaved: true
    };

    const { unmount } = renderWithToast(
      <TestFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...propsWithSaved} />
      </TestFormProvider>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(successToast).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          header: infoLabels.success.updatedInformationHeader,
          content: infoLabels.success.mailingAddress
        })
      }),
      expect.objectContaining({
        autoClose: expect.objectContaining({
          timetoDisplay: expect.any(Number)
        })
      })
    );

    unmount();
  });

  it("should be accessible", async () => {
    let results: any;
    const { container } = render(
      <TestFormProvider defaultValues={mailingAddress}>
        <MailingAddressForm {...props} />
      </TestFormProvider>
    );

    results = await axe(container);

    expect(results).toHaveNoViolations();
  });

  it("should handle missing onChange prop gracefully", () => {
    const propsWithoutOnChange = {
      ...props,
      onChange: undefined,
      isSaved: undefined,
      mailingAddress: { ...mailingAddress, country: { code: undefined, name: "" } }
    };

    expect(() => {
      render(
        <TestFormProvider defaultValues={mailingAddress}>
          <MailingAddressForm {...propsWithoutOnChange} />
        </TestFormProvider>
      );
    }).not.toThrow();
  });

  it("should handle empty countries array gracefully", () => {
    const propsWithEmptyCountries = {
      ...props,
      allCountries: []
    };

    expect(() => {
      render(
        <TestFormProvider defaultValues={mailingAddress}>
          <MailingAddressForm {...propsWithEmptyCountries} />
        </TestFormProvider>
      );
    }).not.toThrow();
  });
});