// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import { useCallback, useEffect, useRef, useState } from "react";
import { debounce } from "./common";

//-------------------------------------
// Custom hook to determine screen
//-------------------------------------
export const useDetectScreen = (width: number): boolean => {
  if (stryMutAct_9fa48("1411")) {
    {}
  } else {
    stryCov_9fa48("1411");
    const [widthAchieved, setWidthAchieved] = useState(stryMutAct_9fa48("1412") ? true : (stryCov_9fa48("1412"), false));
    const updateTarget = (e: MediaQueryListEvent) => {
      if (stryMutAct_9fa48("1413")) {
        {}
      } else {
        stryCov_9fa48("1413");
        if (stryMutAct_9fa48("1415") ? false : stryMutAct_9fa48("1414") ? true : (stryCov_9fa48("1414", "1415"), e.matches)) {
          if (stryMutAct_9fa48("1416")) {
            {}
          } else {
            stryCov_9fa48("1416");
            setWidthAchieved(stryMutAct_9fa48("1417") ? false : (stryCov_9fa48("1417"), true));
          }
        } else {
          if (stryMutAct_9fa48("1418")) {
            {}
          } else {
            stryCov_9fa48("1418");
            setWidthAchieved(stryMutAct_9fa48("1419") ? true : (stryCov_9fa48("1419"), false));
          }
        }
      }
    };
    const handler = debounce(updateTarget, 100); // debounce will ensure one execution

    useEffect(() => {
      if (stryMutAct_9fa48("1420")) {
        {}
      } else {
        stryCov_9fa48("1420");
        if (stryMutAct_9fa48("1422") ? false : stryMutAct_9fa48("1421") ? true : (stryCov_9fa48("1421", "1422"), window)) {
          if (stryMutAct_9fa48("1423")) {
            {}
          } else {
            stryCov_9fa48("1423");
            const media = window.matchMedia(stryMutAct_9fa48("1424") ? `` : (stryCov_9fa48("1424"), `(max-width: ${width}px)`));
            media.addEventListener(stryMutAct_9fa48("1425") ? "" : (stryCov_9fa48("1425"), "change"), handler);
            if (stryMutAct_9fa48("1427") ? false : stryMutAct_9fa48("1426") ? true : (stryCov_9fa48("1426", "1427"), media.matches)) setWidthAchieved(stryMutAct_9fa48("1428") ? false : (stryCov_9fa48("1428"), true));
            return stryMutAct_9fa48("1429") ? () => undefined : (stryCov_9fa48("1429"), () => media.removeEventListener(stryMutAct_9fa48("1430") ? "" : (stryCov_9fa48("1430"), "change"), handler));
          }
        }
      }
    }, stryMutAct_9fa48("1431") ? ["Stryker was here"] : (stryCov_9fa48("1431"), []));
    return widthAchieved;
  }
};
export function useIsMounted(): () => boolean {
  if (stryMutAct_9fa48("1432")) {
    {}
  } else {
    stryCov_9fa48("1432");
    const isMounted = useRef<boolean>(stryMutAct_9fa48("1433") ? true : (stryCov_9fa48("1433"), false));
    useEffect(() => {
      if (stryMutAct_9fa48("1434")) {
        {}
      } else {
        stryCov_9fa48("1434");
        isMounted.current = stryMutAct_9fa48("1435") ? false : (stryCov_9fa48("1435"), true);
        return () => {
          if (stryMutAct_9fa48("1436")) {
            {}
          } else {
            stryCov_9fa48("1436");
            isMounted.current = stryMutAct_9fa48("1437") ? true : (stryCov_9fa48("1437"), false);
          }
        };
      }
    }, stryMutAct_9fa48("1438") ? ["Stryker was here"] : (stryCov_9fa48("1438"), []));
    return useCallback(stryMutAct_9fa48("1439") ? () => undefined : (stryCov_9fa48("1439"), () => isMounted.current), stryMutAct_9fa48("1440") ? ["Stryker was here"] : (stryCov_9fa48("1440"), []));
  }
}
interface UseScriptParams {
  url?: string;
  html?: string | (() => string);
  node?: Document | HTMLElement | undefined;
  type?: string;
  async?: boolean;
  defer?: boolean;
  headScript?: boolean;
  callback?: () => void;
}
export function useScript({
  url = stryMutAct_9fa48("1441") ? "Stryker was here!" : (stryCov_9fa48("1441"), ""),
  html = stryMutAct_9fa48("1442") ? "Stryker was here!" : (stryCov_9fa48("1442"), ""),
  node = document,
  type = stryMutAct_9fa48("1443") ? "" : (stryCov_9fa48("1443"), "text/javascript"),
  async = stryMutAct_9fa48("1444") ? true : (stryCov_9fa48("1444"), false),
  defer = stryMutAct_9fa48("1445") ? true : (stryCov_9fa48("1445"), false),
  headScript = stryMutAct_9fa48("1446") ? true : (stryCov_9fa48("1446"), false),
  callback
}: UseScriptParams): Document | HTMLElement | undefined {
  if (stryMutAct_9fa48("1447")) {
    {}
  } else {
    stryCov_9fa48("1447");
    useEffect(() => {
      if (stryMutAct_9fa48("1448")) {
        {}
      } else {
        stryCov_9fa48("1448");
        const htmlVal = (stryMutAct_9fa48("1451") ? typeof html !== "function" : stryMutAct_9fa48("1450") ? false : stryMutAct_9fa48("1449") ? true : (stryCov_9fa48("1449", "1450", "1451"), typeof html === (stryMutAct_9fa48("1452") ? "" : (stryCov_9fa48("1452"), "function")))) ? html() : html;
        const appendTo = stryMutAct_9fa48("1455") ? node && document : stryMutAct_9fa48("1454") ? false : stryMutAct_9fa48("1453") ? true : (stryCov_9fa48("1453", "1454", "1455"), node || document);
        if (stryMutAct_9fa48("1458") ? appendTo || url || htmlVal : stryMutAct_9fa48("1457") ? false : stryMutAct_9fa48("1456") ? true : (stryCov_9fa48("1456", "1457", "1458"), appendTo && (stryMutAct_9fa48("1460") ? url && htmlVal : stryMutAct_9fa48("1459") ? true : (stryCov_9fa48("1459", "1460"), url || htmlVal)))) {
          if (stryMutAct_9fa48("1461")) {
            {}
          } else {
            stryCov_9fa48("1461");
            const script = document.createElement(stryMutAct_9fa48("1462") ? "" : (stryCov_9fa48("1462"), "script"));
            script.type = type;
            if (stryMutAct_9fa48("1464") ? false : stryMutAct_9fa48("1463") ? true : (stryCov_9fa48("1463", "1464"), url)) script.src = url;
            script.async = async;
            script.defer = defer;
            script.onload = () => {
              if (stryMutAct_9fa48("1465")) {
                {}
              } else {
                stryCov_9fa48("1465");
                if (stryMutAct_9fa48("1467") ? false : stryMutAct_9fa48("1466") ? true : (stryCov_9fa48("1466", "1467"), callback)) callback();
              }
            };
            if (stryMutAct_9fa48("1470") ? htmlVal || !url : stryMutAct_9fa48("1469") ? false : stryMutAct_9fa48("1468") ? true : (stryCov_9fa48("1468", "1469", "1470"), htmlVal && (stryMutAct_9fa48("1471") ? url : (stryCov_9fa48("1471"), !url)))) script.innerHTML = htmlVal;
            if (stryMutAct_9fa48("1473") ? false : stryMutAct_9fa48("1472") ? true : (stryCov_9fa48("1472", "1473"), appendTo instanceof Document)) {
              if (stryMutAct_9fa48("1474")) {
                {}
              } else {
                stryCov_9fa48("1474");
                if (stryMutAct_9fa48("1476") ? false : stryMutAct_9fa48("1475") ? true : (stryCov_9fa48("1475", "1476"), headScript)) {
                  if (stryMutAct_9fa48("1477")) {
                    {}
                  } else {
                    stryCov_9fa48("1477");
                    stryMutAct_9fa48("1478") ? appendTo.head.appendChild(script) : (stryCov_9fa48("1478"), appendTo.head?.appendChild(script));
                  }
                } else {
                  if (stryMutAct_9fa48("1479")) {
                    {}
                  } else {
                    stryCov_9fa48("1479");
                    stryMutAct_9fa48("1480") ? appendTo.body.appendChild(script) : (stryCov_9fa48("1480"), appendTo.body?.appendChild(script));
                  }
                }
              }
            }
            return () => {
              if (stryMutAct_9fa48("1481")) {
                {}
              } else {
                stryCov_9fa48("1481");
                if (stryMutAct_9fa48("1483") ? false : stryMutAct_9fa48("1482") ? true : (stryCov_9fa48("1482", "1483"), appendTo instanceof Document)) {
                  if (stryMutAct_9fa48("1484")) {
                    {}
                  } else {
                    stryCov_9fa48("1484");
                    if (stryMutAct_9fa48("1486") ? false : stryMutAct_9fa48("1485") ? true : (stryCov_9fa48("1485", "1486"), headScript)) {
                      if (stryMutAct_9fa48("1487")) {
                        {}
                      } else {
                        stryCov_9fa48("1487");
                        stryMutAct_9fa48("1488") ? appendTo.head.removeChild(script) : (stryCov_9fa48("1488"), appendTo.head?.removeChild(script));
                      }
                    } else {
                      if (stryMutAct_9fa48("1489")) {
                        {}
                      } else {
                        stryCov_9fa48("1489");
                        stryMutAct_9fa48("1490") ? appendTo.body.removeChild(script) : (stryCov_9fa48("1490"), appendTo.body?.removeChild(script));
                      }
                    }
                  }
                }
              }
            };
          }
        }
      }
    }, stryMutAct_9fa48("1491") ? [] : (stryCov_9fa48("1491"), [url, html, node, type, async, defer, headScript, callback]));
    return node;
  }
}
interface UseAsyncReturn<T, E = unknown> {
  execute: (data?: unknown) => Promise<void>;
  pending: boolean;
  value: T | null;
  error: E | null;
}
export const useAsync = <T, E = unknown>(asyncFunction: (data?: unknown) => Promise<T>, immediate: boolean = stryMutAct_9fa48("1492") ? false : (stryCov_9fa48("1492"), true)): UseAsyncReturn<T, E> => {
  if (stryMutAct_9fa48("1493")) {
    {}
  } else {
    stryCov_9fa48("1493");
    const [pending, setPending] = useState<boolean>(stryMutAct_9fa48("1494") ? true : (stryCov_9fa48("1494"), false));
    const [value, setValue] = useState<T | null>(null);
    const [error, setError] = useState<E | null>(null);
    const execute = useCallback((data?: unknown) => {
      if (stryMutAct_9fa48("1495")) {
        {}
      } else {
        stryCov_9fa48("1495");
        setPending(stryMutAct_9fa48("1496") ? false : (stryCov_9fa48("1496"), true));
        setValue(null);
        setError(null);
        return asyncFunction(data).then(stryMutAct_9fa48("1497") ? () => undefined : (stryCov_9fa48("1497"), (response: T) => setValue(response))).catch(stryMutAct_9fa48("1498") ? () => undefined : (stryCov_9fa48("1498"), (error: E) => setError(error))).finally(stryMutAct_9fa48("1499") ? () => undefined : (stryCov_9fa48("1499"), () => setPending(stryMutAct_9fa48("1500") ? true : (stryCov_9fa48("1500"), false))));
      }
    }, stryMutAct_9fa48("1501") ? [] : (stryCov_9fa48("1501"), [asyncFunction]));
    useEffect(() => {
      if (stryMutAct_9fa48("1502")) {
        {}
      } else {
        stryCov_9fa48("1502");
        if (stryMutAct_9fa48("1504") ? false : stryMutAct_9fa48("1503") ? true : (stryCov_9fa48("1503", "1504"), immediate)) {
          if (stryMutAct_9fa48("1505")) {
            {}
          } else {
            stryCov_9fa48("1505");
            execute();
          }
        }
      }
    }, stryMutAct_9fa48("1506") ? [] : (stryCov_9fa48("1506"), [execute, immediate]));
    return stryMutAct_9fa48("1507") ? {} : (stryCov_9fa48("1507"), {
      execute,
      pending,
      value,
      error
    });
  }
};
type WindowSize = {
  width: number | undefined;
  height: number | undefined;
};
export function useWindowSize(): WindowSize {
  if (stryMutAct_9fa48("1508")) {
    {}
  } else {
    stryCov_9fa48("1508");
    const [windowSize, setWindowSize] = useState<WindowSize>(stryMutAct_9fa48("1509") ? {} : (stryCov_9fa48("1509"), {
      width: undefined,
      height: undefined
    }));
    useEffect(() => {
      if (stryMutAct_9fa48("1510")) {
        {}
      } else {
        stryCov_9fa48("1510");
        if (stryMutAct_9fa48("1513") ? typeof window !== "undefined" : stryMutAct_9fa48("1512") ? false : stryMutAct_9fa48("1511") ? true : (stryCov_9fa48("1511", "1512", "1513"), typeof window === (stryMutAct_9fa48("1514") ? "" : (stryCov_9fa48("1514"), "undefined")))) return;
        const handleResize = () => {
          if (stryMutAct_9fa48("1515")) {
            {}
          } else {
            stryCov_9fa48("1515");
            setWindowSize(stryMutAct_9fa48("1516") ? {} : (stryCov_9fa48("1516"), {
              width: window.innerWidth,
              height: window.innerHeight
            }));
          }
        };
        handleResize();
        window.addEventListener(stryMutAct_9fa48("1517") ? "" : (stryCov_9fa48("1517"), "resize"), handleResize);
        return stryMutAct_9fa48("1518") ? () => undefined : (stryCov_9fa48("1518"), () => window.removeEventListener(stryMutAct_9fa48("1519") ? "" : (stryCov_9fa48("1519"), "resize"), handleResize));
      }
    }, stryMutAct_9fa48("1520") ? ["Stryker was here"] : (stryCov_9fa48("1520"), []));
    return windowSize;
  }
}

//----------------------------------------
// PactSafe executable Script
//----------------------------------------
export function getPactSafeScript(url: string, id: string, pactSafeMessageListner: string): string {
  if (stryMutAct_9fa48("1521")) {
    {}
  } else {
    stryCov_9fa48("1521");
    const innerHTML = stryMutAct_9fa48("1522") ? `` : (stryCov_9fa48("1522"), `
 (function () {
    var EmbeddedSignature = new PactSafeEmbedded('${id}}');
    EmbeddedSignature.open({
      // signing URL is generated by calling:
    // GET => /v1.1/requests/{request_id}/url?signer_id={signer_id}
    url: '${url}',
    
    allow_cancel: true,
    debug: true,
    from_left: 0,
    from_top: 0,
    message_listener: ${pactSafeMessageListner}
  });
}());
  `);
    return innerHTML;
  }
}
export function useUpdateEffect(effect: () => void, dependencies: string[] = stryMutAct_9fa48("1523") ? ["Stryker was here"] : (stryCov_9fa48("1523"), [])): void {
  if (stryMutAct_9fa48("1524")) {
    {}
  } else {
    stryCov_9fa48("1524");
    const isInitialMount = useRef(stryMutAct_9fa48("1525") ? false : (stryCov_9fa48("1525"), true));
    useEffect(() => {
      if (stryMutAct_9fa48("1526")) {
        {}
      } else {
        stryCov_9fa48("1526");
        if (stryMutAct_9fa48("1528") ? false : stryMutAct_9fa48("1527") ? true : (stryCov_9fa48("1527", "1528"), isInitialMount.current)) {
          if (stryMutAct_9fa48("1529")) {
            {}
          } else {
            stryCov_9fa48("1529");
            isInitialMount.current = stryMutAct_9fa48("1530") ? true : (stryCov_9fa48("1530"), false);
          }
        } else {
          if (stryMutAct_9fa48("1531")) {
            {}
          } else {
            stryCov_9fa48("1531");
            return effect();
          }
        }
      }
    }, dependencies);
  }
}