import validationConfig from "../validations/validationConfig";
import { isAdult, isObj, isString } from "..";
import { Message, ValidationRule } from "react-hook-form";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { Country } from "@eait-playerexp-cn/metadata-types";

export type Pattern = {
  value: RegExp;
  message: string;
};
export type MaxNumLength = {
  validate: (value: string) => void;
};
export type MaxLength = {
  value: number;
  message: string;
};
export type Required = {
  required: Message | ValidationRule<boolean>;
};
export type RequiredValidate = Required & {
  validate: (value: Country) => void;
};
export type Validate = {
  validate: (value: string) => string | boolean;
};
export type MaxLengthPatternRequired = Required & {
  maxLength: MaxLength;
  pattern: Pattern;
};
export type MaxLengthRequired = Required & {
  maxLength: MaxLength;
};
export type MaxLengthRequiredValidate = Required & {
  maxLength: MaxLength;
  validate: (value: Date) => void;
};
export type CreatorFormRules = {
  firstName: MaxLengthRequired;
  lastName: MaxLengthRequired;
  dateOfBirth: MaxLengthRequiredValidate;
  country: RequiredValidate;
  interestedCreatorCountry: RequiredValidate;
  url: Required;
  street: MaxLengthRequired;
  city: MaxLengthRequired;
  state: MaxLengthRequired;
  zipCode: MaxLengthRequired;
  tShirtSize: Required;
  entityType: Required;
  businessName: MaxLengthRequired;
  email: MaxLengthPatternRequired;
  followers: MaxNumLength;
};
export type CommunicationFormRules = {
  preferredEmail: MaxLengthPatternRequired;
  preferredPhoneNumber: MaxLengthRequired;
  contentLanguage: Required;
  language: Required;
};
export type PactSafeFormRules = {
  businessName: Required;
};

export type Labels = {
  messages: {
    firstNameTooLong: string;
    lastNameTooLong: string;
    street: string | ValidationRule<boolean>;
    streetTooLong: string;
    city: string | ValidationRule<boolean>;
    cityTooLong: string;
    state: string | ValidationRule<boolean>;
    stateTooLong: string;
    zipCode: string | ValidationRule<boolean>;
    zipCodeTooLong: string;
    tShirtSize: string | ValidationRule<boolean>;
    entityType: string | ValidationRule<boolean>;
    businessName: string | ValidationRule<boolean>;
    businessNameTooLong: string;
    email: string | ValidationRule<boolean>;
    emailTooLong: string;
    emailInvalid: string;
    url: string | ValidationRule<boolean>;
    invalidUrl: string;
    followersMaxLength: string;
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    dateOfBirthInvalid: string;
    ageMustBe18OrOlder: string;
    country: string;
  };
};

type Translations = {
  messages: {
    preferredEmailTooLong: string;
    preferredEmailInvalid: string;
    preferredPhoneNumber: string | ValidationRule<boolean>;
    preferredPhoneNumberTooLong: string;
    contentLanguage: string | ValidationRule<boolean>;
    language: string | ValidationRule<boolean>;
    preferredEmail: string;
  };
};

type Messages = {
  businessNameRequired: string;
};



const rules = (labels: Labels): CreatorFormRules => {
  return {
    firstName: {
      required: labels.messages.firstName,
      maxLength: { value: validationConfig.MAXLENGTH_40, message: labels.messages.firstNameTooLong }
    },
    lastName: {
      required: labels.messages.lastName,
      maxLength: { value: validationConfig.MAXLENGTH_80, message: labels.messages.lastNameTooLong }
    },
    dateOfBirth: {
      required: labels.messages.dateOfBirth,
      maxLength: { value: validationConfig.DATE_MAXLENGTH, message: labels.messages.dateOfBirthInvalid },
      validate: (value) => {
        if (!LocalizedDate.isValid(value)) {
          return labels.messages.dateOfBirthInvalid;
        }
        if (isAdult(value.toString())) {
          return labels.messages.ageMustBe18OrOlder;
        }
        return true;
      }
    },
    country: {
      required: labels.messages.country,
      validate: (countryVal) => {
        const { label, name, value } = countryVal;
        if (!label && !name && !value && isObj(countryVal)) {
          // param is object for drop-down
          return labels.messages.country;
        } else if (isString(countryVal) && !countryVal) {
          return labels.messages.country;
        }
        return true;
      }
    },
    interestedCreatorCountry: {
      required: labels.messages.country,
      validate: (countryVal) => {
        const { label, name, value } = countryVal;
        if ((!label && !name && isObj(countryVal)) || !value) {
          return labels.messages.country;
        } else if (isString(countryVal) && !countryVal) {
          return labels.messages.country;
        }
        return true;
      }
    },
    street: {
      required: labels.messages.street,
      maxLength: { value: validationConfig.MAXLENGTH_255, message: labels.messages.streetTooLong }
    },
    city: {
      required: labels.messages.city,
      maxLength: { value: validationConfig.MAXLENGTH_40, message: labels.messages.cityTooLong }
    },
    state: {
      required: labels.messages.state,
      maxLength: { value: validationConfig.MAXLENGTH_80, message: labels.messages.stateTooLong }
    },
    zipCode: {
      required: labels.messages.zipCode,
      maxLength: { value: validationConfig.MAXLENGTH_20, message: labels.messages.zipCodeTooLong }
    },
    tShirtSize: {
      required: labels.messages.tShirtSize
    },
    entityType: {
      required: labels.messages.entityType
    },
    businessName: {
      required: labels.messages.businessName,
      maxLength: { value: validationConfig.MAXLENGTH, message: labels.messages.businessNameTooLong }
    },
    email: {
      required: labels.messages.email,
      maxLength: { value: validationConfig.MAXLENGTH_80, message: labels.messages.emailTooLong },
      pattern: {
        value: validationConfig.regex.EMAIL,
        message: labels.messages.emailInvalid
      }
    },
    url: {
      required: false
    },
    followers: {
      validate: (value) => (/^\d{0,18}$/.test(value) && value.length <= 18) || labels.messages.followersMaxLength
    }
  };
};

const communicationRules = (translation: Translations): CommunicationFormRules => {
  return {
    preferredEmail: {
      required: translation.messages.preferredEmail,
      maxLength: { value: validationConfig.MAXLENGTH_80, message: translation.messages.preferredEmailTooLong },
      pattern: {
        value: validationConfig.regex.EMAIL,
        message: translation.messages.preferredEmailInvalid
      }
    },
    preferredPhoneNumber: {
      required: translation.messages.preferredPhoneNumber,
      maxLength: { value: validationConfig.MAXLENGTH_40, message: translation.messages.preferredPhoneNumberTooLong }
    },
    contentLanguage: { required: translation.messages.contentLanguage },
    language: { required: translation.messages.language }
  };
};

const getPactFormRules = (messages: Messages): PactSafeFormRules => {
  return {
    businessName: {
      required: messages.businessNameRequired
    }
  };
};

const formsRules = { rules, communicationRules, getPactFormRules };

export default formsRules;
