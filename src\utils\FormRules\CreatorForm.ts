import validationConfig from "../validations/validationConfig";
import { ValidationRule } from "react-hook-form";
import { Country } from "@eait-playerexp-cn/metadata-types";
import {
  MaxLength,
  MaxLengthRequired,
  MaxLengthRequiredValidate,
  Required
} from "@src/Information/PersonalInformationForm/PersonalInformationForm";

export type Pattern = {
  value: RegExp;
  message: string;
};
export type MaxNumLength = {
  validate: (value: string) => void;
};

export type RequiredValidate = Required & {
  validate: (value: Country) => string | boolean;
};
export type Validate = {
  validate: (value: string) => string | boolean;
};
export type MaxLengthPatternRequired = Required & {
  maxLength: MaxLength;
  pattern: Pattern;
};

export type CreatorFormRules = {
  firstName: MaxLengthRequired;
  lastName: MaxLengthRequired;
  dateOfBirth: MaxLengthRequiredValidate;
  country: RequiredValidate;
  interestedCreatorCountry: RequiredValidate;
  url: Required;
  street: MaxLengthRequired;
  city: MaxLengthRequired;
  state: MaxLengthRequired;
  zipCode: MaxLengthRequired;
  tShirtSize: Required;
  entityType: Required;
  businessName: MaxLengthRequired;
  email: MaxLengthPatternRequired;
  followers: MaxNumLength;
};
export type CommunicationFormRules = {
  preferredEmail: MaxLengthPatternRequired;
  preferredPhoneNumber: MaxLengthRequired;
  contentLanguage: Required;
  language: Required;
};
export type PactSafeFormRules = {
  businessName: Required;
};

export type Labels = {
  messages: {
    firstNameTooLong: string;
    lastNameTooLong: string;
    street: string | ValidationRule<boolean>;
    streetTooLong: string;
    city: string | ValidationRule<boolean>;
    cityTooLong: string;
    state: string | ValidationRule<boolean>;
    stateTooLong: string;
    zipCode: string | ValidationRule<boolean>;
    zipCodeTooLong: string;
    tShirtSize: string | ValidationRule<boolean>;
    entityType: string | ValidationRule<boolean>;
    businessName: string | ValidationRule<boolean>;
    businessNameTooLong: string;
    email: string | ValidationRule<boolean>;
    emailTooLong: string;
    emailInvalid: string;
    url: string | ValidationRule<boolean>;
    invalidUrl: string;
    followersMaxLength: string;
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    dateOfBirthInvalid: string;
    ageMustBe18OrOlder: string;
    country: string;
  };
};

type Translations = {
  messages: {
    preferredEmailTooLong: string;
    preferredEmailInvalid: string;
    preferredPhoneNumber: string | ValidationRule<boolean>;
    preferredPhoneNumberTooLong: string;
    contentLanguage: string | ValidationRule<boolean>;
    language: string | ValidationRule<boolean>;
    preferredEmail: string;
  };
};

type Messages = {
  businessNameRequired: string;
};

const communicationRules = (translation: Translations): CommunicationFormRules => {
  return {
    preferredEmail: {
      required: translation.messages.preferredEmail,
      maxLength: { value: validationConfig.MAXLENGTH_80, message: translation.messages.preferredEmailTooLong },
      pattern: {
        value: validationConfig.regex.EMAIL,
        message: translation.messages.preferredEmailInvalid
      }
    },
    preferredPhoneNumber: {
      required: translation.messages.preferredPhoneNumber,
      maxLength: { value: validationConfig.MAXLENGTH_40, message: translation.messages.preferredPhoneNumberTooLong }
    },
    contentLanguage: { required: translation.messages.contentLanguage },
    language: { required: translation.messages.language }
  };
};

const getPactFormRules = (messages: Messages): PactSafeFormRules => {
  return {
    businessName: {
      required: messages.businessNameRequired
    }
  };
};

const formsRules = { communicationRules, getPactFormRules };

export default formsRules;
