// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import React, { memo } from "react";
import CreatorDisplayName from "./CreatorDisplayName/CreatorDisplayName";
import UpdateProfilePicture from "./UploadProfilePicture/UpdateProfilePicture";
import { NextRouter } from "next/router";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { AuthenticatedUser, Dispatch } from "../../utils/types";
import { ButtonLabels, ProfileLabels, ProfilePictureLabels } from "../types";
import { AccountInformationWithLocalizedDate } from "../InformationPage";
interface ProfileCardProps {
  labels: {
    creatorSince: string;
    buttons: ButtonLabels;
    profilePictureLabels: ProfilePictureLabels;
    profileLabels: ProfileLabels;
  };
  user: AuthenticatedUser;
  registrationDate: string;
  accountInformation: AccountInformationWithLocalizedDate;
  data: string;
  stableDispatch: Dispatch;
  DEFAULT_AVATAR_IMAGE: string;
  creatorsClient: TraceableHttpClient;
  router: NextRouter;
}
const ProfileCard = ({
  labels,
  user,
  accountInformation,
  registrationDate,
  data,
  stableDispatch,
  creatorsClient,
  DEFAULT_AVATAR_IMAGE,
  router
}: ProfileCardProps) => {
  if (stryMutAct_9fa48("939")) {
    {}
  } else {
    stryCov_9fa48("939");
    return <div className="profile-card">
      <UpdateProfilePicture isPlaceholderDefault={stryMutAct_9fa48("940") ? true : (stryCov_9fa48("940"), false)} src={user.avatar} user={user} labels={labels} stableDispatch={stableDispatch} creatorsClient={creatorsClient} DEFAULT_AVATAR_IMAGE={DEFAULT_AVATAR_IMAGE} router={router} />

      <div className="profile-card-info">
        <CreatorDisplayName user={user} tooltip={data} />
        {stryMutAct_9fa48("943") ? accountInformation || <div className="profile-card-sub-title">
            <span className="profile-card-creator-since">{labels.creatorSince} </span>
            {registrationDate}
          </div> : stryMutAct_9fa48("942") ? false : stryMutAct_9fa48("941") ? true : (stryCov_9fa48("941", "942", "943"), accountInformation && <div className="profile-card-sub-title">
            <span className="profile-card-creator-since">{labels.creatorSince} </span>
            {registrationDate}
          </div>)}
      </div>
    </div>;
  }
};
export default memo(ProfileCard);