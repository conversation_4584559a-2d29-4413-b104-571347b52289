// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import { Button, edit, Icon } from "@eait-playerexp-cn/core-ui-kit";
import React, { memo, useCallback, useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
const ProfileFormAction = ({
  buttons,
  action: onChange,
  isSaved = stryMutAct_9fa48("1085") ? true : (stryCov_9fa48("1085"), false),
  isLoader = stryMutAct_9fa48("1086") ? true : (stryCov_9fa48("1086"), false)
}) => {
  if (stryMutAct_9fa48("1087")) {
    {}
  } else {
    stryCov_9fa48("1087");
    const {
      formState
    } = useFormContext();
    const [isEdit, setIsEdit] = useState(stryMutAct_9fa48("1088") ? true : (stryCov_9fa48("1088"), false));
    const onEdit = useCallback(() => {
      if (stryMutAct_9fa48("1089")) {
        {}
      } else {
        stryCov_9fa48("1089");
        setIsEdit(stryMutAct_9fa48("1090") ? false : (stryCov_9fa48("1090"), true));
        if (stryMutAct_9fa48("1092") ? false : stryMutAct_9fa48("1091") ? true : (stryCov_9fa48("1091", "1092"), onChange)) onChange(stryMutAct_9fa48("1093") ? false : (stryCov_9fa48("1093"), true));
      }
    }, stryMutAct_9fa48("1094") ? [] : (stryCov_9fa48("1094"), [isEdit]));
    const onCancel = useCallback(() => {
      if (stryMutAct_9fa48("1095")) {
        {}
      } else {
        stryCov_9fa48("1095");
        setIsEdit(stryMutAct_9fa48("1096") ? true : (stryCov_9fa48("1096"), false));
        if (stryMutAct_9fa48("1098") ? false : stryMutAct_9fa48("1097") ? true : (stryCov_9fa48("1097", "1098"), onChange)) onChange(stryMutAct_9fa48("1099") ? true : (stryCov_9fa48("1099"), false));
      }
    }, stryMutAct_9fa48("1100") ? [] : (stryCov_9fa48("1100"), [isEdit]));
    useEffect(() => {
      if (stryMutAct_9fa48("1101")) {
        {}
      } else {
        stryCov_9fa48("1101");
        if (stryMutAct_9fa48("1104") ? isEdit || isSaved : stryMutAct_9fa48("1103") ? false : stryMutAct_9fa48("1102") ? true : (stryCov_9fa48("1102", "1103", "1104"), isEdit && isSaved)) setIsEdit(stryMutAct_9fa48("1105") ? true : (stryCov_9fa48("1105"), false));
      }
    }, stryMutAct_9fa48("1106") ? [] : (stryCov_9fa48("1106"), [isSaved]));
    return <div className="form-action">
      {stryMutAct_9fa48("1109") ? !isEdit || <Button variant="secondary" size="sm" onClick={onEdit}>
          <Icon icon={edit} /> &nbsp;
          {buttons.edit}
        </Button> : stryMutAct_9fa48("1108") ? false : stryMutAct_9fa48("1107") ? true : (stryCov_9fa48("1107", "1108", "1109"), (stryMutAct_9fa48("1110") ? isEdit : (stryCov_9fa48("1110"), !isEdit)) && <Button variant="secondary" size="sm" onClick={onEdit}>
          <Icon icon={edit} /> &nbsp;
          {buttons.edit}
        </Button>)}
      {stryMutAct_9fa48("1113") ? isEdit || <>
          <Button variant="tertiary" size="sm" onClick={onCancel}>
            {buttons.cancel}
          </Button>
          &nbsp;
          <Button variant="primary" size="sm" type="submit" disabled={!formState.isValid || isLoader} spinner={isLoader}>
            {buttons.save}
          </Button>
        </> : stryMutAct_9fa48("1112") ? false : stryMutAct_9fa48("1111") ? true : (stryCov_9fa48("1111", "1112", "1113"), isEdit && <>
          <Button variant="tertiary" size="sm" onClick={onCancel}>
            {buttons.cancel}
          </Button>
          &nbsp;
          <Button variant="primary" size="sm" type="submit" disabled={stryMutAct_9fa48("1116") ? !formState.isValid && isLoader : stryMutAct_9fa48("1115") ? false : stryMutAct_9fa48("1114") ? true : (stryCov_9fa48("1114", "1115", "1116"), (stryMutAct_9fa48("1117") ? formState.isValid : (stryCov_9fa48("1117"), !formState.isValid)) || isLoader)} spinner={isLoader}>
            {buttons.save}
          </Button>
        </>)}
    </div>;
  }
};
export default memo(ProfileFormAction);