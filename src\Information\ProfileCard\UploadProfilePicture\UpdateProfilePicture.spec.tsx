import { render, screen, waitFor } from "@testing-library/react";
import UpdateProfilePicture from "./UpdateProfilePicture";
import React from "react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import { AuthenticatedUser } from "@src/utils/types";
import { ButtonLabels, ProfileLabels, ProfilePictureLabels } from "../../types";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn()
  }))
});

Object.defineProperty(window.URL, "createObjectURL", {
  writable: true,
  value: jest.fn().mockImplementation(() => "mocked-object-url")
});

Object.defineProperty(window.URL, "revokeObjectURL", {
  writable: true,
  value: jest.fn()
});

jest.mock("../../../utils", () => ({
  SESSION_USER: "SessionUser"
}));

const uploadAvatar = jest.fn().mockResolvedValue({ data: { avatar: "new-avatar-url" } });
jest.mock("@eait-playerexp-cn/creators-http-client", () => ({
  CreatorsService: jest.fn().mockImplementation(() => ({
    uploadAvatar
  }))
}));

describe("UpdateProfilePicture", () => {
  const user: AuthenticatedUser = {
    analyticsId: "xNIEuLMZCvlw+7q2oZaBuG0cTdqHTa2JP3l2eCZ2Sjg=",
    avatar: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png",
    creatorCode: "TestAFFILIATE20250",
    isFlagged: false,
    isPayable: true,
    needsMigration: false,
    programs: ["creator_network", "affiliate", "sims_creator_program"],
    status: "ACTIVE",
    tier: "CREATORNETWORK",
    type: "CREATOR",
    username: "245902"
  };

  const labels = {
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close",
      browse: "Browse"
    } as ButtonLabels,
    profilePictureLabels: {
      title: "Change My Avatar",
      message: "Select an image from your computer (JPEG, PNG or GIF). Image should be square and less than 1MB.",
      termsAndConditionsFirst:
        "Respect the rights of others. Only upload images that you own or that you have written permission to freely distribute.",
      termsAndConditionsMiddle: "User Agreement",
      termsAndConditionsLast: "for more information.",
      avatarRequired: "Please select an image",
      avatarInvalid: "Please select valid image",
      avatarMoreThanLimit: "Image size should be less than 1MB"
    } as ProfilePictureLabels,
    profileLabels: {
      updateAvatar: "Update Avatar"
    } as ProfileLabels
  };

  const props = {
    isPlaceholderDefault: false,
    src: user.avatar,
    user,
    labels,
    stableDispatch: jest.fn(),
    creatorsClient: jest.fn() as unknown as TraceableHttpClient,
    defaultAvatarImage: "https://example.com/default-avatar.png",
    locale: "en-us"
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("shows profile picture correctly", () => {
    render(<UpdateProfilePicture {...props} />);

    const avatarImage = screen.getByAltText("profile-card-avatar");
    expect(avatarImage).toBeInTheDocument();
    expect(avatarImage).toHaveAttribute("src", user.avatar);
  });

  it("should display update avatar button", () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    expect(updateButton).toBeInTheDocument();
  });

  it("should open modal when update avatar button is clicked", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    expect(screen.getByText(labels.profilePictureLabels.title)).toBeInTheDocument();
    expect(screen.getByText(labels.profilePictureLabels.message)).toBeInTheDocument();
  });

  it("shows file input in modal", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const fileInput = screen.getByLabelText(/browse/i);
    expect(fileInput).toBeInTheDocument();
    expect(fileInput).toHaveAttribute("type", "file");
    expect(fileInput).toHaveAttribute("hidden");
    expect(fileInput).toHaveAttribute("id", "upload");
    expect(fileInput).toHaveAttribute("name", "upload");
  });

  it("shows terms and conditions text in modal", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    expect(screen.getByText(labels.profilePictureLabels.termsAndConditionsFirst, { exact: false })).toBeInTheDocument();
    expect(screen.getByText("User Agreement")).toBeInTheDocument();
    expect(screen.getByText(/for more information/)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
  });

  it("should close modal when cancel button is clicked", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    await userEvent.click(cancelButton);

    await waitFor(() => {
      expect(screen.queryByText("Change My Avatar")).not.toBeInTheDocument();
    });
  });

  it("should close modal when close button is clicked", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const closeButton = screen.getByRole("button", { name: /close/i });
    await userEvent.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByText("Change My Avatar")).not.toBeInTheDocument();
    });
  });

  it("should show save button as disabled initially", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const saveButton = screen.getByRole("button", { name: "Save" });
    expect(saveButton).toBeDisabled();
  });

  it("should show default avatar when src is not provided", () => {
    const propsWithoutSrc = {
      ...props,
      src: undefined
    };

    render(<UpdateProfilePicture {...propsWithoutSrc} />);

    const avatarImage = screen.getByAltText("profile-card-avatar");
    expect(avatarImage).toBeInTheDocument();
  });

  it("handles placeholder default correctly", () => {
    const propsWithPlaceholder = {
      ...props,
      isPlaceholderDefault: true
    };

    render(<UpdateProfilePicture {...propsWithPlaceholder} />);

    const avatarImage = screen.getByAltText("profile-card-avatar");
    expect(avatarImage).toBeInTheDocument();
  });

  it("should show validation message labels in props", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    expect(props.labels.profilePictureLabels.avatarRequired).toBe("Please select an image");
    expect(props.labels.profilePictureLabels.avatarInvalid).toBe("Please select valid image");
    expect(props.labels.profilePictureLabels.avatarMoreThanLimit).toBe("Image size should be less than 1MB");
  });

  it("should show file size limit in message", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    expect(screen.getByText(/less than 1MB/)).toBeInTheDocument();
  });

  it("should handle form submission", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const saveButton = screen.getByRole("button", { name: "Save" });
    expect(saveButton).toHaveAttribute("type", "submit");
    expect(saveButton).toBeDisabled();
  });

  it("should be accessible", async () => {
    const { container } = render(<UpdateProfilePicture {...props} />);

    expect(await axe(container)).toHaveNoViolations();
  });

  it("should handle missing props gracefully", () => {
    const minimalProps = {
      ...props,
      src: ""
    };

    expect(() => {
      render(<UpdateProfilePicture {...minimalProps} />);
    }).not.toThrow();
  });
});