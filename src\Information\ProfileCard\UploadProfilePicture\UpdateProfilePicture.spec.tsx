import { act, render, screen, waitFor } from "@testing-library/react";
import UpdateProfilePicture from "./UpdateProfilePicture";
import React from "react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import { AuthenticatedUser } from "@src/utils/types";
import { ButtonLabels, ProfileLabels, ProfilePictureLabels } from "../../types";

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  reload: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  prefetch: jest.fn(),
  beforePopState: jest.fn(),
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn()
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  defaultLocale: "en",
  domainLocales: [],
  isPreview: false,
  route: "/",
  pathname: "/",
  query: {},
  asPath: "/",
  basePath: "",
  locale: "en",
  locales: ["en"]
} as any;

const mockCreatorsClient = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  patch: jest.fn(),
  client: {} as any,
  provider: {} as any,
  tracer: {} as any,
  upload: jest.fn()
} as any;

const mockDispatch = jest.fn();

// Mock the SESSION_USER utility
jest.mock("../../../utils", () => ({
  SESSION_USER: "mockSessionUser"
}));

// Mock the CreatorsService
jest.mock("@eait-playerexp-cn/creators-http-client", () => ({
  CreatorsService: {
    uploadAvatar: jest.fn()
  }
}));

describe("UpdateProfilePicture - Tests for profile picture update functionality including rendering, modal handling, file upload, validation, API calls, accessibility, and error handling", () => {
  const user: AuthenticatedUser = {
    analyticsId: "xNIEuLMZCvlw+7q2oZaBuG0cTdqHTa2JP3l2eCZ2Sjg=",
    avatar: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png",
    creatorCode: "TestAFFILIATE20250",
    isFlagged: false,
    isPayable: true,
    needsMigration: false,
    programs: ["creator_network", "affiliate", "sims_creator_program"],
    status: "ACTIVE",
    tier: "CREATORNETWORK",
    type: "CREATOR",
    username: "245902"
  };

  const labels = {
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close",
      browse: "Browse"
    } as ButtonLabels,
    profilePictureLabels: {
      title: "Change My Avatar",
      message: "Select an image from your computer (JPEG, PNG or GIF). Image should be square and less than 1MB.",
      termsAndConditionsFirst: "Respect the rights of others. Only upload images that you own or that you have written permission to freely distribute.",
      termsAndConditionsMiddle: "User Agreement",
      termsAndConditionsLast: "for more information.",
      avatarRequired: "Please select an image",
      avatarInvalid: "Please select valid image",
      avatarMoreThanLimit: "Image size should be less than 1MB"
    } as ProfilePictureLabels,
    profileLabels: {
      updateAvatar: "Update Avatar"
    } as ProfileLabels
  };

  const props = {
    isPlaceholderDefault: false,
    src: user.avatar,
    user,
    labels,
    stableDispatch: mockDispatch,
    creatorsClient: mockCreatorsClient,
    DEFAULT_AVATAR_IMAGE: "https://example.com/default-avatar.png",
    router: mockRouter
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show profile picture correctly", () => {
    render(<UpdateProfilePicture {...props} />);

    const avatarImage = screen.getByRole("img", { name: /avatar/i });
    expect(avatarImage).toBeInTheDocument();
    expect(avatarImage).toHaveAttribute("src", user.avatar);
  });

  it("should display update avatar button", () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    expect(updateButton).toBeInTheDocument();
  });

  it("should open modal when update avatar button is clicked", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    expect(screen.getByText("Change My Avatar")).toBeInTheDocument();
    expect(screen.getByText("Select an image from your computer (JPEG, PNG or GIF). Image should be square and less than 1MB.")).toBeInTheDocument();
  });

  it("should show file input in modal", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const fileInput = screen.getByLabelText(/browse/i);
    expect(fileInput).toBeInTheDocument();
    expect(fileInput).toHaveAttribute("type", "file");
  });

  it("should show terms and conditions text in modal", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    expect(screen.getByText(/Respect the rights of others/)).toBeInTheDocument();
    expect(screen.getByText("User Agreement")).toBeInTheDocument();
    expect(screen.getByText(/for more information/)).toBeInTheDocument();
  });

  it("should show cancel and save buttons in modal", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
  });

  it("should close modal when cancel button is clicked", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    await userEvent.click(cancelButton);

    await waitFor(() => {
      expect(screen.queryByText("Change My Avatar")).not.toBeInTheDocument();
    });
  });

  it("should close modal when close button is clicked", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const closeButton = screen.getByRole("button", { name: /close/i });
    await userEvent.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByText("Change My Avatar")).not.toBeInTheDocument();
    });
  });

  it("should handle file selection", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const fileInput = screen.getByLabelText(/browse/i);
    const file = new File(["test"], "test.jpg", { type: "image/jpeg" });

    await userEvent.upload(fileInput, file);

    expect(fileInput).toBeInTheDocument();
  });

  it("should show save button as disabled initially", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const saveButton = screen.getByRole("button", { name: "Save" });
    expect(saveButton).toBeDisabled();
  });

  it("should enable save button when file is selected", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const fileInput = screen.getByLabelText(/browse/i);
    const file = new File(["test"], "test.jpg", { type: "image/jpeg" });

    await userEvent.upload(fileInput, file);

    await waitFor(() => {
      const saveButton = screen.getByRole("button", { name: "Save" });
      expect(saveButton).not.toBeDisabled();
    });
  });

  it("should show default avatar when src is not provided", () => {
    const propsWithoutSrc = {
      ...props,
      src: undefined
    };

    render(<UpdateProfilePicture {...propsWithoutSrc} />);

    const avatarImage = screen.getByRole("img", { name: /avatar/i });
    expect(avatarImage).toBeInTheDocument();
  });

  it("should handle placeholder default correctly", () => {
    const propsWithPlaceholder = {
      ...props,
      isPlaceholderDefault: true
    };

    render(<UpdateProfilePicture {...propsWithPlaceholder} />);

    const avatarImage = screen.getByRole("img", { name: /avatar/i });
    expect(avatarImage).toBeInTheDocument();
  });

  it("should show browse button in modal", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const browseButton = screen.getByRole("button", { name: "Browse" });
    expect(browseButton).toBeInTheDocument();
  });

  it("should handle file validation for large files", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const fileInput = screen.getByLabelText(/browse/i);
    const largeFile = new File(["x".repeat(2000000)], "large.jpg", { type: "image/jpeg" });

    await userEvent.upload(fileInput, largeFile);

    await waitFor(() => {
      expect(screen.getByText("Image size should be less than 1MB")).toBeInTheDocument();
    });
  });

  it("should handle invalid file types", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const fileInput = screen.getByLabelText(/browse/i);
    const invalidFile = new File(["test"], "test.txt", { type: "text/plain" });

    await userEvent.upload(fileInput, invalidFile);

    await waitFor(() => {
      expect(screen.getByText("Please select valid image")).toBeInTheDocument();
    });
  });

  it("should show required message when no file is selected and save is clicked", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const saveButton = screen.getByRole("button", { name: "Save" });
    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText("Please select an image")).toBeInTheDocument();
    });
  });

  it("should handle successful file upload", async () => {
    const mockUploadAvatar = jest.fn().mockResolvedValue({ data: { avatar: "new-avatar-url" } });
    require("@eait-playerexp-cn/creators-http-client").CreatorsService.uploadAvatar = mockUploadAvatar;

    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    const fileInput = screen.getByLabelText(/browse/i);
    const file = new File(["test"], "test.jpg", { type: "image/jpeg" });

    await userEvent.upload(fileInput, file);

    await waitFor(() => {
      const saveButton = screen.getByRole("button", { name: "Save" });
      expect(saveButton).not.toBeDisabled();
    });

    const saveButton = screen.getByRole("button", { name: "Save" });
    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(mockUploadAvatar).toHaveBeenCalled();
    });
  });

  it("should be accessible", async () => {
    let results: any;
    const { container } = render(<UpdateProfilePicture {...props} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  it("should be accessible with modal open", async () => {
    let results: any;
    const { container } = render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  it("should handle missing props gracefully", () => {
    const minimalProps = {
      ...props,
      src: ""
    };

    expect(() => {
      render(<UpdateProfilePicture {...minimalProps} />);
    }).not.toThrow();
  });

  it("should be memoized and not re-render unnecessarily", () => {
    const { rerender } = render(<UpdateProfilePicture {...props} />);

    rerender(<UpdateProfilePicture {...props} />);

    const avatarImage = screen.getByRole("img", { name: /avatar/i });
    expect(avatarImage).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Update Avatar" })).toBeInTheDocument();
  });
});