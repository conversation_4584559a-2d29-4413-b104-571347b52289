// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import Form from "../utils/Form";
import { CreatorProfile, CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import MailingAddressForm from "./MailingAddressForm/MailingAddressForm";
import PersonalInformationForm from "./PersonalInformationForm/PersonalInformationForm";
import MiscellaneousForm from "./MiscellaneousForm/MiscellaneousForm";
import ProfileCard from "./ProfileCard/ProfileCard";
import CreatorForm from "../utils/FormRules/CreatorForm";
import LegalEntityForm from "./LegalEntityForm/LegalEntityForm";
import Loading from "../components/Loading/Loading";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { ERROR, onToastClose, toastContent, useAsync, VALIDATION_ERROR } from "../utils";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { InfoLabels, Overwrite } from "./types";
import { AccountInformationResponse, AdditionalInformationPayload, LegalInformationPayload, MailingAddressPayload, UpdateCreatorRequest } from "@eait-playerexp-cn/creator-types";
import { AuthenticatedUser, BrowserAnalytics, Dispatch, ErrorHandling, Layout, State } from "@src/utils/types";
import { Country, HardwarePartner } from "@eait-playerexp-cn/metadata-types";
import { NextRouter } from "next/router";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
export type Configuration = {
  creatorsClient: TraceableHttpClient;
  DEFAULT_AVATAR_IMAGE: string;
  PROGRAM_CODE: string;
};
export type AccountInformationWithLocalizedDate = Overwrite<AccountInformationResponse, {
  dateOfBirth: LocalizedDate;
}>;
export type InformationProps = {
  infoLabels: InfoLabels;
  buttons: {
    edit: string;
    cancel: string;
    save: string;
    close: string;
    browse: string;
  };
  user: AuthenticatedUser;
  creator: CreatorProfile;
  updateCreator: (creator: UpdateCreatorRequest) => void;
  hardwarePartners: HardwarePartner[];
  countries: Country[];
  layout: Layout;
  analytics: BrowserAnalytics;
  allCountries: Country[];
  router: NextRouter;
  errorHandler: ErrorHandling;
  configuration: Configuration;
  stableDispatch: Dispatch;
  state: State;
};
export default memo(function Information({
  infoLabels,
  buttons,
  user,
  creator,
  updateCreator,
  hardwarePartners,
  countries,
  layout,
  analytics,
  allCountries,
  router,
  errorHandler,
  configuration,
  stableDispatch,
  state
}: InformationProps) {
  if (stryMutAct_9fa48("288")) {
    {}
  } else {
    stryCov_9fa48("288");
    const {
      creatorsClient,
      DEFAULT_AVATAR_IMAGE,
      PROGRAM_CODE
    } = configuration;
    const {
      main: {
        unhandledError
      }
    } = layout;
    const {
      isValidationError,
      validationErrors,
      isError
    } = state;
    const {
      error: errorToast
    } = useToast();
    const [isAccountInformationSaved, setIsAccountInformationSaved] = useState(stryMutAct_9fa48("289") ? true : (stryCov_9fa48("289"), false));
    const [isMailingAddressSaved, setIsMailingAddressSaved] = useState(stryMutAct_9fa48("290") ? true : (stryCov_9fa48("290"), false));
    const [isAdditionalInformationSaved, setIsAdditionalInformationSaved] = useState(stryMutAct_9fa48("291") ? true : (stryCov_9fa48("291"), false));
    const [isLegalEntitySaved, setIsLegalEntitySaved] = useState(stryMutAct_9fa48("292") ? true : (stryCov_9fa48("292"), false));
    const [accountInformation, setAccountInformation] = useState<AccountInformationWithLocalizedDate>(null);
    const [mailingAddress, setMailingAddress] = useState<MailingAddressPayload>(null);
    const [registrationDate, setRegistrationDate] = useState<string | null>(null);
    const [legalEntity, setLegalEntity] = useState<LegalInformationPayload>(null);
    const [additionalInformation, setAdditionalInformation] = useState<AdditionalInformationPayload>(null);
    const rules = useMemo(stryMutAct_9fa48("293") ? () => undefined : (stryCov_9fa48("293"), () => CreatorForm.rules(infoLabels)), stryMutAct_9fa48("294") ? [] : (stryCov_9fa48("294"), [infoLabels]));
    const creatorService = useMemo(stryMutAct_9fa48("295") ? () => undefined : (stryCov_9fa48("295"), () => new CreatorsService(creatorsClient, DEFAULT_AVATAR_IMAGE)), stryMutAct_9fa48("296") ? [] : (stryCov_9fa48("296"), [creatorsClient]));
    const submitAccountInformation = useCallback(async data => {
      if (stryMutAct_9fa48("297")) {
        {}
      } else {
        stryCov_9fa48("297");
        try {
          if (stryMutAct_9fa48("298")) {
            {}
          } else {
            stryCov_9fa48("298");
            const formData = stryMutAct_9fa48("299") ? {} : (stryCov_9fa48("299"), {
              ...accountInformation,
              ...data
            });
            const updatedAccountInformation = stryMutAct_9fa48("300") ? {} : (stryCov_9fa48("300"), {
              ...formData,
              dateOfBirth: LocalizedDate.format(formData.dateOfBirth, stryMutAct_9fa48("301") ? "" : (stryCov_9fa48("301"), "YYYY-MM-DD"))
            });
            await creatorService.updateCreator({
              accountInformation: updatedAccountInformation,
              program: {
                code: PROGRAM_CODE
              }
            } as unknown as UpdateCreatorRequest);
            analytics.updatedBasicInformation(stryMutAct_9fa48("302") ? {} : (stryCov_9fa48("302"), {
              locale: stryMutAct_9fa48("305") ? router.locale && "" : stryMutAct_9fa48("304") ? false : stryMutAct_9fa48("303") ? true : (stryCov_9fa48("303", "304", "305"), router.locale || (stryMutAct_9fa48("306") ? "Stryker was here!" : (stryCov_9fa48("306"), "")))
            }));
            formData.dateOfBirth = LocalizedDate.fromFormattedDate(formData.dateOfBirth);
            setAccountInformation(formData);
            const updatedCreator = stryMutAct_9fa48("307") ? {} : (stryCov_9fa48("307"), {
              ...creator,
              accountInformation: formData
            });
            updateCreator(updatedCreator as unknown as UpdateCreatorRequest);
            setIsAccountInformationSaved(stryMutAct_9fa48("308") ? false : (stryCov_9fa48("308"), true));
          }
        } catch (e) {
          if (stryMutAct_9fa48("309")) {
            {}
          } else {
            stryCov_9fa48("309");
            errorHandler(stableDispatch, e);
          }
        }
      }
    }, stryMutAct_9fa48("310") ? [] : (stryCov_9fa48("310"), [isAccountInformationSaved, accountInformation, stableDispatch]));
    const submitMailingAddress = useCallback(async data => {
      if (stryMutAct_9fa48("311")) {
        {}
      } else {
        stryCov_9fa48("311");
        try {
          if (stryMutAct_9fa48("312")) {
            {}
          } else {
            stryCov_9fa48("312");
            const updatedMailingAddress = stryMutAct_9fa48("313") ? {} : (stryCov_9fa48("313"), {
              ...data,
              country: stryMutAct_9fa48("314") ? {} : (stryCov_9fa48("314"), {
                code: data.country.value,
                name: data.country.name
              })
            });
            await creatorService.updateCreator({
              mailingAddress: updatedMailingAddress,
              program: {
                code: PROGRAM_CODE
              }
            } as UpdateCreatorRequest);
            analytics.updatedBasicInformation(stryMutAct_9fa48("315") ? {} : (stryCov_9fa48("315"), {
              locale: stryMutAct_9fa48("318") ? router.locale && "" : stryMutAct_9fa48("317") ? false : stryMutAct_9fa48("316") ? true : (stryCov_9fa48("316", "317", "318"), router.locale || (stryMutAct_9fa48("319") ? "Stryker was here!" : (stryCov_9fa48("319"), "")))
            }));
            setMailingAddress(data);
            const updatedCreator = stryMutAct_9fa48("320") ? {} : (stryCov_9fa48("320"), {
              ...creator,
              mailingAddress: data
            });
            updateCreator(updatedCreator as unknown as UpdateCreatorRequest);
            setIsMailingAddressSaved(stryMutAct_9fa48("321") ? false : (stryCov_9fa48("321"), true));
          }
        } catch (e) {
          if (stryMutAct_9fa48("322")) {
            {}
          } else {
            stryCov_9fa48("322");
            errorHandler(stableDispatch, e);
          }
        }
      }
    }, stryMutAct_9fa48("323") ? [] : (stryCov_9fa48("323"), [isMailingAddressSaved, stableDispatch]));
    const submitAdditionalInformation = useCallback(async data => {
      if (stryMutAct_9fa48("324")) {
        {}
      } else {
        stryCov_9fa48("324");
        try {
          if (stryMutAct_9fa48("325")) {
            {}
          } else {
            stryCov_9fa48("325");
            const updatedAdditionalInformation = stryMutAct_9fa48("326") ? {} : (stryCov_9fa48("326"), {
              hardwarePartners: data.hardwarePartners.map(item => {
                if (stryMutAct_9fa48("327")) {
                  {}
                } else {
                  stryCov_9fa48("327");
                  return stryMutAct_9fa48("328") ? {} : (stryCov_9fa48("328"), {
                    id: item.value,
                    name: item.label
                  });
                }
              }),
              hoodieSize: data.hoodieSize.value
            });
            await creatorService.updateCreator({
              additionalInformation: updatedAdditionalInformation,
              program: {
                code: PROGRAM_CODE
              }
            } as UpdateCreatorRequest);
            analytics.updatedBasicInformation(stryMutAct_9fa48("329") ? {} : (stryCov_9fa48("329"), {
              locale: stryMutAct_9fa48("332") ? router.locale && "" : stryMutAct_9fa48("331") ? false : stryMutAct_9fa48("330") ? true : (stryCov_9fa48("330", "331", "332"), router.locale || (stryMutAct_9fa48("333") ? "Stryker was here!" : (stryCov_9fa48("333"), "")))
            }));
            data.hardwarePartners = data.hardwarePartners.map(item => {
              if (stryMutAct_9fa48("334")) {
                {}
              } else {
                stryCov_9fa48("334");
                return stryMutAct_9fa48("335") ? {} : (stryCov_9fa48("335"), {
                  ...item,
                  id: item.value,
                  name: item.label
                });
              }
            });
            data.hoodieSize = data.hoodieSize.value;
            setAdditionalInformation(data);
            const updatedCreator = stryMutAct_9fa48("336") ? {} : (stryCov_9fa48("336"), {
              ...creator,
              additionalInformation: data
            });
            updateCreator(updatedCreator as unknown as UpdateCreatorRequest);
            setIsAdditionalInformationSaved(stryMutAct_9fa48("337") ? false : (stryCov_9fa48("337"), true));
          }
        } catch (e) {
          if (stryMutAct_9fa48("338")) {
            {}
          } else {
            stryCov_9fa48("338");
            errorHandler(stableDispatch, e);
          }
        }
      }
    }, stryMutAct_9fa48("339") ? [] : (stryCov_9fa48("339"), [isAdditionalInformationSaved, stableDispatch]));
    const submitLegalEntity = useCallback(async data => {
      if (stryMutAct_9fa48("340")) {
        {}
      } else {
        stryCov_9fa48("340");
        try {
          if (stryMutAct_9fa48("341")) {
            {}
          } else {
            stryCov_9fa48("341");
            const updatedLegalInformation = stryMutAct_9fa48("342") ? {} : (stryCov_9fa48("342"), {
              ...data,
              country: stryMutAct_9fa48("343") ? {} : (stryCov_9fa48("343"), {
                code: data.country.value,
                name: data.country.name
              }),
              entityType: data.entityType.value
            });
            await creatorService.updateCreator({
              legalInformation: updatedLegalInformation,
              program: {
                code: PROGRAM_CODE
              }
            } as UpdateCreatorRequest);
            analytics.updatedBasicInformation(stryMutAct_9fa48("344") ? {} : (stryCov_9fa48("344"), {
              locale: stryMutAct_9fa48("347") ? router.locale && "" : stryMutAct_9fa48("346") ? false : stryMutAct_9fa48("345") ? true : (stryCov_9fa48("345", "346", "347"), router.locale || (stryMutAct_9fa48("348") ? "Stryker was here!" : (stryCov_9fa48("348"), "")))
            }));
            setLegalEntity(data);
            const updatedCreator = stryMutAct_9fa48("349") ? {} : (stryCov_9fa48("349"), {
              ...creator,
              legalInformation: data
            });
            updateCreator(updatedCreator as unknown as UpdateCreatorRequest);
            setIsLegalEntitySaved(stryMutAct_9fa48("350") ? false : (stryCov_9fa48("350"), true));
          }
        } catch (e) {
          if (stryMutAct_9fa48("351")) {
            {}
          } else {
            stryCov_9fa48("351");
            errorHandler(stableDispatch, e);
          }
        }
      }
    }, stryMutAct_9fa48("352") ? [] : (stryCov_9fa48("352"), [isLegalEntitySaved, stableDispatch]));
    const {
      pending: pendingAccUpd,
      execute: onSubmitAccountInformation
    } = useAsync(submitAccountInformation, stryMutAct_9fa48("353") ? true : (stryCov_9fa48("353"), false));
    const {
      pending: pendingMailAddUpd,
      execute: onSubmitMailingAddress
    } = useAsync(submitMailingAddress, stryMutAct_9fa48("354") ? true : (stryCov_9fa48("354"), false));
    const {
      pending: pendingAddInfoUpd,
      execute: onSubmitAdditionalInformation
    } = useAsync(submitAdditionalInformation, stryMutAct_9fa48("355") ? true : (stryCov_9fa48("355"), false));
    const {
      pending: pendingLegAddUpd,
      execute: onSubmitLegalEntity
    } = useAsync(submitLegalEntity, stryMutAct_9fa48("356") ? true : (stryCov_9fa48("356"), false));
    const accountInformationOnChange = useCallback(stryMutAct_9fa48("357") ? () => undefined : (stryCov_9fa48("357"), () => setIsAccountInformationSaved(stryMutAct_9fa48("358") ? true : (stryCov_9fa48("358"), false))), stryMutAct_9fa48("359") ? ["Stryker was here"] : (stryCov_9fa48("359"), []));
    const mailingAddressOnChange = useCallback(stryMutAct_9fa48("360") ? () => undefined : (stryCov_9fa48("360"), () => setIsMailingAddressSaved(stryMutAct_9fa48("361") ? true : (stryCov_9fa48("361"), false))), stryMutAct_9fa48("362") ? ["Stryker was here"] : (stryCov_9fa48("362"), []));
    const additionalInformationOnChange = useCallback(stryMutAct_9fa48("363") ? () => undefined : (stryCov_9fa48("363"), () => setIsAdditionalInformationSaved(stryMutAct_9fa48("364") ? true : (stryCov_9fa48("364"), false))), stryMutAct_9fa48("365") ? ["Stryker was here"] : (stryCov_9fa48("365"), []));
    const legalEntityOnChange = useCallback(stryMutAct_9fa48("366") ? () => undefined : (stryCov_9fa48("366"), () => setIsLegalEntitySaved(stryMutAct_9fa48("367") ? true : (stryCov_9fa48("367"), false))), stryMutAct_9fa48("368") ? ["Stryker was here"] : (stryCov_9fa48("368"), []));
    useEffect(() => {
      if (stryMutAct_9fa48("369")) {
        {}
      } else {
        stryCov_9fa48("369");
        setAccountInformation(creator.accountInformation as unknown as AccountInformationWithLocalizedDate);
        setRegistrationDate(creator.formattedRegistrationDate(stryMutAct_9fa48("372") ? router.locale && "" : stryMutAct_9fa48("371") ? false : stryMutAct_9fa48("370") ? true : (stryCov_9fa48("370", "371", "372"), router.locale || (stryMutAct_9fa48("373") ? "Stryker was here!" : (stryCov_9fa48("373"), "")))));
        setMailingAddress(creator.mailingAddress as unknown as MailingAddressPayload);
        setAdditionalInformation(creator.additionalInformation as AdditionalInformationPayload);
        setLegalEntity(creator.legalInformation as unknown as LegalInformationPayload);
      }
    }, stryMutAct_9fa48("374") ? [] : (stryCov_9fa48("374"), [creator]));
    useEffect(() => {
      if (stryMutAct_9fa48("375")) {
        {}
      } else {
        stryCov_9fa48("375");
        if (stryMutAct_9fa48("378") ? isError && isValidationError : stryMutAct_9fa48("377") ? false : stryMutAct_9fa48("376") ? true : (stryCov_9fa48("376", "377", "378"), isError || isValidationError)) {
          if (stryMutAct_9fa48("379")) {
            {}
          } else {
            stryCov_9fa48("379");
            errorToast(<Toast header={unhandledError} content={isError ? isError : toastContent(validationErrors)} />, stryMutAct_9fa48("380") ? {} : (stryCov_9fa48("380"), {
              onClose: stryMutAct_9fa48("381") ? () => undefined : (stryCov_9fa48("381"), () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch))
            }));
          }
        }
      }
    }, stryMutAct_9fa48("382") ? [] : (stryCov_9fa48("382"), [isError, isValidationError, stableDispatch, unhandledError]));
    const onChangeAsMailingAddress = useCallback((data, isChecked: boolean) => {
      if (stryMutAct_9fa48("383")) {
        {}
      } else {
        stryCov_9fa48("383");
        if (stryMutAct_9fa48("385") ? false : stryMutAct_9fa48("384") ? true : (stryCov_9fa48("384", "385"), isChecked)) setLegalEntity(stryMutAct_9fa48("386") ? {} : (stryCov_9fa48("386"), {
          ...data,
          ...mailingAddress
        }));
      }
    }, stryMutAct_9fa48("387") ? [] : (stryCov_9fa48("387"), [mailingAddress]));
    return stryMutAct_9fa48("390") ? !legalEntity && <div className="loader">
        <Loading />
      </div> && legalEntity && <div className="profile-information">
        {accountInformation && <ProfileCard labels={{
        profileLabels: infoLabels.profileLabels,
        profilePictureLabels: infoLabels.profilePictureLabels,
        buttons: buttons,
        creatorSince: infoLabels.creatorSince
      }} user={user} registrationDate={registrationDate || ""} accountInformation={accountInformation} data={layout?.toolTip?.badge} stableDispatch={stableDispatch} DEFAULT_AVATAR_IMAGE={DEFAULT_AVATAR_IMAGE} creatorsClient={creatorsClient} router={router} />}
        {rules && accountInformation && <Form key="personal" mode="onChange" onSubmit={onSubmitAccountInformation}>
            <PersonalInformationForm {...{
          infoLabels,
          rules,
          accountInformation,
          buttons,
          onChange: accountInformationOnChange,
          isSaved: isAccountInformationSaved,
          isLoader: pendingAccUpd,
          router
        }} />
          </Form>}
        {allCountries && mailingAddress && rules && <Form key="mailing" mode="onChange" onSubmit={onSubmitMailingAddress}>
            <MailingAddressForm {...{
          infoLabels,
          rules,
          mailingAddress,
          allCountries,
          buttons,
          onChange: mailingAddressOnChange,
          isSaved: isMailingAddressSaved,
          isLoader: pendingMailAddUpd
        }} />
          </Form>}
        {countries && legalEntity && rules && <Form key="legalEntity" mode="onChange" revalidate="onChange" onSubmit={onSubmitLegalEntity}>
            <LegalEntityForm {...{
          infoLabels,
          rules,
          legalEntity,
          countries,
          buttons,
          onChangeAsMailingAddress,
          onChange: legalEntityOnChange,
          isSaved: isLegalEntitySaved,
          isLoader: pendingLegAddUpd
        }} />
          </Form>}
        {hardwarePartners && additionalInformation && rules && <Form key="miscellaneous" mode="onChange" onSubmit={onSubmitAdditionalInformation}>
            <MiscellaneousForm {...{
          infoLabels,
          rules,
          additionalInformation,
          hardwarePartners,
          buttons,
          onChange: additionalInformationOnChange,
          isSaved: isAdditionalInformationSaved,
          isLoader: pendingAddInfoUpd
        }} />
          </Form>}
      </div> : stryMutAct_9fa48("389") ? false : stryMutAct_9fa48("388") ? true : (stryCov_9fa48("388", "389", "390"), (stryMutAct_9fa48("392") ? !legalEntity || <div className="loader">
        <Loading />
      </div> : stryMutAct_9fa48("391") ? false : (stryCov_9fa48("391", "392"), (stryMutAct_9fa48("393") ? legalEntity : (stryCov_9fa48("393"), !legalEntity)) && <div className="loader">
        <Loading />
      </div>)) || (stryMutAct_9fa48("395") ? legalEntity || <div className="profile-information">
        {accountInformation && <ProfileCard labels={{
        profileLabels: infoLabels.profileLabels,
        profilePictureLabels: infoLabels.profilePictureLabels,
        buttons: buttons,
        creatorSince: infoLabels.creatorSince
      }} user={user} registrationDate={registrationDate || ""} accountInformation={accountInformation} data={layout?.toolTip?.badge} stableDispatch={stableDispatch} DEFAULT_AVATAR_IMAGE={DEFAULT_AVATAR_IMAGE} creatorsClient={creatorsClient} router={router} />}
        {rules && accountInformation && <Form key="personal" mode="onChange" onSubmit={onSubmitAccountInformation}>
            <PersonalInformationForm {...{
          infoLabels,
          rules,
          accountInformation,
          buttons,
          onChange: accountInformationOnChange,
          isSaved: isAccountInformationSaved,
          isLoader: pendingAccUpd,
          router
        }} />
          </Form>}
        {allCountries && mailingAddress && rules && <Form key="mailing" mode="onChange" onSubmit={onSubmitMailingAddress}>
            <MailingAddressForm {...{
          infoLabels,
          rules,
          mailingAddress,
          allCountries,
          buttons,
          onChange: mailingAddressOnChange,
          isSaved: isMailingAddressSaved,
          isLoader: pendingMailAddUpd
        }} />
          </Form>}
        {countries && legalEntity && rules && <Form key="legalEntity" mode="onChange" revalidate="onChange" onSubmit={onSubmitLegalEntity}>
            <LegalEntityForm {...{
          infoLabels,
          rules,
          legalEntity,
          countries,
          buttons,
          onChangeAsMailingAddress,
          onChange: legalEntityOnChange,
          isSaved: isLegalEntitySaved,
          isLoader: pendingLegAddUpd
        }} />
          </Form>}
        {hardwarePartners && additionalInformation && rules && <Form key="miscellaneous" mode="onChange" onSubmit={onSubmitAdditionalInformation}>
            <MiscellaneousForm {...{
          infoLabels,
          rules,
          additionalInformation,
          hardwarePartners,
          buttons,
          onChange: additionalInformationOnChange,
          isSaved: isAdditionalInformationSaved,
          isLoader: pendingAddInfoUpd
        }} />
          </Form>}
      </div> : stryMutAct_9fa48("394") ? false : (stryCov_9fa48("394", "395"), legalEntity && <div className="profile-information">
        {stryMutAct_9fa48("398") ? accountInformation || <ProfileCard labels={{
        profileLabels: infoLabels.profileLabels,
        profilePictureLabels: infoLabels.profilePictureLabels,
        buttons: buttons,
        creatorSince: infoLabels.creatorSince
      }} user={user} registrationDate={registrationDate || ""} accountInformation={accountInformation} data={layout?.toolTip?.badge} stableDispatch={stableDispatch} DEFAULT_AVATAR_IMAGE={DEFAULT_AVATAR_IMAGE} creatorsClient={creatorsClient} router={router} /> : stryMutAct_9fa48("397") ? false : stryMutAct_9fa48("396") ? true : (stryCov_9fa48("396", "397", "398"), accountInformation && <ProfileCard labels={stryMutAct_9fa48("399") ? {} : (stryCov_9fa48("399"), {
        profileLabels: infoLabels.profileLabels,
        profilePictureLabels: infoLabels.profilePictureLabels,
        buttons: buttons,
        creatorSince: infoLabels.creatorSince
      })} user={user} registrationDate={stryMutAct_9fa48("402") ? registrationDate && "" : stryMutAct_9fa48("401") ? false : stryMutAct_9fa48("400") ? true : (stryCov_9fa48("400", "401", "402"), registrationDate || (stryMutAct_9fa48("403") ? "Stryker was here!" : (stryCov_9fa48("403"), "")))} accountInformation={accountInformation} data={stryMutAct_9fa48("405") ? layout.toolTip?.badge : stryMutAct_9fa48("404") ? layout?.toolTip.badge : (stryCov_9fa48("404", "405"), layout?.toolTip?.badge)} stableDispatch={stableDispatch} DEFAULT_AVATAR_IMAGE={DEFAULT_AVATAR_IMAGE} creatorsClient={creatorsClient} router={router} />)}
        {stryMutAct_9fa48("408") ? rules && accountInformation || <Form key="personal" mode="onChange" onSubmit={onSubmitAccountInformation}>
            <PersonalInformationForm {...{
          infoLabels,
          rules,
          accountInformation,
          buttons,
          onChange: accountInformationOnChange,
          isSaved: isAccountInformationSaved,
          isLoader: pendingAccUpd,
          router
        }} />
          </Form> : stryMutAct_9fa48("407") ? false : stryMutAct_9fa48("406") ? true : (stryCov_9fa48("406", "407", "408"), (stryMutAct_9fa48("410") ? rules || accountInformation : stryMutAct_9fa48("409") ? true : (stryCov_9fa48("409", "410"), rules && accountInformation)) && <Form key="personal" mode="onChange" onSubmit={onSubmitAccountInformation}>
            <PersonalInformationForm {...stryMutAct_9fa48("411") ? {} : (stryCov_9fa48("411"), {
          infoLabels,
          rules,
          accountInformation,
          buttons,
          onChange: accountInformationOnChange,
          isSaved: isAccountInformationSaved,
          isLoader: pendingAccUpd,
          router
        })} />
          </Form>)}
        {stryMutAct_9fa48("414") ? allCountries && mailingAddress && rules || <Form key="mailing" mode="onChange" onSubmit={onSubmitMailingAddress}>
            <MailingAddressForm {...{
          infoLabels,
          rules,
          mailingAddress,
          allCountries,
          buttons,
          onChange: mailingAddressOnChange,
          isSaved: isMailingAddressSaved,
          isLoader: pendingMailAddUpd
        }} />
          </Form> : stryMutAct_9fa48("413") ? false : stryMutAct_9fa48("412") ? true : (stryCov_9fa48("412", "413", "414"), (stryMutAct_9fa48("416") ? allCountries && mailingAddress || rules : stryMutAct_9fa48("415") ? true : (stryCov_9fa48("415", "416"), (stryMutAct_9fa48("418") ? allCountries || mailingAddress : stryMutAct_9fa48("417") ? true : (stryCov_9fa48("417", "418"), allCountries && mailingAddress)) && rules)) && <Form key="mailing" mode="onChange" onSubmit={onSubmitMailingAddress}>
            <MailingAddressForm {...stryMutAct_9fa48("419") ? {} : (stryCov_9fa48("419"), {
          infoLabels,
          rules,
          mailingAddress,
          allCountries,
          buttons,
          onChange: mailingAddressOnChange,
          isSaved: isMailingAddressSaved,
          isLoader: pendingMailAddUpd
        })} />
          </Form>)}
        {stryMutAct_9fa48("422") ? countries && legalEntity && rules || <Form key="legalEntity" mode="onChange" revalidate="onChange" onSubmit={onSubmitLegalEntity}>
            <LegalEntityForm {...{
          infoLabels,
          rules,
          legalEntity,
          countries,
          buttons,
          onChangeAsMailingAddress,
          onChange: legalEntityOnChange,
          isSaved: isLegalEntitySaved,
          isLoader: pendingLegAddUpd
        }} />
          </Form> : stryMutAct_9fa48("421") ? false : stryMutAct_9fa48("420") ? true : (stryCov_9fa48("420", "421", "422"), (stryMutAct_9fa48("424") ? countries && legalEntity || rules : stryMutAct_9fa48("423") ? true : (stryCov_9fa48("423", "424"), (stryMutAct_9fa48("426") ? countries || legalEntity : stryMutAct_9fa48("425") ? true : (stryCov_9fa48("425", "426"), countries && legalEntity)) && rules)) && <Form key="legalEntity" mode="onChange" revalidate="onChange" onSubmit={onSubmitLegalEntity}>
            <LegalEntityForm {...stryMutAct_9fa48("427") ? {} : (stryCov_9fa48("427"), {
          infoLabels,
          rules,
          legalEntity,
          countries,
          buttons,
          onChangeAsMailingAddress,
          onChange: legalEntityOnChange,
          isSaved: isLegalEntitySaved,
          isLoader: pendingLegAddUpd
        })} />
          </Form>)}
        {stryMutAct_9fa48("430") ? hardwarePartners && additionalInformation && rules || <Form key="miscellaneous" mode="onChange" onSubmit={onSubmitAdditionalInformation}>
            <MiscellaneousForm {...{
          infoLabels,
          rules,
          additionalInformation,
          hardwarePartners,
          buttons,
          onChange: additionalInformationOnChange,
          isSaved: isAdditionalInformationSaved,
          isLoader: pendingAddInfoUpd
        }} />
          </Form> : stryMutAct_9fa48("429") ? false : stryMutAct_9fa48("428") ? true : (stryCov_9fa48("428", "429", "430"), (stryMutAct_9fa48("432") ? hardwarePartners && additionalInformation || rules : stryMutAct_9fa48("431") ? true : (stryCov_9fa48("431", "432"), (stryMutAct_9fa48("434") ? hardwarePartners || additionalInformation : stryMutAct_9fa48("433") ? true : (stryCov_9fa48("433", "434"), hardwarePartners && additionalInformation)) && rules)) && <Form key="miscellaneous" mode="onChange" onSubmit={onSubmitAdditionalInformation}>
            <MiscellaneousForm {...stryMutAct_9fa48("435") ? {} : (stryCov_9fa48("435"), {
          infoLabels,
          rules,
          additionalInformation,
          hardwarePartners,
          buttons,
          onChange: additionalInformationOnChange,
          isSaved: isAdditionalInformationSaved,
          isLoader: pendingAddInfoUpd
        })} />
          </Form>)}
      </div>)));
  }
});