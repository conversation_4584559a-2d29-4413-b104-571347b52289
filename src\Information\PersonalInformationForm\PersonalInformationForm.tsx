import { Controller, useFormContext } from "react-hook-form";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import ProfileFormButtons from "../ProfileFormButtons/ProfileFormButtons";
import FormTitle from "../FormTitle/FormTitle";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { DateInput, Input } from "@eait-playerexp-cn/core-ui-kit";
import { isAdult } from "../../utils";
import { ButtonLabels, InfoLabels, Overwrite } from "../types";
import { AccountInformationResponse } from "@eait-playerexp-cn/creator-types";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { Message, ValidationRule } from "react-hook-form";

type AccountInformation = Overwrite<
  AccountInformationResponse,
  {
    dateOfBirth: LocalizedDate;
  }
>;
export type Required = {
  required: Message | ValidationRule<boolean>;
};
export type MaxLength = {
  value: number;
  message: string;
};
export type MaxLengthRequiredValidate = Required & {
  maxLength: MaxLength;
  validate: (value: Date) => boolean | string;
};
export type MaxLengthRequired = Required & {
  maxLength: MaxLength;
};
export type PersonalInformationFormRules = {
  firstName: MaxLengthRequired;
  lastName: MaxLengthRequired;
  dateOfBirth: MaxLengthRequiredValidate;
};
export type PersonalInformationFormLabels = {
  infoLabels: InfoLabels;
  buttons: ButtonLabels;
};
interface PersonalInformationFormProps {
  labels: PersonalInformationFormLabels;
  accountInformation: AccountInformation;
  onChange: () => void;
  isSaved: boolean;
  isLoader: boolean;
  locale: string;
}

export const REGEX_PATTERN = {
  EMAIL:
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
};

export const validationConfig = {
  MAXLENGTH: 50,
  MAXLENGTH_20: 20,
  MAXLENGTH_40: 40,
  MAXLENGTH_80: 80,
  MAXLENGTH_255: 255,
  DATE_MAXLENGTH: 10,
  regex: {
    EMAIL: REGEX_PATTERN.EMAIL
  },
  MAX_FILE_SIZE: *********
};

const personalInformationRules = (labels: InfoLabels): PersonalInformationFormRules => {
  return {
    firstName: {
      required: labels.messages.firstName,
      maxLength: { value: validationConfig.MAXLENGTH_40, message: labels.messages.firstNameTooLong }
    },
    lastName: {
      required: labels.messages.lastName,
      maxLength: { value: validationConfig.MAXLENGTH_80, message: labels.messages.lastNameTooLong }
    },
    dateOfBirth: {
      required: labels.messages.dateOfBirth,
      maxLength: { value: validationConfig.DATE_MAXLENGTH, message: labels.messages.dateOfBirthInvalid },
      validate: (value) => {
        if (!LocalizedDate.isValid(value)) {
          return labels.messages.dateOfBirthInvalid;
        }
        if (isAdult(value.toString())) {
          return labels.messages.ageMustBe18OrOlder;
        }
        return true;
      }
    }
  };
};

const PersonalInformationForm = ({
  labels,
  accountInformation,
  onChange,
  isSaved = false,
  isLoader,
  locale
}: PersonalInformationFormProps) => {
  const methods = useFormContext();
  const { control, setValue, setError } = methods;
  const { infoLabels, buttons } = labels;
  const [isEdit, setIsEdit] = useState(false);
  const { success: successToast } = useToast();
  const rules = useMemo(() => personalInformationRules(infoLabels), [infoLabels]);
  const timeToDisplay = Math.min(Math.max(infoLabels.success.personalInformation.length * 50, 2000), 7000);
  const onEditChange = useCallback(
    (isChecked: boolean) => {
      setIsEdit(isChecked);
      if (onChange) onChange();
    },
    [isEdit]
  );

  useEffect(() => {
    if (isEdit && isSaved) setIsEdit(false);
  }, [isSaved]);

  return (
    <>
      {isSaved &&
        isEdit &&
        successToast(
          <Toast
            header={infoLabels.success.updatedInformationHeader}
            content={infoLabels.success.personalInformation}
          />,
          {
            autoClose: timeToDisplay
          }
        )}

      <div className="personal-information">
        <div className="form-sub-title-and-action">
          <FormTitle subTitle={infoLabels.personalInformation} />
          <ProfileFormButtons {...{ buttons, action: onEditChange, isSaved, isLoader }} />
        </div>

        <div className="personal-field-title">{infoLabels.labels.firstName}</div>

        <div className="personal-field">
          {!isEdit && accountInformation.firstName}
          {isEdit && (
            <Controller
              control={control}
              name="firstName"
              rules={rules.firstName}
              defaultValue={accountInformation.firstName}
              render={({ field, fieldState: { error } }) => (
                <Input
                  id="profile-personal-information-firstName"
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  placeholder={infoLabels.labels.firstName}
                  ariaLabel={infoLabels.labels.firstName}
                />
              )}
            />
          )}
        </div>

        <div className="personal-field-title">{infoLabels.labels.lastName}</div>
        <div className="personal-field">
          {!isEdit && accountInformation.lastName}
          {isEdit && (
            <Controller
              control={control}
              name="lastName"
              rules={rules.lastName}
              defaultValue={accountInformation.lastName}
              render={({ field, fieldState: { error } }) => (
                <Input
                  id="profile-personal-information-lastName"
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  placeholder={infoLabels.labels.lastName}
                  ariaLabel={infoLabels.labels.lastName}
                />
              )}
            />
          )}
        </div>

        <div className="personal-field-title">{infoLabels.labels.EAID}</div>
        <div className="personal-field">{accountInformation.defaultGamerTag}</div>

        <div className="personal-field-title">{infoLabels.labels.EAEmail}</div>
        <div className="personal-field">{accountInformation.originEmail}</div>

        <div className="personal-field-title">{infoLabels.labels.dateOfBirth}</div>
        <div className="personal-field">
          {!isEdit && accountInformation.dateOfBirth.format("MMMM D, YYYY", locale)}
          {isEdit && (
            <Controller
              control={control}
              name="dateOfBirth"
              rules={rules.dateOfBirth}
              defaultValue={accountInformation.dateOfBirth.toDate()}
              render={({ field, fieldState: { error } }) => (
                <DateInput
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  placeholder={infoLabels.labels.dateOfBirth}
                  locale={locale}
                  maxDate={new Date()}
                  title={infoLabels.header.calendar}
                  cancelText={buttons.cancel}
                  okText={buttons.ok}
                  onCancel={(date) => {
                    if (isAdult(date.toString())) {
                      setError(
                        "dateOfBirth",
                        { type: "manual", message: infoLabels.messages.ageMustBe18OrOlder },
                        { shouldFocus: true }
                      );
                    } else {
                      setError("dateOfBirth", null);
                    }
                    setValue("dateOfBirth", date);
                  }}
                />
              )}
            />
          )}
        </div>
      </div>
    </>
  );
};

export default memo(PersonalInformationForm);
