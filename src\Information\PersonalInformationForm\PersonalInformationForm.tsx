import { Controller, useFormContext } from "react-hook-form";
import React, { memo, useCallback, useEffect, useState } from "react";
import ProfileFormAction from "../ProfileFormAction/ProfileFormAction";
import FormTitle from "../FormTitle/FormTitle";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { DateInput, Input } from "@eait-playerexp-cn/core-ui-kit";
import { isAdult } from "../../utils";
import { ButtonLabels, InfoLabels, Overwrite } from "../types";
import { NextRouter } from "next/router";
import { AccountInformationResponse } from "@eait-playerexp-cn/creator-types";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { CreatorFormRules } from "@src/utils/FormRules/CreatorForm";

type AccountInformation = Overwrite<AccountInformationResponse, {
  dateOfBirth: LocalizedDate
}>;

interface PersonalInformationFormProps {
  infoLabels: InfoLabels;
  rules: CreatorFormRules;
  accountInformation: AccountInformation;
  buttons: ButtonLabels;
  onChange: () => void;
  isSaved: boolean;
  isLoader: boolean;
  router: NextRouter;
}

const PersonalInformationForm = ({
  infoLabels,
  rules,
  accountInformation,
  buttons,
  onChange,
  isSaved = false,
  isLoader,
  router
}: PersonalInformationFormProps) => {
  const methods = useFormContext();
  const { control, setValue, setError } = methods;
  const [isEdit, setIsEdit] = useState(false);
  const { success: successToast } = useToast();
  const timeToDisplay = Math.min(Math.max(infoLabels.success.personalInformation.length * 50, 2000), 7000);
  const onEditChange = useCallback(
    (isChecked: boolean) => {
      setIsEdit(isChecked);
      if(onChange) onChange();
    },
    [isEdit]
  );

  useEffect(() => {
    if(isEdit && isSaved) setIsEdit(false);
  }, [isSaved]);

  return (
    <>
      {isSaved && isEdit &&
        successToast(
          <Toast
            header={infoLabels.success.updatedInformationHeader}
            content={infoLabels.success.personalInformation}
          />,
          {
            autoClose: timeToDisplay
          }
        )}

      <div className="personal-information">
        <div className="form-sub-title-and-action">
          <FormTitle subTitle={infoLabels.personalInformation} />
          <ProfileFormAction {...{ buttons, action: onEditChange, isSaved, isLoader }} />
        </div>

        <div className="personal-field-title">{infoLabels.labels.firstName}</div>

        <div className="personal-field">
          {!isEdit && accountInformation.firstName}
          {isEdit && (
            <Controller
              control={control}
              name="firstName"
              rules={rules.firstName}
              defaultValue={accountInformation.firstName}
              render={({ field, fieldState: { error } }) => (
                <Input
                  id= "profile-personal-information-firstName"
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  placeholder={infoLabels.labels.firstName}
                  ariaLabel={infoLabels.labels.firstName}
                />
              )}
            />
          )}
        </div>

        <div className="personal-field-title">{infoLabels.labels.lastName}</div>
        <div className="personal-field">
          {!isEdit && accountInformation.lastName}
          {isEdit && (
            <Controller
              control={control}
              name="lastName"
              rules={rules.lastName}
              defaultValue={accountInformation.lastName}
              render={({ field, fieldState: { error } }) => (
                <Input
                  id= "profile-personal-information-lastName"
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  placeholder={infoLabels.labels.lastName}
                  ariaLabel={infoLabels.labels.lastName}
                />
              )}
            />
          )}
        </div>

        <div className="personal-field-title">{infoLabels.labels.EAID}</div>
        <div className="personal-field">{accountInformation.defaultGamerTag}</div>

        <div className="personal-field-title">{infoLabels.labels.EAEmail}</div>
        <div className="personal-field">{accountInformation.originEmail}</div>

        <div className="personal-field-title">{infoLabels.labels.dateOfBirth}</div>
        <div className="personal-field">
          {!isEdit && accountInformation.dateOfBirth.format("MMMM D, YYYY", router.locale)}
          {isEdit && (
            <Controller
              control={control}
              name="dateOfBirth"
              rules={rules.dateOfBirth}
              defaultValue={accountInformation.dateOfBirth.toDate()}
              render={({ field, fieldState: { error } }) => (
                <DateInput
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  placeholder={infoLabels.labels.dateOfBirth}
                  locale={router.locale}
                  maxDate={new Date()}
                  title={infoLabels.header.calendar}
                  cancelText={buttons.cancel}
                  okText={buttons.ok}
                  onCancel={(date) => {
                    if (isAdult(date.toString())) {
                      setError(
                        "dateOfBirth",
                        { type: "manual", message: infoLabels.messages.ageMustBe18OrOlder },
                        { shouldFocus: true }
                      );
                    } else {
                      setError("dateOfBirth", null);
                    }
                    setValue("dateOfBirth", date);
                  }}
                />
              )}
            />
          )}
        </div>
      </div>
    </>
  );
};

export default memo(PersonalInformationForm);
