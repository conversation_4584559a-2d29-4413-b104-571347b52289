{"name": "@eait-playerexp-cn/profile-ui", "version": "0.1.0", "description": "UI Components for the Profile Micro Frontend", "main": "dist/index.js", "files": ["dist", "README.md"], "scripts": {"build": "tsup && npm run build:copy-files", "build:copy-files": "copyfiles -e src/styles/storybook.css -u 1 \"src/**/*.css\" dist/", "commitlint": "commitlint --edit", "format": "prettier . --write", "format:check": "prettier . --check", "lint": "eslint \"src/**/*.{ts,tsx}\" --fix", "lint:check": "eslint \"src/**/*.{ts,tsx}\"", "lint-staged": "lint-staged", "prepare": "husky", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "test": "jest --runInBand=true --cache", "test:coverage": "jest --runInBand=true --coverage --cache", "test:mutation": "stryker run", "test:types": "tsc --noEmit"}, "peerDependencies": {"@eait-playerexp-cn/client-kernel": "^1.0.0", "@eait-playerexp-cn/core-ui-kit": "8.26.8", "@eait-playerexp-cn/http": "1.0.0", "@eait-playerexp-cn/identity-test-fixtures": "1.1.0", "@eait-playerexp-cn/creators-http-client": "2.0.1", "@eait-playerexp-cn/creator-test-fixtures": "1.6.0", "@eait-playerexp-cn/http-client": "1.4.3", "@eait-playerexp-cn/metadata-http-client": "1.0.0", "@eait-playerexp-cn/metadata-test-fixtures": "1.0.1", "@eait-playerexp-cn/metadata-types": "1.0.1", "@eait-playerexp-cn/creator-types": "^1.3.0", "@sentry/nextjs": "7.119.2", "next": "^14.2.6", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hook-form": "^7.53.1"}, "devDependencies": {"@commitlint/cli": "19.2.2", "@commitlint/config-conventional": "19.2.2", "@storybook/addon-essentials": "8.3.5", "@storybook/addon-interactions": "8.3.5", "@storybook/addon-links": "8.3.5", "@storybook/addon-styling-webpack": "1.0.0", "@storybook/blocks": "8.3.5", "@storybook/builder-webpack5": "8.3.5", "@storybook/preset-create-react-app": "8.3.5", "@storybook/react": "8.3.5", "@storybook/react-webpack5": "8.3.5", "@storybook/test": "7.6.8", "@stryker-mutator/core": "8.5.0", "@stryker-mutator/jest-runner": "8.5.0", "@stryker-mutator/typescript-checker": "8.5.0", "@tailwindcss/typography": "0.5.4", "@testing-library/jest-dom": "6.4.8", "@testing-library/react": "16.0.0", "@testing-library/user-event": "14.5.2", "@types/jest": "29.5.12", "@types/jest-axe": "3.5.7", "@typescript-eslint/eslint-plugin": "8.7.0", "autoprefixer": "10.4.20", "copyfiles": "2.4.1", "cssnano": "7.0.5", "eslint": "9.11.1", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "husky": "9.1.6", "jest": "29.7.0", "jest-axe": "9.0.0", "jest-environment-jsdom": "29.7.0", "jest-html-reporters": "3.1.7", "lint-staged": "15.2.10", "postcss": "8.4.41", "postcss-import": "16.1.0", "prettier": "3.3.3", "prettier-plugin-tailwindcss": "0.6.6", "storybook": "8.3.5", "style-loader": "4.0.0", "tailwindcss": "3.4.10", "ts-jest": "29.2.5", "tsup": "8.3.0", "typescript": "5.5.3"}, "publishConfig": {"access": "public"}, "lint-staged": {"*.{ts,tsx}": ["eslint --quiet --cache --fix", "prettier --write", "jest --bail --findRelatedTests --passWithNoTests"], "*.{js,jsx,json,css}": ["prettier --write"]}}