---
currentMenu: installation
---

# Installation

## Using the module in a project

To use this npm package, add an `.npmrc` file to the project that depends on the module.

```bash
echo @eait-playerexp-cn:registry=https://gitlab.ea.com/api/v4/packages/npm/ >> .npmrc
```

In order to access the GitLab npm Registry, you'll need a [personal access token](https://gitlab.ea.com/-/profile/personal_access_tokens) with the `read_registry` and `api` grant.

Add the token for the scoped packages URL. This will allow you to download
`@eait-playerexp-cn/` packages from private projects.

```bash
npm config set -- '//gitlab.ea.com/api/v4/packages/npm/:_authToken' "<your_token>"
```

Then, install the module:

```bash
npm i @eait-playerexp-cn/component-library --save --save-exact
```

## Set up for local development

### Prerequisites

You'll need the following software in your local development environment

- [Node and npm](https://nodejs.org/en/download/package-manager/)
- [Make](https://www.gnu.org/software/make/manual/make.html)
- [Docker](https://docs.docker.com/get-docker/)

### Getting started

Run the command below to install all the packages required by this component library.

```bash
make bootstrap
```

The above command will also create a default `.env` file, for whenever you need to update this project's documentation
