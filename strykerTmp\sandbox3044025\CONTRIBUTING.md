---
currentMenu: contributing
---

# Contributing

## Workflow

1. Clone the project. `<NAME_EMAIL>:eait-playerexp-cn/creator-network-frontend/frontend-templates/component-library.git`
1. Create a branch for your task `CRNE-XXXX` it should match the identifier of the Jira task
1. Make your bug fix or feature addition
1. Tests your changes locally
1. Send a merge request against the `main` branch

**All Merge Requests must be reviewed**

## Conventions

- [Commit messages convention](https://eait-playerexp-cn.gitlab.ea.com/technical-documentation/docs/conventions/git-commits.html)

## How-to

- [Contribute Documentation](https://eait-playerexp-cn.gitlab.ea.com/technical-documentation/docs/how-tos/contribute-documentation.html)
- [Rebase a MR](https://eait-playerexp-cn.gitlab.ea.com/technical-documentation/docs/how-tos/rebasing.html)
  since all repositories are configured to use a [semi-linear Git history](https://docs.gitlab.com/ee/user/project/merge_requests/reviewing_and_managing_merge_requests.html#semi-linear-history-merge-requests)
- [Open an MR](https://eait-playerexp-cn.gitlab.ea.com/technical-documentation/docs/how-tos/opening-a-mr.html)
- [Review an MR](https://eait-playerexp-cn.gitlab.ea.com/technical-documentation/docs/how-tos/reviewing-a-mr.html)
