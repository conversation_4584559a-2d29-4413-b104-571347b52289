import { Button, edit, Icon } from "@eait-playerexp-cn/core-ui-kit";
import React, { memo, useCallback, useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";

const ProfileFormButtons = ({ buttons, action: onChange, isSaved = false, isLoader = false }) => {
  const { formState } = useFormContext();
  const [isEdit, setIsEdit] = useState(false);

  const onEdit = useCallback(() => {
    setIsEdit(true);
    if (onChange) onChange(true);
  }, [isEdit]);

  const onCancel = useCallback(() => {
    setIsEdit(false);
    if (onChange) onChange(false);
  }, [isEdit]);

  useEffect(() => {
    if (isEdit && isSaved) setIsEdit(false);
  }, [isSaved]);

  return (
    <div className="form-action">
      {!isEdit && (
        <Button variant="secondary" size="sm" onClick={onEdit}>
          <Icon icon={edit} /> &nbsp;
          {buttons.edit}
        </Button>
      )}
      {isEdit && (
        <>
          <Button variant="tertiary" size="sm" onClick={onCancel}>
            {buttons.cancel}
          </Button>
          &nbsp;
          <Button
            variant="primary"
            size="sm"
            type="submit"
            disabled={!formState.isValid || isLoader}
            spinner={isLoader}
          >
            {buttons.save}
          </Button>
        </>
      )}
    </div>
  );
};

export default memo(ProfileFormButtons);
