import { render, waitFor, screen, within } from "@testing-library/react";
import PersonalInformationForm from "./PersonalInformationForm";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import React from "react";
import { NextRouter } from "next/router";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { clearValueFor, enterValueFor } from "@src/Helpers/Forms";
import { FormProvider, useForm } from "react-hook-form";

// Mock react-hook-form for testing
const MockFormProvider = ({ children, defaultValues = {} }) => {
  const methods = useForm({
    defaultValues,
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("PersonalInformationForm", () => {
  const mockAccountInformation = {
    defaultGamerTag: "245902",
    nucleusId: *************,
    firstName: "<PERSON><PERSON>",
    lastName: "Nrus<PERSON><PERSON><PERSON>",
    originEmail: "<EMAIL>",
    dateOfBirth: {
      millisecondsEpoch: ************,
      format: () => "July 30, 1995",
      toDate: () => new Date(************)
    } as unknown as LocalizedDate,
    needsMigration: false,
    payable: true,
    flagged: false,
    disabled: false,
    preferredName: "Mouli",
    preferredPronouns: null
  };

  const mockProps = {
    infoLabels: {
      personalInformation: "Personal Information",
      mailingAddress: "Mailing Address",
      miscellaneous: "Miscellaneous",
      creatorSince: "Creator Since",
      legalEntityType: "Legal Entity & Address",
      legalEntityDescription:
        "This info will also be used for payment purposes. Changing your entity type or address may require you to update your payment/tax info in the Payment Information page.",
      header: {
        calendar: "Calender"
      },
      success: {
        updatedInformationHeader: "Information update successful",
        personalInformation: "You have successfully updated your Personal Information.",
        mailingAddress: "You have successfully updated your Mailing Address.",
        miscellaneous: "You have successfully updated your Miscellaneous Information.",
        legalEntityType: "You have successfully updated your Legal Entity Information."
      },
      labels: {
        none: "None",
        firstName: "First Name",
        lastName: "Last Name",
        EAID: "Electronic Arts ID",
        EAEmail: "Electronic Arts Account Email",
        dateOfBirth: "Date of Birth",
        country: "Country/Region",
        street: "Street",
        city: "City",
        state: "State or Province",
        zipCode: "Zip Code or Postal Code",
        tShirtSize: "T-Shirt Size",
        hardwarePartners: "Hardware Partners",
        entityType: "Entity Type",
        individual: "Individual",
        business: "Business",
        businessName: "Name of Business",
        legalAddressAsMailingAddress: "Address is the same as my mailing address."
      },
      messages: {
        firstName: "First Name is required",
        firstNameTooLong: "First Name is too long",
        lastName: "Last Name is required",
        lastNameTooLong: "Last Name is too long",
        dateOfBirth: "Date of Birth is required",
        dateOfBirthInvalid: "Date of Birth is invalid",
        ageMustBe18OrOlder: "Must be 18 or older",
        country: "Country/Region is required",
        street: "Street is required",
        streetTooLong: "Street is too long",
        city: "City is required",
        cityTooLong: "City is too long",
        state: "State or Province is required",
        stateTooLong: "State is too long",
        zipCode: "Zip Code or Postal Code is required",
        zipCodeTooLong: "Zip Code or Postal Code is too long",
        primaryPlatform: "Primary platform is required",
        tShirtSize: "T-Shirt Size is required",
        hardwarePartners: "Hardware Partners is required",
        entityType: "Entity Type is required",
        businessName: "Business Name is required",
        businessNameTooLong: "Business Name is too long",
        email: "Email Address is required",
        emailTooLong: "Email Address is too long",
        emailInvalid: "Email Address is invalid",
        url: "URL is required",
        invalidUrl: "URL provided is invalid",
        duplicateUrl: "Duplicate URLs not allowed",
        urlScanFailed: "You cannot submit content from this website or domain",
        followersMaxLength: "Maximum 18 digits allowed"
      },
      profilePictureLabels: {
        title: "Change My Avatar",
        message: "Select an image from your computer (JPEG, PNG or GIF). Image should be square and less than 1MB.",
        termsAndConditionsFirst:
          'Respect the rights of others. Only upload images that you own or that you have written permission to freely distribute. Like most other online services, your avatar will be visible to other users of the service and associated with your Electronic Arts ID, even if your profile is set to "no one can see." Please read our ',
        termsAndConditionsMiddle: "User Agreement",
        termsAndConditionsLast: "for more information.",
        avatarRequired: "Please select an image",
        avatarInvalid: "Please select valid image",
        avatarMoreThanLimit: "Image size should be less than 1MB"
      },
      info: {
        businessName: "Only if you are contracting under a business entity; otherwise, leave blank."
      },
      profileLabels: {
        updateAvatar: "Update Avatar"
      }
    },
    rules: {
      firstName: { required: "First Name is required" },
      lastName: { required: "Last Name is required" },
      dateOfBirth: { required: "Date of Birth is required" }
    },
    accountInformation: mockAccountInformation,
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close"
    },
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false,
    router: {
      query: {},
      locale: "en-us",
      pathname: "",
      push: jest.fn()
    } as unknown as NextRouter
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Rendering", () => {
    it("should render personal information form correctly", () => {
      render(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...mockProps} />
        </MockFormProvider>
      );

      expect(screen.getByText("Personal Information")).toBeInTheDocument();
      expect(screen.getByText("First Name")).toBeInTheDocument();
      expect(screen.getByText("Last Name")).toBeInTheDocument();
      expect(screen.getByText("Date of Birth")).toBeInTheDocument();
      expect(screen.getByText("Electronic Arts ID")).toBeInTheDocument();
      expect(screen.getByText("Electronic Arts Account Email")).toBeInTheDocument();
    });

    it("should display user information in read-only mode initially", () => {
      render(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...mockProps} />
        </MockFormProvider>
      );

      expect(screen.getByText("Mouli")).toBeInTheDocument();
      expect(screen.getByText("Nrusimhadri")).toBeInTheDocument();
      expect(screen.getByText("245902")).toBeInTheDocument();
      expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    });

    it("should show edit button initially", () => {
      render(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...mockProps} />
        </MockFormProvider>
      );

      expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    });
  });

  describe("Edit Mode", () => {
    it("should enable edit mode when edit button is clicked", async () => {
      render(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...mockProps} />
        </MockFormProvider>
      );

      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
      expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
    });

    it("should show form inputs in edit mode", async () => {
      render(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...mockProps} />
        </MockFormProvider>
      );

      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      expect(screen.getByDisplayValue("Mouli")).toBeInTheDocument();
      expect(screen.getByDisplayValue("Nrusimhadri")).toBeInTheDocument();
    });

    it("should call onChange when edit mode is activated", async () => {
      render(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...mockProps} />
        </MockFormProvider>
      );

      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      expect(mockProps.onChange).toHaveBeenCalled();
    });
  });

  describe("Form Validation", () => {
    it("should show validation error for empty first name", async () => {
      const propsWithValidation = {
        ...mockProps,
        rules: {
          firstName: { 
            required: "First Name is required",
            validate: (value) => value.trim() !== "" || "First Name is required"
          }
        }
      };

      render(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...propsWithValidation} />
        </MockFormProvider>
      );

      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      await clearValueFor(/^First Name/i);
      
      await waitFor(() => {
        expect(screen.getByText("First Name is required")).toBeInTheDocument();
      });
    });

    it("should show validation error for empty last name", async () => {
      const propsWithValidation = {
        ...mockProps,
        rules: {
          lastName: { 
            required: "Last Name is required",
            validate: (value) => value.trim() !== "" || "Last Name is required"
          }
        }
      };

      render(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...propsWithValidation} />
        </MockFormProvider>
      );

      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      await clearValueFor(/^Last Name/i);
      
      await waitFor(() => {
        expect(screen.getByText("Last Name is required")).toBeInTheDocument();
      });
    });
  });

  describe("Form Submission", () => {
    it("should allow editing and saving personal information", async () => {
      render(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...mockProps} />
        </MockFormProvider>
      );

      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      await clearValueFor(/^First Name/i);
      await enterValueFor(/^First Name/i, "Jane");

      const saveButton = screen.getByRole("button", { name: "Save" });
      expect(saveButton).toBeEnabled();
    });

    it("should show success toast when information is saved", async () => {
      const propsWithSaved = {
        ...mockProps,
        isSaved: true
      };

      const { unmount } = renderWithToast(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...propsWithSaved} />
        </MockFormProvider>
      );

      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      await waitFor(() => {
        expect(screen.getByText("Information update successful")).toBeInTheDocument();
        expect(screen.getByText("You have successfully updated your Personal Information.")).toBeInTheDocument();
      });

      unmount();
    });
  });

  describe("Cancel Functionality", () => {
    it("should exit edit mode when cancel button is clicked", async () => {
      render(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...mockProps} />
        </MockFormProvider>
      );

      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      const cancelButton = screen.getByRole("button", { name: "Cancel" });
      await userEvent.click(cancelButton);

      expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
      expect(screen.queryByRole("button", { name: "Cancel" })).not.toBeInTheDocument();
    });
  });

  describe("Loading State", () => {
    it("should show loading state when isLoader is true", async () => {
      const propsWithLoader = {
        ...mockProps,
        isLoader: true
      };

      render(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...propsWithLoader} />
        </MockFormProvider>
      );

      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      const saveButton = screen.getByRole("button", { name: "Save" });
      expect(saveButton).toBeDisabled();
    });
  });

  describe("Date of Birth Display", () => {
    it("should display formatted date of birth", () => {
      render(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...mockProps} />
        </MockFormProvider>
      );

      expect(screen.getByText("July 30, 1995")).toBeInTheDocument();
    });
  });

  describe("Component Lifecycle", () => {
    it("should reset form when accountInformation changes", () => {
      const { rerender } = render(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...mockProps} />
        </MockFormProvider>
      );

      const updatedAccountInformation = {
        ...mockAccountInformation,
        firstName: "Updated Name"
      };

      const updatedProps = {
        ...mockProps,
        accountInformation: updatedAccountInformation
      };

      rerender(
        <MockFormProvider defaultValues={updatedAccountInformation}>
          <PersonalInformationForm {...updatedProps} />
        </MockFormProvider>
      );

      expect(screen.getByText("Updated Name")).toBeInTheDocument();
    });

    it("should exit edit mode when isSaved becomes true", async () => {
      const { rerender } = render(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...mockProps} />
        </MockFormProvider>
      );

      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();

      const updatedProps = {
        ...mockProps,
        isSaved: true
      };

      rerender(
        <MockFormProvider defaultValues={mockAccountInformation}>
          <PersonalInformationForm {...updatedProps} />
        </MockFormProvider>
      );

      await waitFor(() => {
        expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
      });
    });
  });
});
