import { render, screen, waitFor } from "@testing-library/react";
import PersonalInformationForm from "./PersonalInformationForm";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import React from "react";
import { NextRouter } from "next/router";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { clearValueFor } from "@src/Helpers/Forms";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import Random from "@src/Factories/Random";
import formsRules from "@src/utils/FormRules/CreatorForm";
import { isAdult } from "@src/utils";
import { InformationPageLabels } from "@src/Translations/InformationPageLabels";

jest.mock("@src/utils", () => ({
  ...jest.requireActual("@src/utils"),
  isAdult: jest.fn(),
  onToastClose: jest.fn()
}));

const FormWrapper = ({ children }) => {
  const methods = useForm({
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("PersonalInformationForm", () => {
  const validDate = new Date(1995, 7, 30); // July 30, 1995
  const infoLabels = InformationPageLabels.infoLabels;
  const accountInformation = {
    defaultGamerTag: "245902",
    nucleusId: *************,
    firstName: Random.firstName(),
    lastName: Random.lastName(),
    originEmail: Random.email(),
    dateOfBirth: {
      millisecondsEpoch: ************,
      format: (_format: string, _locale?: string) => "July 30, 1995",
      toDate: () => validDate
    } as unknown as LocalizedDate,
    needsMigration: false,
    payable: true,
    flagged: false,
    disabled: false,
    preferredName: Random.firstName(),
    preferredPronouns: null
  };

  const personalInformationFormProps = {
    infoLabels,
    rules: formsRules.rules(infoLabels),
    accountInformation,
    buttons: InformationPageLabels.buttons,
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false,
    router: {
      query: {},
      locale: "en-us",
      pathname: "",
      push: jest.fn()
    } as unknown as NextRouter
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("shows values in view mode by default", () => {
    renderWithToast(
      <FormWrapper>
        <PersonalInformationForm {...personalInformationFormProps} />
      </FormWrapper>
    );

    expect(screen.getByText("First Name")).toBeInTheDocument();
    expect(screen.getByText(accountInformation.firstName)).toBeInTheDocument();
    expect(screen.getByText("Edit")).toBeInTheDocument();
  });

  it("switches to edit mode and shows input fields", async () => {
    renderWithToast(
      <FormWrapper>
        <PersonalInformationForm {...personalInformationFormProps} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: /edit/i }));

    expect(await screen.findByPlaceholderText("First Name")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /save/i })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /save/i })).toBeEnabled();
  });

  it("shows validation error when required fields are empty", async () => {
    render(
      <FormWrapper>
        <PersonalInformationForm {...{ ...personalInformationFormProps, isSaved: undefined }} />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: /edit/i }));

    await clearValueFor(/First Name/i);
    await clearValueFor(/Last Name/i);

    await waitFor(() => {
      expect(screen.getByText(infoLabels.messages.firstName)).toBeInTheDocument();
      expect(screen.getByText(infoLabels.messages.lastName)).toBeInTheDocument();
    });
  });

  it("shows toast on successful save", async () => {
    const propsWithSaved = {
      ...personalInformationFormProps,
      isSaved: true
    };
    const { unmount } = renderWithToast(
      <FormWrapper>
        <PersonalInformationForm {...propsWithSaved} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: /edit/i }));

    await waitFor(() => {
      expect(screen.getAllByText("Information update successful")[0]).toBeInTheDocument();
      expect(screen.getAllByText("You have successfully updated your Personal Information.")[0]).toBeInTheDocument();
    });

    unmount();
  });

  it("displays error if under 18", async () => {
    (isAdult as jest.Mock).mockReturnValue(true);
    renderWithToast(
      <FormWrapper>
        <PersonalInformationForm {...personalInformationFormProps} />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: /edit/i }));
    const dateInput = screen.getByPlaceholderText(/Date of Birth/i);
    const underageDate = new Date();
    underageDate.setFullYear(underageDate.getFullYear() - 10);

    await userEvent.clear(dateInput);
    await userEvent.type(dateInput, underageDate.toISOString().split("T")[0]);

    await waitFor(() => {
      expect(screen.getByText(infoLabels.messages.ageMustBe18OrOlder)).toBeInTheDocument();
    });
  });

  it("is accessible", async () => {
    const { container } = renderWithToast(
      <FormWrapper>
        <PersonalInformationForm {...personalInformationFormProps} />
      </FormWrapper>
    );

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
