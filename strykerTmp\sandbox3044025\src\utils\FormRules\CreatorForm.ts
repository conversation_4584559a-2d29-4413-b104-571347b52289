// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import validationConfig from "../validations/validationConfig";
import { isAdult, isObj, isString } from "..";
import { Message, ValidationRule } from "react-hook-form";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { Country } from "@eait-playerexp-cn/metadata-types";
export type Pattern = {
  value: RegExp;
  message: string;
};
export type MaxNumLength = {
  validate: (value: string) => void;
};
export type MaxLength = {
  value: number;
  message: string;
};
export type Required = {
  required: Message | ValidationRule<boolean>;
};
export type RequiredValidate = Required & {
  validate: (value: Country) => string | boolean;
};
export type Validate = {
  validate: (value: string) => string | boolean;
};
export type MaxLengthPatternRequired = Required & {
  maxLength: MaxLength;
  pattern: Pattern;
};
export type MaxLengthRequired = Required & {
  maxLength: MaxLength;
};
export type MaxLengthRequiredValidate = Required & {
  maxLength: MaxLength;
  validate: (value: Date) => boolean | string;
};
export type CreatorFormRules = {
  firstName: MaxLengthRequired;
  lastName: MaxLengthRequired;
  dateOfBirth: MaxLengthRequiredValidate;
  country: RequiredValidate;
  interestedCreatorCountry: RequiredValidate;
  url: Required;
  street: MaxLengthRequired;
  city: MaxLengthRequired;
  state: MaxLengthRequired;
  zipCode: MaxLengthRequired;
  tShirtSize: Required;
  entityType: Required;
  businessName: MaxLengthRequired;
  email: MaxLengthPatternRequired;
  followers: MaxNumLength;
};
export type CommunicationFormRules = {
  preferredEmail: MaxLengthPatternRequired;
  preferredPhoneNumber: MaxLengthRequired;
  contentLanguage: Required;
  language: Required;
};
export type PactSafeFormRules = {
  businessName: Required;
};
export type Labels = {
  messages: {
    firstNameTooLong: string;
    lastNameTooLong: string;
    street: string | ValidationRule<boolean>;
    streetTooLong: string;
    city: string | ValidationRule<boolean>;
    cityTooLong: string;
    state: string | ValidationRule<boolean>;
    stateTooLong: string;
    zipCode: string | ValidationRule<boolean>;
    zipCodeTooLong: string;
    tShirtSize: string | ValidationRule<boolean>;
    entityType: string | ValidationRule<boolean>;
    businessName: string | ValidationRule<boolean>;
    businessNameTooLong: string;
    email: string | ValidationRule<boolean>;
    emailTooLong: string;
    emailInvalid: string;
    url: string | ValidationRule<boolean>;
    invalidUrl: string;
    followersMaxLength: string;
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    dateOfBirthInvalid: string;
    ageMustBe18OrOlder: string;
    country: string;
  };
};
type Translations = {
  messages: {
    preferredEmailTooLong: string;
    preferredEmailInvalid: string;
    preferredPhoneNumber: string | ValidationRule<boolean>;
    preferredPhoneNumberTooLong: string;
    contentLanguage: string | ValidationRule<boolean>;
    language: string | ValidationRule<boolean>;
    preferredEmail: string;
  };
};
type Messages = {
  businessNameRequired: string;
};
const rules = (labels: Labels): CreatorFormRules => {
  if (stryMutAct_9fa48("1312")) {
    {}
  } else {
    stryCov_9fa48("1312");
    return stryMutAct_9fa48("1313") ? {} : (stryCov_9fa48("1313"), {
      firstName: stryMutAct_9fa48("1314") ? {} : (stryCov_9fa48("1314"), {
        required: labels.messages.firstName,
        maxLength: stryMutAct_9fa48("1315") ? {} : (stryCov_9fa48("1315"), {
          value: validationConfig.MAXLENGTH_40,
          message: labels.messages.firstNameTooLong
        })
      }),
      lastName: stryMutAct_9fa48("1316") ? {} : (stryCov_9fa48("1316"), {
        required: labels.messages.lastName,
        maxLength: stryMutAct_9fa48("1317") ? {} : (stryCov_9fa48("1317"), {
          value: validationConfig.MAXLENGTH_80,
          message: labels.messages.lastNameTooLong
        })
      }),
      dateOfBirth: stryMutAct_9fa48("1318") ? {} : (stryCov_9fa48("1318"), {
        required: labels.messages.dateOfBirth,
        maxLength: stryMutAct_9fa48("1319") ? {} : (stryCov_9fa48("1319"), {
          value: validationConfig.DATE_MAXLENGTH,
          message: labels.messages.dateOfBirthInvalid
        }),
        validate: value => {
          if (stryMutAct_9fa48("1320")) {
            {}
          } else {
            stryCov_9fa48("1320");
            if (stryMutAct_9fa48("1323") ? false : stryMutAct_9fa48("1322") ? true : stryMutAct_9fa48("1321") ? LocalizedDate.isValid(value) : (stryCov_9fa48("1321", "1322", "1323"), !LocalizedDate.isValid(value))) {
              if (stryMutAct_9fa48("1324")) {
                {}
              } else {
                stryCov_9fa48("1324");
                return labels.messages.dateOfBirthInvalid;
              }
            }
            if (stryMutAct_9fa48("1326") ? false : stryMutAct_9fa48("1325") ? true : (stryCov_9fa48("1325", "1326"), isAdult(value.toString()))) {
              if (stryMutAct_9fa48("1327")) {
                {}
              } else {
                stryCov_9fa48("1327");
                return labels.messages.ageMustBe18OrOlder;
              }
            }
            return stryMutAct_9fa48("1328") ? false : (stryCov_9fa48("1328"), true);
          }
        }
      }),
      country: stryMutAct_9fa48("1329") ? {} : (stryCov_9fa48("1329"), {
        required: labels.messages.country,
        validate: countryVal => {
          if (stryMutAct_9fa48("1330")) {
            {}
          } else {
            stryCov_9fa48("1330");
            const {
              label,
              name,
              value
            } = countryVal;
            if (stryMutAct_9fa48("1333") ? !label && !name && !value || isObj(countryVal) : stryMutAct_9fa48("1332") ? false : stryMutAct_9fa48("1331") ? true : (stryCov_9fa48("1331", "1332", "1333"), (stryMutAct_9fa48("1335") ? !label && !name || !value : stryMutAct_9fa48("1334") ? true : (stryCov_9fa48("1334", "1335"), (stryMutAct_9fa48("1337") ? !label || !name : stryMutAct_9fa48("1336") ? true : (stryCov_9fa48("1336", "1337"), (stryMutAct_9fa48("1338") ? label : (stryCov_9fa48("1338"), !label)) && (stryMutAct_9fa48("1339") ? name : (stryCov_9fa48("1339"), !name)))) && (stryMutAct_9fa48("1340") ? value : (stryCov_9fa48("1340"), !value)))) && isObj(countryVal))) {
              if (stryMutAct_9fa48("1341")) {
                {}
              } else {
                stryCov_9fa48("1341");
                // param is object for drop-down
                return labels.messages.country;
              }
            } else if (stryMutAct_9fa48("1344") ? isString(countryVal) || !countryVal : stryMutAct_9fa48("1343") ? false : stryMutAct_9fa48("1342") ? true : (stryCov_9fa48("1342", "1343", "1344"), isString(countryVal) && (stryMutAct_9fa48("1345") ? countryVal : (stryCov_9fa48("1345"), !countryVal)))) {
              if (stryMutAct_9fa48("1346")) {
                {}
              } else {
                stryCov_9fa48("1346");
                return labels.messages.country;
              }
            }
            return stryMutAct_9fa48("1347") ? false : (stryCov_9fa48("1347"), true);
          }
        }
      }),
      interestedCreatorCountry: stryMutAct_9fa48("1348") ? {} : (stryCov_9fa48("1348"), {
        required: labels.messages.country,
        validate: countryVal => {
          if (stryMutAct_9fa48("1349")) {
            {}
          } else {
            stryCov_9fa48("1349");
            const {
              label,
              name,
              value
            } = countryVal;
            if (stryMutAct_9fa48("1352") ? !label && !name && isObj(countryVal) && !value : stryMutAct_9fa48("1351") ? false : stryMutAct_9fa48("1350") ? true : (stryCov_9fa48("1350", "1351", "1352"), (stryMutAct_9fa48("1354") ? !label && !name || isObj(countryVal) : stryMutAct_9fa48("1353") ? false : (stryCov_9fa48("1353", "1354"), (stryMutAct_9fa48("1356") ? !label || !name : stryMutAct_9fa48("1355") ? true : (stryCov_9fa48("1355", "1356"), (stryMutAct_9fa48("1357") ? label : (stryCov_9fa48("1357"), !label)) && (stryMutAct_9fa48("1358") ? name : (stryCov_9fa48("1358"), !name)))) && isObj(countryVal))) || (stryMutAct_9fa48("1359") ? value : (stryCov_9fa48("1359"), !value)))) {
              if (stryMutAct_9fa48("1360")) {
                {}
              } else {
                stryCov_9fa48("1360");
                return labels.messages.country;
              }
            } else if (stryMutAct_9fa48("1363") ? isString(countryVal) || !countryVal : stryMutAct_9fa48("1362") ? false : stryMutAct_9fa48("1361") ? true : (stryCov_9fa48("1361", "1362", "1363"), isString(countryVal) && (stryMutAct_9fa48("1364") ? countryVal : (stryCov_9fa48("1364"), !countryVal)))) {
              if (stryMutAct_9fa48("1365")) {
                {}
              } else {
                stryCov_9fa48("1365");
                return labels.messages.country;
              }
            }
            return stryMutAct_9fa48("1366") ? false : (stryCov_9fa48("1366"), true);
          }
        }
      }),
      street: stryMutAct_9fa48("1367") ? {} : (stryCov_9fa48("1367"), {
        required: labels.messages.street,
        maxLength: stryMutAct_9fa48("1368") ? {} : (stryCov_9fa48("1368"), {
          value: validationConfig.MAXLENGTH_255,
          message: labels.messages.streetTooLong
        })
      }),
      city: stryMutAct_9fa48("1369") ? {} : (stryCov_9fa48("1369"), {
        required: labels.messages.city,
        maxLength: stryMutAct_9fa48("1370") ? {} : (stryCov_9fa48("1370"), {
          value: validationConfig.MAXLENGTH_40,
          message: labels.messages.cityTooLong
        })
      }),
      state: stryMutAct_9fa48("1371") ? {} : (stryCov_9fa48("1371"), {
        required: labels.messages.state,
        maxLength: stryMutAct_9fa48("1372") ? {} : (stryCov_9fa48("1372"), {
          value: validationConfig.MAXLENGTH_80,
          message: labels.messages.stateTooLong
        })
      }),
      zipCode: stryMutAct_9fa48("1373") ? {} : (stryCov_9fa48("1373"), {
        required: labels.messages.zipCode,
        maxLength: stryMutAct_9fa48("1374") ? {} : (stryCov_9fa48("1374"), {
          value: validationConfig.MAXLENGTH_20,
          message: labels.messages.zipCodeTooLong
        })
      }),
      tShirtSize: stryMutAct_9fa48("1375") ? {} : (stryCov_9fa48("1375"), {
        required: labels.messages.tShirtSize
      }),
      entityType: stryMutAct_9fa48("1376") ? {} : (stryCov_9fa48("1376"), {
        required: labels.messages.entityType
      }),
      businessName: stryMutAct_9fa48("1377") ? {} : (stryCov_9fa48("1377"), {
        required: labels.messages.businessName,
        maxLength: stryMutAct_9fa48("1378") ? {} : (stryCov_9fa48("1378"), {
          value: validationConfig.MAXLENGTH,
          message: labels.messages.businessNameTooLong
        })
      }),
      email: stryMutAct_9fa48("1379") ? {} : (stryCov_9fa48("1379"), {
        required: labels.messages.email,
        maxLength: stryMutAct_9fa48("1380") ? {} : (stryCov_9fa48("1380"), {
          value: validationConfig.MAXLENGTH_80,
          message: labels.messages.emailTooLong
        }),
        pattern: stryMutAct_9fa48("1381") ? {} : (stryCov_9fa48("1381"), {
          value: validationConfig.regex.EMAIL,
          message: labels.messages.emailInvalid
        })
      }),
      url: stryMutAct_9fa48("1382") ? {} : (stryCov_9fa48("1382"), {
        required: stryMutAct_9fa48("1383") ? true : (stryCov_9fa48("1383"), false)
      }),
      followers: stryMutAct_9fa48("1384") ? {} : (stryCov_9fa48("1384"), {
        validate: stryMutAct_9fa48("1385") ? () => undefined : (stryCov_9fa48("1385"), value => stryMutAct_9fa48("1388") ? /^\d{0,18}$/.test(value) && value.length <= 18 && labels.messages.followersMaxLength : stryMutAct_9fa48("1387") ? false : stryMutAct_9fa48("1386") ? true : (stryCov_9fa48("1386", "1387", "1388"), (stryMutAct_9fa48("1390") ? /^\d{0,18}$/.test(value) || value.length <= 18 : stryMutAct_9fa48("1389") ? false : (stryCov_9fa48("1389", "1390"), (stryMutAct_9fa48("1394") ? /^\D{0,18}$/ : stryMutAct_9fa48("1393") ? /^\d$/ : stryMutAct_9fa48("1392") ? /^\d{0,18}/ : stryMutAct_9fa48("1391") ? /\d{0,18}$/ : (stryCov_9fa48("1391", "1392", "1393", "1394"), /^\d{0,18}$/)).test(value) && (stryMutAct_9fa48("1397") ? value.length > 18 : stryMutAct_9fa48("1396") ? value.length < 18 : stryMutAct_9fa48("1395") ? true : (stryCov_9fa48("1395", "1396", "1397"), value.length <= 18)))) || labels.messages.followersMaxLength))
      })
    });
  }
};
const communicationRules = (translation: Translations): CommunicationFormRules => {
  if (stryMutAct_9fa48("1398")) {
    {}
  } else {
    stryCov_9fa48("1398");
    return stryMutAct_9fa48("1399") ? {} : (stryCov_9fa48("1399"), {
      preferredEmail: stryMutAct_9fa48("1400") ? {} : (stryCov_9fa48("1400"), {
        required: translation.messages.preferredEmail,
        maxLength: stryMutAct_9fa48("1401") ? {} : (stryCov_9fa48("1401"), {
          value: validationConfig.MAXLENGTH_80,
          message: translation.messages.preferredEmailTooLong
        }),
        pattern: stryMutAct_9fa48("1402") ? {} : (stryCov_9fa48("1402"), {
          value: validationConfig.regex.EMAIL,
          message: translation.messages.preferredEmailInvalid
        })
      }),
      preferredPhoneNumber: stryMutAct_9fa48("1403") ? {} : (stryCov_9fa48("1403"), {
        required: translation.messages.preferredPhoneNumber,
        maxLength: stryMutAct_9fa48("1404") ? {} : (stryCov_9fa48("1404"), {
          value: validationConfig.MAXLENGTH_40,
          message: translation.messages.preferredPhoneNumberTooLong
        })
      }),
      contentLanguage: stryMutAct_9fa48("1405") ? {} : (stryCov_9fa48("1405"), {
        required: translation.messages.contentLanguage
      }),
      language: stryMutAct_9fa48("1406") ? {} : (stryCov_9fa48("1406"), {
        required: translation.messages.language
      })
    });
  }
};
const getPactFormRules = (messages: Messages): PactSafeFormRules => {
  if (stryMutAct_9fa48("1407")) {
    {}
  } else {
    stryCov_9fa48("1407");
    return stryMutAct_9fa48("1408") ? {} : (stryCov_9fa48("1408"), {
      businessName: stryMutAct_9fa48("1409") ? {} : (stryCov_9fa48("1409"), {
        required: messages.businessNameRequired
      })
    });
  }
};
const formsRules = stryMutAct_9fa48("1410") ? {} : (stryCov_9fa48("1410"), {
  rules,
  communicationRules,
  getPactFormRules
});
export default formsRules;