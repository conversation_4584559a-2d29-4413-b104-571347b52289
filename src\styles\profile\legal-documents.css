.legal-documents {
  @apply text-black;
}
.legal-documents > .profile-legal-documents-intro-title {
  @apply mb-meas8;
}
.legal-documents .cn-tabs-header {
  max-width: 400px;
  justify-content: space-between;
}
.legal-documents .cn-tabs-header button {
  @apply flex pb-meas4 font-text-regular xs:text-mobile-body-small md:text-tablet-body-small lg:text-desktop-body-small;
}
.legal-documents .cn-tabs-header > .cn-tab.active {
  @apply border-b-4 border-primary;
}

.legal-documents .cn-tabs-content {
  @apply bg-white xl:min-w-[794px] xl:max-w-[899px];
  border: 1px solid #a5a5a5;
  box-sizing: border-box;
  border-radius: 4px;
}
.opportunity-contract,
.creator-agreement {
  @apply flex flex-col items-center justify-center;
}
.legal-documents table {
  border-collapse: collapse;
  width: 95%;
}
.legal-documents .cn-tabs-content tr,
.legal-documents .cn-tabs-content tr {
  @apply p-meas7 text-left;
  border-bottom: 1px solid #a5a5a5;
}

.legal-documents .cn-tabs-content table > tbody tr:last-of-type {
  border-bottom: none;
}
.legal-documents a {
  text-decoration: underline;
}

.legal-documents .cn-tabs-content table > thead {
  @apply hidden md:block;
}

.legal-documents .cn-tabs-content table tr {
  @apply flex flex-col items-start justify-center md:grid md:justify-between md:gap-meas4;
}

@media screen and (min-width: 768px) {
  .legal-documents .cn-tabs-content .opportunity-contract table tr {
    grid-template-columns: 2fr 2fr 1fr;
  }
  .legal-documents .cn-tabs-content .creator-agreement table tr {
    @apply flex flex-row justify-between;
  }
}

.legal-documents .no-contracts > svg {
  @apply max-h-[60px] w-[30pc] max-w-[60px];
}

.legal-documents .no-contracts .no-contract-content > h6 {
  @apply font-text-regular text-black xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.legal-documents .no-contracts .no-contract-content > p {
  @apply font-text-regular text-black xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}

.legal-documents .no-contracts {
  @apply flex p-meas16;
}

.legal-documents .no-contracts .no-contract-content {
  @apply flex flex-col pl-meas16;
}

.legal-documents .cn-tabs-content table tbody .no-contracts-container {
  @apply md:table-row;
}
