import { screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";

export async function selectOption({
  option,
  container,
  label
}: {
  option: string;
  container: HTMLElement;
  label: string;
}): Promise<HTMLElement> {
  const dropdown = screen.getByLabelText(label);
  await userEvent.click(dropdown);
  // Wait for the options to be displayed
  await waitFor(() => expect(container.querySelector(".select-list")).toBeInTheDocument(), { timeout: 1_100 });
  await userEvent.click(await screen.findByRole("button", { name: option }));
  await waitFor(() => expect(container.querySelector(".select-list")).not.toBeInTheDocument());

  return dropdown;
}

export async function selectMultipleOptions(options: string[]): Promise<HTMLElement> {
  const dropdown = screen.getByTestId("multi-select-id"); // can cause issues

  for (let i = 0; i < options.length; i++) {
    // Expand options
    await userEvent.click(dropdown);
    await waitFor(() => expect(screen.queryByRole("menu")).toBeInTheDocument());
    // Select option
    await userEvent.click(await screen.findByRole("checkbox", { name: options[i] }));
    await waitFor(() => expect(screen.queryByRole("menu")).not.toBeInTheDocument());
  }

  return dropdown;
}

export async function enterValueFor(label: RegExp | string, data: string): Promise<void> {
  const inputField = await screen.findByLabelText(label);
  await userEvent.type(inputField, data);
  await waitFor(() => expect(inputField).toHaveValue(data));
}

export async function clearValueFor(label: RegExp | string): Promise<void> {
  const inputField = screen.getByLabelText(label);
  await userEvent.click(inputField); // we need to open the calendar when the input has no value
  await userEvent.clear(inputField);
  await waitFor(() => expect(inputField).toHaveValue(""));
}

export async function clearDateFor(label: RegExp | string): Promise<void> {
  await clearValueFor(label);
  await userEvent.click(screen.getByRole("button", { name: /Ok/i }));
}

export async function enterDateFor(label: RegExp | string, data: string): Promise<void> {
  await enterValueFor(label, data);
  await userEvent.click(screen.getByRole("button", { name: /Ok/i }));
}

export async function clearValueForUrl(label: RegExp | string): Promise<void> {
  const inputField = screen.getByLabelText(label);
  await userEvent.clear(inputField);
  await waitFor(() => expect(inputField).toHaveValue("https://"));
}
