import React from "react";
import { DEFAULT_LOCALE, DOMAIN_ERROR, ERROR, HAS_EXCEPTION, SUCCESS, VALIDATION_ERROR } from "./constants";
import * as Sentry from "@sentry/nextjs";
import getConfig from "next/config";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { Dispatch, FunctionArgumentType, ValidationError } from "./types";
import { IncomingHttpHeaders } from "http";

//------------------------------------------------
// debounce to resolve duplicate calls on resize
//------------------------------------------------
type DebounceFunction = (...args: FunctionArgumentType[]) => void;
export function debounce<T extends DebounceFunction>(func: T, wait: number, immediate?: boolean): DebounceFunction {
  let timeout: NodeJS.Timeout | null = null;

  return function (this: unknown, ...args: FunctionArgumentType[]): void {
    const later = function (this: unknown) {
      timeout = null;
      if (!immediate) func.apply(this, args); // Use `this` directly
    };

    const callNow = immediate && !timeout;
    clearTimeout(timeout as NodeJS.Timeout);
    timeout = setTimeout(later, wait);

    if (callNow) func.apply(this, args); // Use `this` directly
  };
}

//------------------------------------------------
// Set cookie when local is updated
//------------------------------------------------
export function setCookie(locale: string): void {
  document.cookie = `NEXT_LOCALE=${locale}; expires=Fri, 31 Dec 9999 23:59:59 GMT; Path=/`;
}

export function isObj(obj: object): boolean {
  return Object.prototype.toString.call(obj) === "[object Object]";
}

export function isString(obj: object): boolean {
  return Object.prototype.toString.call(obj) === "[object String]";
}

export function isBrowser(): boolean {
  return typeof window !== "undefined";
}

//------------------------------------------------
// Extract Locale from accept-language header
//------------------------------------------------

export function getLocale(headers: IncomingHttpHeaders, SUPPORTED_LOCALES: string[]): string {
  // If no browser accept-language then fallback to DEFAULT_LOCALE
  const browserLocale = ((headers &&
    headers["accept-language"] &&
    headers["accept-language"]?.match(/\w{2}\W\w{2}/)) || [DEFAULT_LOCALE])[0];
  return SUPPORTED_LOCALES.includes(browserLocale.toLowerCase()) ? browserLocale.toLowerCase() : DEFAULT_LOCALE;
}

export function toastContent(validationErrors: ValidationError[]): JSX.Element {
  return (
    <ul className="formatted-content">
      {validationErrors.map((error, index) =>
        !Array.isArray(error.errorMessages) ? (
          <li className="text-gray-90" key={index}>{`${error.propertyName} ${error.errorMessages}`}</li>
        ) : (
          <li className="text-gray-90" key={index}>
            {error.errorMessages.length === 1 ? (
              `${error.propertyName} ${error.errorMessages[0]}`
            ) : (
              <>
                {error.propertyName}
                <ul key={"messages-" + index}>
                  {error.errorMessages.map((message, messageIndex) => (
                    <li key={"message-" + messageIndex}>{message}</li>
                  ))}
                </ul>
              </>
            )}
          </li>
        )
      )}
    </ul>
  );
}

export const onToastClose = (toast: string, dispatch: (action: { type: string; data: boolean }) => void): void => {
  switch (toast) {
    case ERROR:
      dispatch({ type: ERROR, data: false });
      break;
    case SUCCESS:
      dispatch({ type: SUCCESS, data: false });
      break;
    case VALIDATION_ERROR:
      dispatch({ type: VALIDATION_ERROR, data: false });
      break;
    case DOMAIN_ERROR:
      dispatch({ type: DOMAIN_ERROR, data: false });
      break;
    default:
      break;
  }
};

interface ErrorResponse {
  response?: {
    data?: {
      code?: string;
      message?: string;
      detail?: string;
      errors?: Record<string, string>;
    };
    status?: number;
  };
  code?: string;
}

type ErrorMap = Map<string, string>;

export const getExtractedErrorMessage = (
  errorMap: ErrorMap,
  error: ErrorResponse | boolean,
  fallback: string
): string => {
  return (
    errorMap.get(((error as ErrorResponse)?.response?.data?.code || (error as ErrorResponse)?.code) ?? "") ||
    (error as ErrorResponse)?.response?.data?.message ||
    (error as ErrorResponse)?.response?.data?.detail ||
    fallback
  );
};

export function validationMessage(validationErrors: Record<string, string>): ValidationError[] {
  return Object.keys(validationErrors).reduce((formattedErrors: ValidationError[], key: string) => {
    const propertyName = `${key
      .split(/\.|(?=[A-Z])/)
      .join(" ")
      .toLowerCase()}`;
    const errorMessages = validationErrors[key];
    formattedErrors.push({ propertyName, errorMessages });
    return formattedErrors;
  }, []);
}

export function errorHandling(dispatch: Dispatch, e: ErrorResponse): void {
  try {
    const { publicRuntimeConfig: props = {} } = getConfig();
    const SUPPORTED_LOCALES = JSON.parse(props.SUPPORTED_LOCALES);
    Sentry.captureException(e);
    switch (e?.response?.status) {
      case 401:
        const redirectUrl =
          SUPPORTED_LOCALES.indexOf(window.location.pathname.split("/")[1]) === -1
            ? "/"
            : `/${window.location.pathname.split("/")[1]}`;
        window.location.href = redirectUrl;
        break;
      case 404:
        dispatch({ type: HAS_EXCEPTION, data: e.response.status });
        break;
      case 500:
        dispatch({ type: HAS_EXCEPTION, data: e.response.status });
        break;
      case 422:
        dispatch({ type: VALIDATION_ERROR, data: validationMessage(e.response?.data?.errors ?? {}) });
        break;
      case 409:
        dispatch({ type: DOMAIN_ERROR, data: e });
        break;
      default:
        if (!e?.response?.data?.message) console.log(e);
        dispatch({ type: HAS_EXCEPTION, data: e?.response?.status ?? [] });
        break;
    }
  } catch (error) {
    // TODO: in local environment it should console.error, anywhere else should send error to server for logging
    console.error({ originalError: e, errorHandlingError: error });
  }
}

//---------------------------------
// Age must be 18 years or older
//---------------------------------
export function isAdult(birthDate: string): boolean {
  const dateOfBirth = LocalizedDate.fromFormattedDate(birthDate);
  return dateOfBirth.isAfter(LocalizedDate.subtractFromNow(18, "years"));
}
