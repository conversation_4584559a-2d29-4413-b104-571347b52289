// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import { Controller, useFormContext } from "react-hook-form";
import React, { memo, useCallback, useEffect, useState } from "react";
import { Select } from "@eait-playerexp-cn/core-ui-kit";
import ProfileFormAction from "../ProfileFormAction/ProfileFormAction";
import { MultiSelect } from "@eait-playerexp-cn/core-ui-kit";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import FormTitle from "../FormTitle/FormTitle";
import { ButtonLabels, InfoLabels } from "../types";
import { HardwarePartner } from "@eait-playerexp-cn/metadata-types";
import { AdditionalInformationPayload } from "@eait-playerexp-cn/creator-types";
import { CreatorFormRules } from "@src/utils/FormRules/CreatorForm";
interface MiscellaneousFormProps {
  infoLabels: InfoLabels;
  rules: CreatorFormRules;
  additionalInformation: AdditionalInformationPayload;
  hardwarePartners: HardwarePartner[];
  buttons: ButtonLabels;
  onChange: () => void;
  isSaved: boolean;
  isLoader: boolean;
}
const tShirtSizes = stryMutAct_9fa48("742") ? [] : (stryCov_9fa48("742"), [stryMutAct_9fa48("743") ? {} : (stryCov_9fa48("743"), {
  value: stryMutAct_9fa48("744") ? "" : (stryCov_9fa48("744"), "XS"),
  label: stryMutAct_9fa48("745") ? "" : (stryCov_9fa48("745"), "XS")
}), stryMutAct_9fa48("746") ? {} : (stryCov_9fa48("746"), {
  value: stryMutAct_9fa48("747") ? "" : (stryCov_9fa48("747"), "S"),
  label: stryMutAct_9fa48("748") ? "" : (stryCov_9fa48("748"), "S")
}), stryMutAct_9fa48("749") ? {} : (stryCov_9fa48("749"), {
  value: stryMutAct_9fa48("750") ? "" : (stryCov_9fa48("750"), "M"),
  label: stryMutAct_9fa48("751") ? "" : (stryCov_9fa48("751"), "M")
}), stryMutAct_9fa48("752") ? {} : (stryCov_9fa48("752"), {
  value: stryMutAct_9fa48("753") ? "" : (stryCov_9fa48("753"), "L"),
  label: stryMutAct_9fa48("754") ? "" : (stryCov_9fa48("754"), "L")
}), stryMutAct_9fa48("755") ? {} : (stryCov_9fa48("755"), {
  value: stryMutAct_9fa48("756") ? "" : (stryCov_9fa48("756"), "XL"),
  label: stryMutAct_9fa48("757") ? "" : (stryCov_9fa48("757"), "XL")
}), stryMutAct_9fa48("758") ? {} : (stryCov_9fa48("758"), {
  value: stryMutAct_9fa48("759") ? "" : (stryCov_9fa48("759"), "XXL"),
  label: stryMutAct_9fa48("760") ? "" : (stryCov_9fa48("760"), "XXL")
}), stryMutAct_9fa48("761") ? {} : (stryCov_9fa48("761"), {
  value: stryMutAct_9fa48("762") ? "" : (stryCov_9fa48("762"), "XXXL"),
  label: stryMutAct_9fa48("763") ? "" : (stryCov_9fa48("763"), "XXXL")
})]);
const MiscellaneousForm = memo(function MiscellaneousForm({
  infoLabels,
  rules,
  additionalInformation,
  hardwarePartners,
  buttons,
  onChange,
  isSaved = stryMutAct_9fa48("764") ? true : (stryCov_9fa48("764"), false),
  isLoader
}: MiscellaneousFormProps) {
  if (stryMutAct_9fa48("765")) {
    {}
  } else {
    stryCov_9fa48("765");
    const {
      control
    } = useFormContext();
    const [isEdit, setIsEdit] = useState(stryMutAct_9fa48("766") ? true : (stryCov_9fa48("766"), false));
    const {
      success: successToast
    } = useToast();
    const timetoDisplay = stryMutAct_9fa48("767") ? Math.max(Math.max(infoLabels.success.miscellaneous.length * 50, 2000), 7000) : (stryCov_9fa48("767"), Math.min(stryMutAct_9fa48("768") ? Math.min(infoLabels.success.miscellaneous.length * 50, 2000) : (stryCov_9fa48("768"), Math.max(stryMutAct_9fa48("769") ? infoLabels.success.miscellaneous.length / 50 : (stryCov_9fa48("769"), infoLabels.success.miscellaneous.length * 50), 2000)), 7000));
    const [defaultHoodieSize, setDefaultHoodieSize] = useState(stryMutAct_9fa48("770") ? {} : (stryCov_9fa48("770"), {
      label: stryMutAct_9fa48("771") ? "Stryker was here!" : (stryCov_9fa48("771"), ""),
      value: stryMutAct_9fa48("772") ? "Stryker was here!" : (stryCov_9fa48("772"), "")
    }));
    const [defaultHardwarePartners, setDefaultHardwarePartners] = useState(stryMutAct_9fa48("773") ? ["Stryker was here"] : (stryCov_9fa48("773"), []));
    useEffect(() => {
      if (stryMutAct_9fa48("774")) {
        {}
      } else {
        stryCov_9fa48("774");
        setDefaultHoodieSize(stryMutAct_9fa48("775") ? {} : (stryCov_9fa48("775"), {
          value: stryMutAct_9fa48("778") ? additionalInformation.hoodieSize && "" : stryMutAct_9fa48("777") ? false : stryMutAct_9fa48("776") ? true : (stryCov_9fa48("776", "777", "778"), additionalInformation.hoodieSize || (stryMutAct_9fa48("779") ? "Stryker was here!" : (stryCov_9fa48("779"), ""))),
          label: stryMutAct_9fa48("782") ? additionalInformation.hoodieSize && "None" : stryMutAct_9fa48("781") ? false : stryMutAct_9fa48("780") ? true : (stryCov_9fa48("780", "781", "782"), additionalInformation.hoodieSize || (stryMutAct_9fa48("783") ? "" : (stryCov_9fa48("783"), "None")))
        }));
        const convertedHardwarePartners = stryMutAct_9fa48("786") ? additionalInformation.hardwarePartners?.map(hardwarepartner => ({
          value: hardwarepartner.id,
          label: hardwarepartner.name
        })) && [] : stryMutAct_9fa48("785") ? false : stryMutAct_9fa48("784") ? true : (stryCov_9fa48("784", "785", "786"), (stryMutAct_9fa48("787") ? additionalInformation.hardwarePartners.map(hardwarepartner => ({
          value: hardwarepartner.id,
          label: hardwarepartner.name
        })) : (stryCov_9fa48("787"), additionalInformation.hardwarePartners?.map(stryMutAct_9fa48("788") ? () => undefined : (stryCov_9fa48("788"), hardwarepartner => stryMutAct_9fa48("789") ? {} : (stryCov_9fa48("789"), {
          value: hardwarepartner.id,
          label: hardwarepartner.name
        }))))) || (stryMutAct_9fa48("790") ? ["Stryker was here"] : (stryCov_9fa48("790"), [])));
        setDefaultHardwarePartners(convertedHardwarePartners);
      }
    }, stryMutAct_9fa48("791") ? [] : (stryCov_9fa48("791"), [additionalInformation]));
    const onEditChange = useCallback((isChecked: boolean) => {
      if (stryMutAct_9fa48("792")) {
        {}
      } else {
        stryCov_9fa48("792");
        setIsEdit(isChecked);
        if (stryMutAct_9fa48("794") ? false : stryMutAct_9fa48("793") ? true : (stryCov_9fa48("793", "794"), onChange)) onChange();
      }
    }, stryMutAct_9fa48("795") ? [] : (stryCov_9fa48("795"), [isEdit]));
    useEffect(() => {
      if (stryMutAct_9fa48("796")) {
        {}
      } else {
        stryCov_9fa48("796");
        if (stryMutAct_9fa48("799") ? isEdit || isSaved : stryMutAct_9fa48("798") ? false : stryMutAct_9fa48("797") ? true : (stryCov_9fa48("797", "798", "799"), isEdit && isSaved)) setIsEdit(stryMutAct_9fa48("800") ? true : (stryCov_9fa48("800"), false));
      }
    }, stryMutAct_9fa48("801") ? [] : (stryCov_9fa48("801"), [isSaved]));
    return <>
      {stryMutAct_9fa48("804") ? isSaved && isEdit || successToast(<Toast header={infoLabels.success.updatedInformationHeader} content={infoLabels.success.miscellaneous} />, {
        autoClose: {
          timetoDisplay
        }
      }) : stryMutAct_9fa48("803") ? false : stryMutAct_9fa48("802") ? true : (stryCov_9fa48("802", "803", "804"), (stryMutAct_9fa48("806") ? isSaved || isEdit : stryMutAct_9fa48("805") ? true : (stryCov_9fa48("805", "806"), isSaved && isEdit)) && successToast(<Toast header={infoLabels.success.updatedInformationHeader} content={infoLabels.success.miscellaneous} />, stryMutAct_9fa48("807") ? {} : (stryCov_9fa48("807"), {
        autoClose: stryMutAct_9fa48("808") ? {} : (stryCov_9fa48("808"), {
          timetoDisplay
        })
      })))}
      <div className="miscellaneous-information">
        <div className="form-sub-title-and-action">
          <FormTitle subTitle={infoLabels.miscellaneous} />
          <ProfileFormAction {...stryMutAct_9fa48("809") ? {} : (stryCov_9fa48("809"), {
            buttons,
            action: onEditChange,
            isSaved,
            isLoader
          })} />
        </div>

        <div className="miscellaneous-field-title" id="hoodie-size">
          {infoLabels.labels.tShirtSize}
        </div>
        <div className="miscellaneous-field">
          {stryMutAct_9fa48("812") ? !isEdit || additionalInformation.hoodieSize || infoLabels.labels.none : stryMutAct_9fa48("811") ? false : stryMutAct_9fa48("810") ? true : (stryCov_9fa48("810", "811", "812"), (stryMutAct_9fa48("813") ? isEdit : (stryCov_9fa48("813"), !isEdit)) && (stryMutAct_9fa48("815") ? additionalInformation.hoodieSize && infoLabels.labels.none : stryMutAct_9fa48("814") ? true : (stryCov_9fa48("814", "815"), additionalInformation.hoodieSize || infoLabels.labels.none)))}
          {stryMutAct_9fa48("818") ? isEdit || <Controller control={control} name="hoodieSize" rules={rules.tShirtSize} defaultValue={additionalInformation.hoodieSize} render={({
            field,
            fieldState: {
              error
            }
          }) => <Select id="hoodie-size" // label prop is not passed, so 'id' is being used for aria-labelledby
          selectedOption={defaultHoodieSize} errorMessage={error && error.message} options={tShirtSizes} onChange={item => {
            field.onChange(item);
          }} />} /> : stryMutAct_9fa48("817") ? false : stryMutAct_9fa48("816") ? true : (stryCov_9fa48("816", "817", "818"), isEdit && <Controller control={control} name="hoodieSize" rules={rules.tShirtSize} defaultValue={additionalInformation.hoodieSize} render={stryMutAct_9fa48("819") ? () => undefined : (stryCov_9fa48("819"), ({
            field,
            fieldState: {
              error
            }
          }) => <Select id="hoodie-size" // label prop is not passed, so 'id' is being used for aria-labelledby
          selectedOption={defaultHoodieSize} errorMessage={stryMutAct_9fa48("822") ? error || error.message : stryMutAct_9fa48("821") ? false : stryMutAct_9fa48("820") ? true : (stryCov_9fa48("820", "821", "822"), error && error.message)} options={tShirtSizes} onChange={item => {
            if (stryMutAct_9fa48("823")) {
              {}
            } else {
              stryCov_9fa48("823");
              field.onChange(item);
            }
          }} />)} />)}
        </div>

        <div className="miscellaneous-field-title">{infoLabels.labels.hardwarePartners}</div>
        <div className={stryMutAct_9fa48("824") ? `` : (stryCov_9fa48("824"), `miscellaneous-field ${stryMutAct_9fa48("827") ? !isEdit || "hardware-partner-field" : stryMutAct_9fa48("826") ? false : stryMutAct_9fa48("825") ? true : (stryCov_9fa48("825", "826", "827"), (stryMutAct_9fa48("828") ? isEdit : (stryCov_9fa48("828"), !isEdit)) && (stryMutAct_9fa48("829") ? "" : (stryCov_9fa48("829"), "hardware-partner-field")))}`)}>
          {stryMutAct_9fa48("832") ? !isEdit || (additionalInformation.hardwarePartners.length ? additionalInformation.hardwarePartners.map((item, key) => <div className="hardware-partner-head" key={key}>
                    {item.name}
                  </div>) : infoLabels.labels.none) : stryMutAct_9fa48("831") ? false : stryMutAct_9fa48("830") ? true : (stryCov_9fa48("830", "831", "832"), (stryMutAct_9fa48("833") ? isEdit : (stryCov_9fa48("833"), !isEdit)) && (additionalInformation.hardwarePartners.length ? additionalInformation.hardwarePartners.map(stryMutAct_9fa48("834") ? () => undefined : (stryCov_9fa48("834"), (item, key) => <div className="hardware-partner-head" key={key}>
                    {item.name}
                  </div>)) : infoLabels.labels.none))}
          {stryMutAct_9fa48("837") ? isEdit || <Controller control={control} name="hardwarePartners" defaultValue={defaultHardwarePartners} render={({
            field,
            fieldState: {
              error
            }
          }) => <MultiSelect selectedOptions={field.value || defaultHardwarePartners} errorMessage={error && error.message || ""} {...field} options={hardwarePartners} placeholder={infoLabels.labels.hardwarePartners} />} /> : stryMutAct_9fa48("836") ? false : stryMutAct_9fa48("835") ? true : (stryCov_9fa48("835", "836", "837"), isEdit && <Controller control={control} name="hardwarePartners" defaultValue={defaultHardwarePartners} render={stryMutAct_9fa48("838") ? () => undefined : (stryCov_9fa48("838"), ({
            field,
            fieldState: {
              error
            }
          }) => <MultiSelect selectedOptions={stryMutAct_9fa48("841") ? field.value && defaultHardwarePartners : stryMutAct_9fa48("840") ? false : stryMutAct_9fa48("839") ? true : (stryCov_9fa48("839", "840", "841"), field.value || defaultHardwarePartners)} errorMessage={stryMutAct_9fa48("844") ? error && error.message && "" : stryMutAct_9fa48("843") ? false : stryMutAct_9fa48("842") ? true : (stryCov_9fa48("842", "843", "844"), (stryMutAct_9fa48("846") ? error || error.message : stryMutAct_9fa48("845") ? false : (stryCov_9fa48("845", "846"), error && error.message)) || (stryMutAct_9fa48("847") ? "Stryker was here!" : (stryCov_9fa48("847"), "")))} {...field} options={hardwarePartners} placeholder={infoLabels.labels.hardwarePartners} />)} />)}
        </div>
      </div>
    </>;
  }
});
export default MiscellaneousForm;