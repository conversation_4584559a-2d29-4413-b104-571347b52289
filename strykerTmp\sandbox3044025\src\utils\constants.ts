// @ts-nocheck
//------------------------
// Constants
//------------------------
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
export const LOADING = stryMutAct_9fa48("1263") ? "" : (stryCov_9fa48("1263"), "LOADING");
export const ERROR: string = stryMutAct_9fa48("1264") ? "" : (stryCov_9fa48("1264"), "ERROR");
export const VALIDATION_ERROR: string = stryMutAct_9fa48("1265") ? "" : (stryCov_9fa48("1265"), "VALIDATION_ERROR");
export const DOMAIN_ERROR: string = stryMutAct_9fa48("1266") ? "" : (stryCov_9fa48("1266"), "DOMAIN_ERROR");
export const HAS_EXCEPTION: string = stryMutAct_9fa48("1267") ? "" : (stryCov_9fa48("1267"), "HAS_EXCEPTION");
export const SESSION_USER = stryMutAct_9fa48("1268") ? "" : (stryCov_9fa48("1268"), "SESSION_USER");
export const SUCCESS: string = stryMutAct_9fa48("1269") ? "" : (stryCov_9fa48("1269"), "SUCCESS");
export const ONBOARDING_STEPS: string = stryMutAct_9fa48("1270") ? "" : (stryCov_9fa48("1270"), "ONBOARDING_STEPS");
export const USER_NAVIGATED: string = stryMutAct_9fa48("1271") ? "" : (stryCov_9fa48("1271"), "USER_NAVIGATED");
export const NO_SHALLOW_ROUTING = stryMutAct_9fa48("1272") ? [] : (stryCov_9fa48("1272"), [stryMutAct_9fa48("1273") ? "" : (stryCov_9fa48("1273"), "connected-accounts"), stryMutAct_9fa48("1274") ? "" : (stryCov_9fa48("1274"), "communication-settings")]);
export const POPUP_OPENED = stryMutAct_9fa48("1275") ? "" : (stryCov_9fa48("1275"), "POPUP_OPENED");
export const WINDOW_PARAMS = stryMutAct_9fa48("1276") ? "" : (stryCov_9fa48("1276"), "resizable=yes,scrollbars=yes,height=600px, width=600px");
export const PARTICIPATION = stryMutAct_9fa48("1277") ? "" : (stryCov_9fa48("1277"), "PARTICIPATION");
export const JOIN_OPPORTUNITY_FORM = stryMutAct_9fa48("1278") ? "" : (stryCov_9fa48("1278"), "JOIN_OPPORTUNITY_FORM");
export const OPPORTUNITY = stryMutAct_9fa48("1279") ? "" : (stryCov_9fa48("1279"), "OPPORTUNITY");
export const OPPORTUNITY_STATUS = stryMutAct_9fa48("1280") ? "" : (stryCov_9fa48("1280"), "OPPORTUNITY_STATUS");
export const PARTICIPATION_STATUS = stryMutAct_9fa48("1281") ? "" : (stryCov_9fa48("1281"), "PARTICIPATION_STATUS");
export const JOIN_OPPORTUNITY_STEPS = stryMutAct_9fa48("1282") ? "" : (stryCov_9fa48("1282"), "JOIN_OPPORTUNITY_STEPS");
export const SET_COUNTRIES = stryMutAct_9fa48("1283") ? "" : (stryCov_9fa48("1283"), "SET_COUNTRIES");
export const INFO = stryMutAct_9fa48("1284") ? "" : (stryCov_9fa48("1284"), "INFO");
export const WARNING = stryMutAct_9fa48("1285") ? "" : (stryCov_9fa48("1285"), "WARNING");
export const TOAST_ERROR = stryMutAct_9fa48("1286") ? "" : (stryCov_9fa48("1286"), "TOAST_ERROR");
export const COMPLETED_OPPORTUNITY_STEPS = stryMutAct_9fa48("1287") ? "" : (stryCov_9fa48("1287"), "COMPLETED_OPPORTUNITY_STEPS");
export const DEFAULT = stryMutAct_9fa48("1288") ? "" : (stryCov_9fa48("1288"), "DEFAULT");
export const DEFAULT_LOCALE = stryMutAct_9fa48("1289") ? "" : (stryCov_9fa48("1289"), "en-us");
export const COMPLETED_ONBOARDING_STEPS = stryMutAct_9fa48("1290") ? "" : (stryCov_9fa48("1290"), "COMPLETED_ONBOARDING_STEPS");
export const RELOAD_INTERESTED_CREATOR_ACCOUNTS = stryMutAct_9fa48("1291") ? "" : (stryCov_9fa48("1291"), "RELOAD_INTERESTED_CREATOR_ACCOUNTS");
export const GET_FB_PAGES = stryMutAct_9fa48("1292") ? "" : (stryCov_9fa48("1292"), "GET_FB_PAGES");
export const INVALID_TIKTOK_SCOPE = stryMutAct_9fa48("1293") ? "" : (stryCov_9fa48("1293"), "invalid-tiktok-scope");
export const CURRENT_DELIVERABLE_PAGE = stryMutAct_9fa48("1294") ? "" : (stryCov_9fa48("1294"), "CURRENT_DELIVERABLE_PAGE");
export const CLICKED_DELIVERABLE_ID = stryMutAct_9fa48("1295") ? "" : (stryCov_9fa48("1295"), "CLICKED_DELIVERABLE_ID");
export const CREATOR_CODE_DETAILS = stryMutAct_9fa48("1296") ? "" : (stryCov_9fa48("1296"), "CREATOR_CODE_DETAILS");
export const GET_PLATFORMS = stryMutAct_9fa48("1297") ? "" : (stryCov_9fa48("1297"), "GET_PLATFORMS");
export const ACTIVE_GAME_CODE = stryMutAct_9fa48("1298") ? "" : (stryCov_9fa48("1298"), "ACTIVE_GAME_CODE");
export const ACTIVE_CONTENT_SUBMISSION = stryMutAct_9fa48("1299") ? "" : (stryCov_9fa48("1299"), "ACTIVE_CONTENT_SUBMISSION");
export const SHOW_FACEBOOK_PAGES = stryMutAct_9fa48("1300") ? "" : (stryCov_9fa48("1300"), "SHOW_FACEBOOK_PAGES");
export const JOINED = stryMutAct_9fa48("1301") ? "" : (stryCov_9fa48("1301"), "JOINED");
export const COMPLETED = stryMutAct_9fa48("1302") ? "" : (stryCov_9fa48("1302"), "COMPLETED");
export const DECLINED = stryMutAct_9fa48("1303") ? "" : (stryCov_9fa48("1303"), "DECLINED");
export const INVITED = stryMutAct_9fa48("1304") ? "" : (stryCov_9fa48("1304"), "INVITED");
export const OPEN = stryMutAct_9fa48("1305") ? "" : (stryCov_9fa48("1305"), "OPEN");
export const PAST = stryMutAct_9fa48("1306") ? "" : (stryCov_9fa48("1306"), "PAST");
export const SIMS_UGX = stryMutAct_9fa48("1307") ? "" : (stryCov_9fa48("1307"), "sims_ugx");
export const SHOW_WARNING_FOR_CHANGES_REQUESTED = stryMutAct_9fa48("1308") ? "" : (stryCov_9fa48("1308"), "SHOW_WARNING_FOR_CHANGES_REQUESTED");