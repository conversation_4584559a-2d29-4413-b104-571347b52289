import { act, render, screen, waitFor } from "@testing-library/react";
import LegalEntityForm from "./LegalEntityForm";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import { aLegalEntityInformationPayload } from "@eait-playerexp-cn/creator-test-fixtures";
import { LegalInformationPayload, LegalEntityType } from "@eait-playerexp-cn/creator-types";
import { Country } from "@eait-playerexp-cn/metadata-types";
import formsRules from "@src/utils/FormRules/CreatorForm";
import { InformationPageLabels } from "@src/Translations/InformationPageLabels";

const successToast = jest.fn();
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: successToast
  })
}));

const MockFormProvider = ({ children, defaultValues = {} }: { children: React.ReactNode; defaultValues?: any }) => {
  const methods = useForm({
    defaultValues: {
      entityType: defaultValues.entityType || { value: "INDIVIDUAL" },
      businessName: defaultValues.businessName || "",
      street: defaultValues.street || "",
      city: defaultValues.city || "",
      country: defaultValues.country || null,
      state: defaultValues.state || "",
      zipCode: defaultValues.zipCode || "",
      ...defaultValues
    },
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("LegalEntityForm - Tests for legal entity information form functionality including rendering, edit mode, entity type selection, address fields, validation, toast messages, accessibility, and error handling", () => {
  const legalEntity: LegalInformationPayload = {
    ...aLegalEntityInformationPayload(),
    entityType: "INDIVIDUAL" as LegalEntityType,
    businessName: "Test Business LLC",
    street: "123 Main Street",
    city: "Test City",
    country: {
      code: "US",
      name: "United States"
    },
    state: "California",
    zipCode: "90210"
  };

  const mockCountries: Country[] = [
    {
      value: "US",
      label: "United States",
      name: "United States"
    },
    {
      value: "CA",
      label: "Canada",
      name: "Canada"
    },
    {
      value: "UK",
      label: "United Kingdom",
      name: "United Kingdom"
    }
  ];

  const infoLabels = InformationPageLabels.infoLabels;

  const mockProps = {
    infoLabels,
    rules: formsRules.rules(infoLabels),
    legalEntity,
    countries: mockCountries,
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close",
      browse: "Browse"
    },
    onChangeAsMailingAddress: jest.fn(),
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render legal entity form correctly", () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    expect(screen.getByText(infoLabels.legalEntityType)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.legalEntityDescription)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.entityType)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.street)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.city)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.country)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.state)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.zipCode)).toBeInTheDocument();
  });

  it("should display entity type in read-only mode initially", () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    expect(screen.getByText(infoLabels.labels.individual)).toBeInTheDocument();
  });

  it("should display address fields in read-only mode initially", () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    expect(screen.getByText("123 Main Street")).toBeInTheDocument();
    expect(screen.getByText("Test City")).toBeInTheDocument();
    expect(screen.getByText("United States")).toBeInTheDocument();
    expect(screen.getByText("California")).toBeInTheDocument();
    expect(screen.getByText("90210")).toBeInTheDocument();
  });

  it("should show business name field when entity type is BUSINESS", () => {
    const businessLegalEntity = {
      ...legalEntity,
      entityType: "BUSINESS" as LegalEntityType
    };

    render(
      <MockFormProvider defaultValues={businessLegalEntity}>
        <LegalEntityForm {...mockProps} legalEntity={businessLegalEntity} />
      </MockFormProvider>
    );

    expect(screen.getByText(infoLabels.labels.businessName)).toBeInTheDocument();
    expect(screen.getByText("Test Business LLC")).toBeInTheDocument();
  });

  it("should show edit button initially", () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
  });

  it("should enable edit mode when edit button is clicked", async () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
  });

  it("should show form inputs in edit mode", async () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("radio", { name: infoLabels.labels.individual })).toBeInTheDocument();
    expect(screen.getByRole("radio", { name: infoLabels.labels.business })).toBeInTheDocument();
    expect(screen.getByPlaceholderText(infoLabels.labels.street)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(infoLabels.labels.city)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(infoLabels.labels.state)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(infoLabels.labels.zipCode)).toBeInTheDocument();
  });

  it("should call onChange when edit mode is toggled", async () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(mockProps.onChange).toHaveBeenCalledTimes(1);
  });

  it("should exit edit mode when cancel button is clicked", async () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    await userEvent.click(cancelButton);

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Cancel" })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Save" })).not.toBeInTheDocument();
  });

  it("should exit edit mode when isSaved becomes true", async () => {
    const { rerender } = render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();

    const propsWithSaved = { ...mockProps, isSaved: true };
    rerender(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...propsWithSaved} />
      </MockFormProvider>
    );

    await waitFor(() => {
      expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    });
  });

  it("should display both entity type options in edit mode", async () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("radio", { name: infoLabels.labels.individual })).toBeInTheDocument();
    expect(screen.getByRole("radio", { name: infoLabels.labels.business })).toBeInTheDocument();
  });

  it("should show selected entity type in edit mode", async () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const individualRadio = screen.getByRole("radio", { name: infoLabels.labels.individual });
    expect(individualRadio).toBeChecked();
  });

  it("should handle entity type selection change", async () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const businessRadio = screen.getByRole("radio", { name: infoLabels.labels.business });
    await userEvent.click(businessRadio);

    expect(businessRadio).toBeChecked();
  });

  it("should show business name field when business entity type is selected", async () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const businessRadio = screen.getByRole("radio", { name: infoLabels.labels.business });
    await userEvent.click(businessRadio);

    await waitFor(() => {
      expect(screen.getByPlaceholderText(infoLabels.labels.businessName)).toBeInTheDocument();
    });
  });

  it("should hide business name field when individual entity type is selected", async () => {
    const businessLegalEntity = {
      ...legalEntity,
      entityType: "BUSINESS" as LegalEntityType
    };

    render(
      <MockFormProvider defaultValues={businessLegalEntity}>
        <LegalEntityForm {...mockProps} legalEntity={businessLegalEntity} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const individualRadio = screen.getByRole("radio", { name: infoLabels.labels.individual });
    await userEvent.click(individualRadio);

    await waitFor(() => {
      expect(screen.queryByPlaceholderText(infoLabels.labels.businessName)).not.toBeInTheDocument();
    });
  });

  it("should display country select in edit mode", async () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const countrySelect = screen.getByRole("button", { name: "United States" });
    expect(countrySelect).toBeInTheDocument();
  });

  it("should handle country selection change", async () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const countrySelect = screen.getByRole("button", { name: "United States" });
    await userEvent.click(countrySelect);
    await userEvent.click(screen.getByText("Canada"));

    expect(screen.getByText("Canada")).toBeInTheDocument();
  });

  it("should show checkbox for using address as mailing address in edit mode", async () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("checkbox", { name: infoLabels.labels.legalAddressAsMailingAddress })).toBeInTheDocument();
  });

  it("should call onChangeAsMailingAddress when checkbox is toggled", async () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const checkbox = screen.getByRole("checkbox", { name: infoLabels.labels.legalAddressAsMailingAddress });
    await userEvent.click(checkbox);

    expect(mockProps.onChangeAsMailingAddress).toHaveBeenCalledTimes(1);
  });

  it("should disable address fields when checkbox is checked", async () => {
    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const checkbox = screen.getByRole("checkbox", { name: infoLabels.labels.legalAddressAsMailingAddress });
    await userEvent.click(checkbox);

    await waitFor(() => {
      expect(screen.queryByPlaceholderText(infoLabels.labels.street)).not.toBeInTheDocument();
      expect(screen.queryByPlaceholderText(infoLabels.labels.city)).not.toBeInTheDocument();
      expect(screen.queryByPlaceholderText(infoLabels.labels.state)).not.toBeInTheDocument();
      expect(screen.queryByPlaceholderText(infoLabels.labels.zipCode)).not.toBeInTheDocument();
    });
  });

  it("should show success toast when information is saved", async () => {
    const propsWithSaved = {
      ...mockProps,
      isSaved: true
    };

    const { unmount } = renderWithToast(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...propsWithSaved} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(successToast).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          header: infoLabels.success.updatedInformationHeader,
          content: infoLabels.success.legalEntityType
        })
      }),
      expect.objectContaining({
        autoClose: expect.any(Number)
      })
    );

    unmount();
  });

  it("should calculate toast display time based on message length", async () => {
    const longMessage = "A".repeat(200);
    const propsWithLongMessage = {
      ...mockProps,
      isSaved: true,
      infoLabels: {
        ...mockProps.infoLabels,
        success: {
          ...mockProps.infoLabels.success,
          legalEntityType: longMessage
        }
      }
    };

    const { unmount } = renderWithToast(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...propsWithLongMessage} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(successToast).toHaveBeenCalledWith(
      expect.anything(),
      expect.objectContaining({
        autoClose: 7000
      })
    );

    unmount();
  });

  it("should show loading state when isLoader is true", () => {
    const propsWithLoader = {
      ...mockProps,
      isLoader: true
    };

    render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...propsWithLoader} />
      </MockFormProvider>
    );

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
  });

  it("should be accessible", async () => {
    let results: any;
    const { container } = render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  it("should handle missing onChange prop gracefully", () => {
    const propsWithoutOnChange = {
      ...mockProps,
      onChange: undefined
    };

    expect(() => {
      render(
        <MockFormProvider defaultValues={legalEntity}>
          <LegalEntityForm {...propsWithoutOnChange} />
        </MockFormProvider>
      );
    }).not.toThrow();
  });

  it("should handle empty countries array gracefully", () => {
    const propsWithEmptyCountries = {
      ...mockProps,
      countries: []
    };

    expect(() => {
      render(
        <MockFormProvider defaultValues={legalEntity}>
          <LegalEntityForm {...propsWithEmptyCountries} />
        </MockFormProvider>
      );
    }).not.toThrow();
  });

  it("should be memoized and not re-render unnecessarily", () => {
    const { rerender } = render(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    rerender(
      <MockFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...mockProps} />
      </MockFormProvider>
    );

    expect(screen.getByText(infoLabels.legalEntityType)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.entityType)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.street)).toBeInTheDocument();
  });
});