import { act, render, screen, waitFor } from "@testing-library/react";
import LegalEntityForm from "./LegalEntityForm";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import { aLegalEntityInformationPayload } from "@eait-playerexp-cn/creator-test-fixtures";
import { LegalInformationPayload, LegalEntityType } from "@eait-playerexp-cn/creator-types";
import { Country } from "@eait-playerexp-cn/metadata-types";
import formsRules from "@src/utils/FormRules/CreatorForm";
import { InformationPageLabels } from "@src/Translations/InformationPageLabels";

const successToast = jest.fn();
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: successToast
  })
}));

const TestFormProvider = ({ children, defaultValues = {} }: { children: React.ReactNode; defaultValues?: any }) => {
  const methods = useForm({
    defaultValues: {
      entityType: defaultValues.entityType || { value: "INDIVIDUAL" },
      businessName: defaultValues.businessName || "",
      street: defaultValues.street || "",
      city: defaultValues.city || "",
      country: defaultValues.country || null,
      state: defaultValues.state || "",
      zipCode: defaultValues.zipCode || "",
      ...defaultValues
    },
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("LegalEntityForm - Tests for legal entity information form functionality including rendering, edit mode, entity type selection, address fields, validation, toast messages, accessibility, and error handling", () => {
  const legalEntity: LegalInformationPayload = {
    ...aLegalEntityInformationPayload(),
    entityType: "INDIVIDUAL" as LegalEntityType,
    businessName: "Test Business LLC",
    street: "123 Main Street",
    city: "Test City",
    country: {
      code: "US",
      name: "United States"
    },
    state: "California",
    zipCode: "90210"
  };

  const countries: Country[] = [
    {
      value: "US",
      label: "United States",
      name: "United States"
    },
    {
      value: "CA",
      label: "Canada",
      name: "Canada"
    },
    {
      value: "UK",
      label: "United Kingdom",
      name: "United Kingdom"
    }
  ];

  const infoLabels = InformationPageLabels.infoLabels;

  const props = {
    infoLabels,
    rules: formsRules.rules(infoLabels),
    legalEntity,
    countries,
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close",
      browse: "Browse"
    },
    onChangeAsMailingAddress: jest.fn(),
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show legal entity form correctly", () => {
    render(
      <TestFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...props} />
      </TestFormProvider>
    );

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    expect(screen.getByText(infoLabels.legalEntityType)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.legalEntityDescription)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.entityType)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.street)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.city)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.country)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.state)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.zipCode)).toBeInTheDocument();
  });

  it("should show business name when entity type is BUSINESS", () => {
    const businessLegalEntity = {
      ...legalEntity,
      entityType: "BUSINESS" as LegalEntityType
    };

    render(
      <TestFormProvider defaultValues={businessLegalEntity}>
        <LegalEntityForm {...props} legalEntity={businessLegalEntity} />
      </TestFormProvider>
    );

    expect(screen.getByText(infoLabels.labels.businessName)).toBeInTheDocument();
    expect(screen.getByText("Test Business LLC")).toBeInTheDocument();
  });

  it("should enable edit mode when edit button is clicked", async () => {
    render(
      <TestFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...{...props, isSaved: undefined}} />
      </TestFormProvider>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.individual)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.business)).toBeInTheDocument();
    expect(screen.getByDisplayValue("123 Main Street")).toBeInTheDocument();
    expect(screen.getByDisplayValue("Test City")).toBeInTheDocument();
    expect(screen.getByDisplayValue("California")).toBeInTheDocument();
    expect(screen.getByDisplayValue("90210")).toBeInTheDocument();
    expect(screen.getByRole("checkbox")).toBeInTheDocument();
    expect(screen.getByText("Address is the same as my mailing address.")).toBeInTheDocument();
    expect(props.onChange).toHaveBeenCalledTimes(1);
  });

  it("should exit edit mode when cancel button is clicked", async () => {
    render(
      <TestFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...props} />
      </TestFormProvider>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));
    expect(screen.queryByRole("button", { name: "Edit" })).not.toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();

    await userEvent.click(screen.getByRole("button", { name: "Cancel" }));

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Cancel" })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Save" })).not.toBeInTheDocument();
  });

  it("should exit edit mode when isSaved becomes true", async () => {
    const { rerender } = render(
      <TestFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...props} />
      </TestFormProvider>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();

    const propsWithSaved = { ...props, isSaved: true };
    rerender(
      <TestFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...propsWithSaved} />
      </TestFormProvider>
    );

    await waitFor(() => {
      expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    });
  });

  it("shows radio buttons for entity type options in edit mode", async () => {
    render(
      <TestFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...props} />
      </TestFormProvider>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(screen.getByText(infoLabels.labels.individual)).toHaveClass("radio-btn-label");
    expect(screen.getByText(infoLabels.labels.business)).toHaveClass("radio-btn-label");
  });

  it("should handle entity type selection change", async () => {
    render(
      <TestFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...props} />
      </TestFormProvider>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    const businessOption = screen.getByText(infoLabels.labels.business);
    await userEvent.click(businessOption);

    await waitFor(() => {
      const radioItem = businessOption.closest('.radio-btn-container').querySelector('.radio-btn-item');
      expect(radioItem).toHaveClass('selected');
    });
  });

  it("should call onChangeAsMailingAddress when checkbox is toggled", async () => {
    render(
      <TestFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...props} />
      </TestFormProvider>
    );
    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    await userEvent.click(screen.getByRole("checkbox"));

    expect(props.onChangeAsMailingAddress).toHaveBeenCalledTimes(1);
  });

  it("should show success toast when information is saved", async () => {
    const propsWithSaved = {
      ...props,
      isSaved: true
    };

    const { unmount } = renderWithToast(
      <TestFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...propsWithSaved} />
      </TestFormProvider>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(successToast).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          header: infoLabels.success.updatedInformationHeader,
          content: infoLabels.success.legalEntityType
        })
      }),
      expect.objectContaining({
        autoClose: expect.any(Number)
      })
    );

    unmount();
  });

  it("should be accessible", async () => {
    let results: any;
    const { container } = render(
      <TestFormProvider defaultValues={legalEntity}>
        <LegalEntityForm {...props} />
      </TestFormProvider>
    );

    results = await axe(container);

    expect(results).toHaveNoViolations();
  });

  it("should handle missing onChange prop gracefully", () => {
    const propsWithoutOnChange = {
      ...props,
      onChange: undefined
    };

    expect(() => {
      render(
        <TestFormProvider defaultValues={legalEntity}>
          <LegalEntityForm {...propsWithoutOnChange} />
        </TestFormProvider>
      );
    }).not.toThrow();
  });
});