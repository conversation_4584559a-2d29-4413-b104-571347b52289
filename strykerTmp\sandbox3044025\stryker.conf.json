{"$schema": "./node_modules/@stryker-mutator/core/schema/stryker-schema.json", "_comment": "Please see the guide for more information: https://stryker-mutator.io/docs/stryker-js/guides/react", "testRunner": "jest", "allowEmpty": true, "incremental": true, "jest": {"projectType": "custom", "configFile": "jest.config.js", "enableFindRelatedTests": false}, "ignorePatterns": ["reports", "src/index.tx", "src/**/*.stories.{js,jsx,ts,tsx}"], "thresholds": {"high": 90, "low": 70, "break": 100}, "coverageAnalysis": "perTest", "ignoreStatic": true, "reporters": ["progress", "clear-text", "html"], "checkers": ["typescript"], "tsconfigFile": "tsconfig.json", "concurrency": 5, "timeoutMS": 120000, "tempDirName": "strykerTmp", "cleanTempDir": "always"}