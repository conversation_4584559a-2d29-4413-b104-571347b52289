import { useCallback, useEffect, useRef, useState } from "react";
import { debounce } from "./common";

//-------------------------------------
// Custom hook to determine screen
//-------------------------------------
export const useDetectScreen = (width: number): boolean => {
  const [widthAchieved, setWidthAchieved] = useState(false);

  const updateTarget = (e: MediaQueryListEvent) => {
    if (e.matches) {
      setWidthAchieved(true);
    } else {
      setWidthAchieved(false);
    }
  };

  const handler = debounce(updateTarget, 100); // debounce will ensure one execution

  useEffect(() => {
    if (window) {
      const media = window.matchMedia(`(max-width: ${width}px)`);
      media.addEventListener("change", handler);
      if (media.matches) setWidthAchieved(true);
      return () => media.removeEventListener("change", handler);
    }
  }, []);

  return widthAchieved;
};

export function useIsMounted(): () => boolean {
  const isMounted = useRef<boolean>(false);

  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  return useCallback(() => isMounted.current, []);
}

interface UseScriptParams {
  url?: string;
  html?: string | (() => string);
  node?: Document | HTMLElement | undefined;
  type?: string;
  async?: boolean;
  defer?: boolean;
  headScript?: boolean;
  callback?: () => void;
}

export function useScript({
  url = "",
  html = "",
  node = document,
  type = "text/javascript",
  async = false,
  defer = false,
  headScript = false,
  callback
}: UseScriptParams): Document | HTMLElement | undefined {
  useEffect(() => {
    const htmlVal = typeof html === "function" ? html() : html;
    const appendTo = node || document;
    if (appendTo && (url || htmlVal)) {
      const script = document.createElement("script");
      script.type = type;
      if (url) script.src = url;
      script.async = async;
      script.defer = defer;
      script.onload = () => {
        if (callback) callback();
      };

      if (htmlVal && !url) script.innerHTML = htmlVal;
      if (appendTo instanceof Document) {
        if (headScript) {
          appendTo.head?.appendChild(script);
        } else {
          appendTo.body?.appendChild(script);
        }
      }

      return () => {
        if (appendTo instanceof Document) {
          if (headScript) {
            appendTo.head?.removeChild(script);
          } else {
            appendTo.body?.removeChild(script);
          }
        }
      };
    }
  }, [url, html, node, type, async, defer, headScript, callback]);

  return node;
}

interface UseAsyncReturn<T, E = unknown> {
  execute: (data?: unknown) => Promise<void>;
  pending: boolean;
  value: T | null;
  error: E | null;
}

export const useAsync = <T, E = unknown>(
  asyncFunction: (data?: unknown) => Promise<T>,
  immediate: boolean = true
): UseAsyncReturn<T, E> => {
  const [pending, setPending] = useState<boolean>(false);
  const [value, setValue] = useState<T | null>(null);
  const [error, setError] = useState<E | null>(null);

  const execute = useCallback(
    (data?: unknown) => {
      setPending(true);
      setValue(null);
      setError(null);
      return asyncFunction(data)
        .then((response: T) => setValue(response))
        .catch((error: E) => setError(error))
        .finally(() => setPending(false));
    },
    [asyncFunction]
  );

  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [execute, immediate]);

  return { execute, pending, value, error };
};

type WindowSize = {
  width: number | undefined;
  height: number | undefined;
};

export function useWindowSize(): WindowSize {
  const [windowSize, setWindowSize] = useState<WindowSize>({
    width: undefined,
    height: undefined
  });

  useEffect(() => {
    if (typeof window === "undefined") return;

    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    handleResize();

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return windowSize;
}

//----------------------------------------
// PactSafe executable Script
//----------------------------------------
export function getPactSafeScript(url: string, id: string, pactSafeMessageListner: string): string {
  const innerHTML = `
 (function () {
    var EmbeddedSignature = new PactSafeEmbedded('${id}}');
    EmbeddedSignature.open({
      // signing URL is generated by calling:
    // GET => /v1.1/requests/{request_id}/url?signer_id={signer_id}
    url: '${url}',
    
    allow_cancel: true,
    debug: true,
    from_left: 0,
    from_top: 0,
    message_listener: ${pactSafeMessageListner}
  });
}());
  `;

  return innerHTML;
}

export function useUpdateEffect(effect: () => void, dependencies: string[] = []): void {
  const isInitialMount = useRef(true);

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
    } else {
      return effect();
    }
  }, dependencies);
}
