.profile-communication-preferences {
  @apply flex w-full flex-col items-center justify-center pb-[8px] md:pb-[115px] xl:min-w-[790px] xl:pb-[88px];
}
.profile-communication-preferences form {
  @apply w-full border-t border-gray-40 border-opacity-[0.33] pt-meas16;
}
.profile-communication-preferences-intro,
.profile-preferred-discord,
.profile-preferred-email,
.profile-preferred-phone,
.profile-preferred-language,
.profile-preferred-content-language {
  @apply grid w-full grid-cols-1 gap-y-[10px] pb-[25px] md:grid-cols-3;
}
.profile-preferred-content-language {
  @apply pb-[13px];
}
.profile-preferred-language {
  @apply border-none;
}
.profile-communication-preferences-intro-title,
.profile-payment-information-intro-title,
.profile-legal-documents-intro-title {
  @apply col-span-3 pb-meas4 font-display-regular font-bold text-gray-10 xs:text-mobile-h4 md:col-span-2 md:text-tablet-h4 lg:text-desktop-h4;
}
.profile-communication-preferences-intro-description {
  @apply col-span-3 flex pb-meas10 font-text-regular  text-black xs:text-mobile-body-large md:col-span-2 md:w-[640px] md:text-tablet-body-large lg:text-desktop-body-large;
}
.profile-preferred-email-sub-tile,
.profile-preferred-phone-sub-tile,
.profile-preferred-language-sub-tile,
.profile-preferred-content-language-sub-tile {
  @apply col-span-3 pb-meas4 font-display-regular font-bold text-gray-10 xs:text-mobile-h5 md:col-span-2 md:text-tablet-h5 lg:text-desktop-h5;
}
.profile-communication-preferences > .mg-communication-row.discord > .mg-communication-title.discord {
  @apply font-display-regular font-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}
.profile-preferred-discord-description,
.profile-preferred-email-description,
.profile-preferred-language-description,
.profile-preferred-content-language-description {
  @apply col-span-3 flex pb-meas18 font-text-regular text-black xs:text-mobile-body-default md:col-span-2 md:w-[640px] md:text-tablet-body-default lg:text-desktop-body-default;
}
.communication-preference-field-title {
  @apply col-span-3 flex items-center font-text-regular text-black xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1;
}
.communication-preference-content-languages-field {
  @apply col-span-3 flex flex-wrap font-text-regular text-gray-10 xs:text-mobile-body-default md:col-span-2 md:text-tablet-body-default lg:text-desktop-body-default;
}
.communication-preference-content-languages-field-edit {
  @apply col-span-3 mb-meas6 flex font-text-regular text-gray-10 xs:text-mobile-body-default md:block md:w-[640px] md:text-tablet-body-default lg:text-desktop-body-default;
}
.communication-preference-field {
  @apply col-span-3 flex w-full break-all font-text-regular text-black xs:text-mobile-body-default md:w-[320px] md:text-tablet-body-default lg:text-desktop-body-default;
}
.communication-preference-field > label,
.communication-preference-field .input-box,
.communication-preference-field .select-box {
  @apply w-full;
}
.communication-preference-field .select-header-title,
.communication-preference-field .select-header-label {
  @apply w-full;
}
.communication-preference-field .select-list,
.select-scroll-list {
  @apply w-[99.7%];
}
.language-head,
.hardware-partner-head {
  @apply mb-meas6 mr-meas6 flex items-center rounded-[100px] bg-gray-20 px-meas4 text-gray-90 xs:text-mobile-body-small md:text-tablet-body-small lg:text-desktop-body-small;
}

.profile-communication-preferences .discord {
  @apply text-black;
}
