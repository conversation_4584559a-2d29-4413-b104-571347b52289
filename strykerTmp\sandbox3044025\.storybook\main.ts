// @ts-nocheck
import type { StorybookConfig } from "@storybook/react-webpack5";

const config: StorybookConfig = {
  stories: ["../src/**/*.stories.@(tsx)"],
  addons: [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    "@storybook/addon-interactions",
    "@storybook/preset-create-react-app",
    "@storybook/addon-styling-webpack"
  ],
  framework: "@storybook/react-webpack5",
  docs: {
    autodocs: true
  },
  core: {
    builder: "@storybook/builder-webpack5",
    disableTelemetry: true
  },
  staticDirs: ["../public"],
  webpackFinal: async (config) => {
    if (!config.resolve) config.resolve = {};
    if (!config.resolve.fallback) config.resolve.fallback = {};
    config.resolve.fallback["zlib"] = false;
    return config;
  }
};

export default config;
