import { render, screen } from "@testing-library/react";
import React from "react";
import Example from "./Example";
import { axe } from "jest-axe";

describe("Example", () => {
  const greeting = "Hello World!";

  it("shows button with a given greeting message", () => {
    render(<Example greeting={greeting} />);

    expect(screen.getByRole("button", { name: greeting })).toBeInTheDocument();
  });

  it("is accessible", async () => {
    const { container } = render(<Example greeting={greeting} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
