// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import { Controller, FieldValues, useFormContext } from "react-hook-form";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import { Select } from "@eait-playerexp-cn/core-ui-kit";
import ProfileFormAction from "../ProfileFormAction/ProfileFormAction";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import FormTitle from "../FormTitle/FormTitle";
import { Checkbox, Input, RadioButton } from "@eait-playerexp-cn/core-ui-kit";
import { ButtonLabels, InfoLabels, Overwrite } from "../types";
import { Country } from "@eait-playerexp-cn/metadata-types";
import { LegalEntityType, LegalInformationPayload } from "@eait-playerexp-cn/creator-types";
import { CreatorFormRules } from "@src/utils/FormRules/CreatorForm";
interface LegalEntityFormProps {
  infoLabels: InfoLabels;
  rules: CreatorFormRules;
  legalEntity: LegalInformationPayload;
  countries: Country[];
  buttons: ButtonLabels;
  onChangeAsMailingAddress: (data: FieldValues, isChecked: boolean) => void;
  onChange: () => void;
  isSaved: boolean;
  isLoader: boolean;
}
type LegalInformation = Overwrite<LegalInformationPayload, {
  country: Country;
  entityType: {
    value: LegalEntityType;
  };
}>;
const LegalEntityForm = ({
  infoLabels,
  rules,
  legalEntity,
  countries,
  buttons,
  onChange,
  isSaved = stryMutAct_9fa48("436") ? true : (stryCov_9fa48("436"), false),
  onChangeAsMailingAddress,
  isLoader
}: LegalEntityFormProps) => {
  if (stryMutAct_9fa48("437")) {
    {}
  } else {
    stryCov_9fa48("437");
    const {
      control,
      reset,
      getValues,
      setValue
    } = useFormContext();
    const [isEdit, setIsEdit] = useState(stryMutAct_9fa48("438") ? true : (stryCov_9fa48("438"), false));
    const [asMailingAddress, setAsMailingAddress] = useState(stryMutAct_9fa48("439") ? true : (stryCov_9fa48("439"), false));
    const [entityType, setEntityType] = useState(null);
    const [legalEntityValue, setLegalEntityValue] = useState(null);
    const {
      success: successToast
    } = useToast();
    const timetoDisplay = stryMutAct_9fa48("440") ? Math.max(Math.max(infoLabels.success.legalEntityType.length * 50, 2000), 7000) : (stryCov_9fa48("440"), Math.min(stryMutAct_9fa48("441") ? Math.min(infoLabels.success.legalEntityType.length * 50, 2000) : (stryCov_9fa48("441"), Math.max(stryMutAct_9fa48("442") ? infoLabels.success.legalEntityType.length / 50 : (stryCov_9fa48("442"), infoLabels.success.legalEntityType.length * 50), 2000)), 7000));
    const onEditChange = useCallback(isChecked => {
      if (stryMutAct_9fa48("443")) {
        {}
      } else {
        stryCov_9fa48("443");
        setIsEdit(isChecked);
        setAsMailingAddress(stryMutAct_9fa48("444") ? isChecked : (stryCov_9fa48("444"), !isChecked));
        if (stryMutAct_9fa48("446") ? false : stryMutAct_9fa48("445") ? true : (stryCov_9fa48("445", "446"), onChange)) onChange();
        if (stryMutAct_9fa48("449") ? !isChecked || legalEntityValue : stryMutAct_9fa48("448") ? false : stryMutAct_9fa48("447") ? true : (stryCov_9fa48("447", "448", "449"), (stryMutAct_9fa48("450") ? isChecked : (stryCov_9fa48("450"), !isChecked)) && legalEntityValue)) {
          if (stryMutAct_9fa48("451")) {
            {}
          } else {
            stryCov_9fa48("451");
            setEntityType(legalEntityValue.entityType);
          }
        } else {
          if (stryMutAct_9fa48("452")) {
            {}
          } else {
            stryCov_9fa48("452");
            const values = getValues();
            if (stryMutAct_9fa48("454") ? false : stryMutAct_9fa48("453") ? true : (stryCov_9fa48("453", "454"), values)) {
              if (stryMutAct_9fa48("455")) {
                {}
              } else {
                stryCov_9fa48("455");
                setEntityType(values.entityType);
              }
            }
          }
        }
      }
    }, stryMutAct_9fa48("456") ? [] : (stryCov_9fa48("456"), [getValues, legalEntityValue, onChange]));
    const LegalEntityOptions = useMemo(() => {
      if (stryMutAct_9fa48("457")) {
        {}
      } else {
        stryCov_9fa48("457");
        return stryMutAct_9fa48("458") ? [] : (stryCov_9fa48("458"), [stryMutAct_9fa48("459") ? {} : (stryCov_9fa48("459"), {
          value: stryMutAct_9fa48("460") ? "" : (stryCov_9fa48("460"), "INDIVIDUAL"),
          label: infoLabels.labels.individual
        }), stryMutAct_9fa48("461") ? {} : (stryCov_9fa48("461"), {
          value: stryMutAct_9fa48("462") ? "" : (stryCov_9fa48("462"), "BUSINESS"),
          label: infoLabels.labels.business
        })]);
      }
    }, stryMutAct_9fa48("463") ? [] : (stryCov_9fa48("463"), [infoLabels.labels.business, infoLabels.labels.individual]));
    useEffect(() => {
      if (stryMutAct_9fa48("464")) {
        {}
      } else {
        stryCov_9fa48("464");
        if (stryMutAct_9fa48("467") ? isEdit || isSaved : stryMutAct_9fa48("466") ? false : stryMutAct_9fa48("465") ? true : (stryCov_9fa48("465", "466", "467"), isEdit && isSaved)) setIsEdit(stryMutAct_9fa48("468") ? true : (stryCov_9fa48("468"), false));
      }
    }, stryMutAct_9fa48("469") ? [] : (stryCov_9fa48("469"), [isSaved, isEdit]));
    const updateAsMailingAddress = useCallback(event => {
      if (stryMutAct_9fa48("470")) {
        {}
      } else {
        stryCov_9fa48("470");
        if (stryMutAct_9fa48("472") ? false : stryMutAct_9fa48("471") ? true : (stryCov_9fa48("471", "472"), onChangeAsMailingAddress)) onChangeAsMailingAddress(getValues(), event.target.checked);
        setAsMailingAddress(event.target.checked);
      }
    }, stryMutAct_9fa48("473") ? [] : (stryCov_9fa48("473"), [getValues, onChangeAsMailingAddress]));
    useEffect(() => {
      if (stryMutAct_9fa48("474")) {
        {}
      } else {
        stryCov_9fa48("474");
        const values = {
          ...legalEntity
        } as unknown as LegalInformation;
        if (stryMutAct_9fa48("478") ? legalEntity.country?.code : stryMutAct_9fa48("477") ? legalEntity?.country.code : stryMutAct_9fa48("476") ? false : stryMutAct_9fa48("475") ? true : (stryCov_9fa48("475", "476", "477", "478"), legalEntity?.country?.code)) {
          if (stryMutAct_9fa48("479")) {
            {}
          } else {
            stryCov_9fa48("479");
            values.country = {
              value: legalEntity.country.code,
              label: legalEntity.country.name
            } as unknown as Country;
          }
        }
        if (stryMutAct_9fa48("482") ? typeof legalEntity?.entityType !== "string" : stryMutAct_9fa48("481") ? false : stryMutAct_9fa48("480") ? true : (stryCov_9fa48("480", "481", "482"), typeof (stryMutAct_9fa48("483") ? legalEntity.entityType : (stryCov_9fa48("483"), legalEntity?.entityType)) === (stryMutAct_9fa48("484") ? "" : (stryCov_9fa48("484"), "string")))) {
          if (stryMutAct_9fa48("485")) {
            {}
          } else {
            stryCov_9fa48("485");
            values.entityType = stryMutAct_9fa48("486") ? {} : (stryCov_9fa48("486"), {
              value: legalEntity.entityType
            });
          }
        }
        if (stryMutAct_9fa48("488") ? false : stryMutAct_9fa48("487") ? true : (stryCov_9fa48("487", "488"), values)) {
          if (stryMutAct_9fa48("489")) {
            {}
          } else {
            stryCov_9fa48("489");
            setEntityType(values.entityType);
            setLegalEntityValue(values);
            reset(values);
          }
        }
      }
    }, stryMutAct_9fa48("490") ? [] : (stryCov_9fa48("490"), [legalEntity, reset]));
    const updateEntityType = useCallback((item, field) => {
      if (stryMutAct_9fa48("491")) {
        {}
      } else {
        stryCov_9fa48("491");
        setEntityType(item);
        field.onChange(item);
        if (stryMutAct_9fa48("494") ? item.value !== "INDIVIDUAL" : stryMutAct_9fa48("493") ? false : stryMutAct_9fa48("492") ? true : (stryCov_9fa48("492", "493", "494"), item.value === (stryMutAct_9fa48("495") ? "" : (stryCov_9fa48("495"), "INDIVIDUAL")))) {
          if (stryMutAct_9fa48("496")) {
            {}
          } else {
            stryCov_9fa48("496");
            const values = getValues();
            delete values.businessName;
            reset(values);
          }
        }
      }
    }, stryMutAct_9fa48("497") ? [] : (stryCov_9fa48("497"), [getValues, reset]));
    useEffect(() => {
      if (stryMutAct_9fa48("498")) {
        {}
      } else {
        stryCov_9fa48("498");
        if (stryMutAct_9fa48("501") ? legalEntityValue || Array.isArray(countries) : stryMutAct_9fa48("500") ? false : stryMutAct_9fa48("499") ? true : (stryCov_9fa48("499", "500", "501"), legalEntityValue && Array.isArray(countries))) {
          if (stryMutAct_9fa48("502")) {
            {}
          } else {
            stryCov_9fa48("502");
            setValue(stryMutAct_9fa48("503") ? "" : (stryCov_9fa48("503"), "country"), stryMutAct_9fa48("506") ? legalEntityValue?.country && countries[0] : stryMutAct_9fa48("505") ? false : stryMutAct_9fa48("504") ? true : (stryCov_9fa48("504", "505", "506"), (stryMutAct_9fa48("507") ? legalEntityValue.country : (stryCov_9fa48("507"), legalEntityValue?.country)) || countries[0]));
          }
        }
      }
    }, stryMutAct_9fa48("508") ? [] : (stryCov_9fa48("508"), [legalEntityValue, countries]));
    return stryMutAct_9fa48("511") ? legalEntityValue || <>
        {isSaved && isEdit && successToast(<Toast header={infoLabels.success.updatedInformationHeader} content={infoLabels.success.legalEntityType} />, {
        autoClose: timetoDisplay
      })}
        <div className="legal-entity-information">
          <div className="form-sub-title-and-action">
            <FormTitle subTitle={infoLabels.legalEntityType} />

            <ProfileFormAction {...{
            buttons,
            action: onEditChange,
            isSaved,
            isLoader
          }} />
          </div>
          <div className="profile-legal-entity-description">{infoLabels.legalEntityDescription}</div>

          <div className="legal-entity-field-title legal-entity-type-title">{infoLabels.labels.entityType}</div>
          <div className="legal-entity-field">
            {!isEdit && LegalEntityOptions.filter(item => item.value === legalEntityValue.entityType?.value)[0]?.label}
            {isEdit && <Controller control={control} name="entityType" rules={rules.entityType} render={({
            field,
            fieldState: {
              error
            }
          }) => <RadioButton errorMessage={error && error.message || ""} {...field} selectedOption={field.value} onChange={item => updateEntityType(item, field)} options={LegalEntityOptions} />} />}
          </div>

          {entityType?.value === "BUSINESS" && <>
              <div className="legal-entity-field-title inline">{infoLabels.labels.businessName}</div>
              <div className="legal-entity-field">
                {!isEdit && legalEntityValue.businessName}
                {isEdit && <Controller control={control} name="businessName" rules={rules.businessName} render={({
              field,
              fieldState: {
                error
              }
            }) => <Input id="legal-entity-businessname" errorMessage={error && error.message || ""} {...field} helpText={infoLabels.info.businessName} placeholder={infoLabels.labels.businessName} />} />}
              </div>
            </>}

          <div className="legal-entity-field-title">{infoLabels.labels.street}</div>
          <div className="legal-entity-field">
            {!isEdit || asMailingAddress ? legalEntityValue.street : !asMailingAddress && <Controller control={control} name="street" rules={rules.street} render={({
            field,
            fieldState: {
              error
            }
          }) => <Input id="legal-entity-street" errorMessage={error && error.message || ""} {...field} placeholder={infoLabels.labels.street} />} />}
          </div>

          <div className="legal-entity-field-title">{infoLabels.labels.city}</div>
          <div className="legal-entity-field">
            {!isEdit || asMailingAddress ? legalEntityValue.city : !asMailingAddress && <Controller control={control} name="city" rules={rules.city} render={({
            field,
            fieldState: {
              error
            }
          }) => <Input id="legal-entity-city" errorMessage={error && error.message || ""} {...field} placeholder={infoLabels.labels.city} ariaLabel={infoLabels.labels.city} />} />}
          </div>

          <div className="legal-entity-field-title">{infoLabels.labels.country}</div>
          <div className="legal-entity-field">
            {!isEdit || asMailingAddress ? legalEntityValue.country?.label : !asMailingAddress && <Controller control={control} name="country" rules={rules.country} render={({
            field,
            fieldState: {
              error
            }
          }) => <Select id="legal-entity-country" selectedOption={legalEntityValue.country} errorMessage={error && error.message} options={countries} onChange={item => {
            field.onChange(item);
          }} />} />}
          </div>

          <div className="legal-entity-field-title">{infoLabels.labels.state}</div>
          <div className="legal-entity-field">
            {!isEdit || asMailingAddress ? legalEntityValue.state : !asMailingAddress && <Controller control={control} name="state" rules={rules.state} render={({
            field,
            fieldState: {
              error
            }
          }) => <Input id="legal-entity-state" errorMessage={error && error.message || ""} {...field} placeholder={infoLabels.labels.state} />} />}
          </div>

          <div className="legal-entity-field-title">{infoLabels.labels.zipCode}</div>
          <div className="legal-entity-field">
            {!isEdit || asMailingAddress ? legalEntityValue.zipCode : !asMailingAddress && <Controller control={control} name="zipCode" rules={rules.zipCode} render={({
            field,
            fieldState: {
              error
            }
          }) => <Input id="legal-entity-zipcode" errorMessage={error && error.message || ""} {...field} placeholder={infoLabels.labels.zipCode} />} />}
          </div>

          {isEdit && <>
              <div className="legal-entity-field-title">{""}</div>
              <div className="legal-entity-field">
                <Checkbox options={[{
              id: "asMailingAddress",
              label: infoLabels.labels.legalAddressAsMailingAddress,
              isChecked: asMailingAddress,
              onChange: updateAsMailingAddress,
              dark: true
            }]} />
              </div>
            </>}
        </div>
      </> : stryMutAct_9fa48("510") ? false : stryMutAct_9fa48("509") ? true : (stryCov_9fa48("509", "510", "511"), legalEntityValue && <>
        {stryMutAct_9fa48("514") ? isSaved && isEdit || successToast(<Toast header={infoLabels.success.updatedInformationHeader} content={infoLabels.success.legalEntityType} />, {
        autoClose: timetoDisplay
      }) : stryMutAct_9fa48("513") ? false : stryMutAct_9fa48("512") ? true : (stryCov_9fa48("512", "513", "514"), (stryMutAct_9fa48("516") ? isSaved || isEdit : stryMutAct_9fa48("515") ? true : (stryCov_9fa48("515", "516"), isSaved && isEdit)) && successToast(<Toast header={infoLabels.success.updatedInformationHeader} content={infoLabels.success.legalEntityType} />, stryMutAct_9fa48("517") ? {} : (stryCov_9fa48("517"), {
        autoClose: timetoDisplay
      })))}
        <div className="legal-entity-information">
          <div className="form-sub-title-and-action">
            <FormTitle subTitle={infoLabels.legalEntityType} />

            <ProfileFormAction {...stryMutAct_9fa48("518") ? {} : (stryCov_9fa48("518"), {
            buttons,
            action: onEditChange,
            isSaved,
            isLoader
          })} />
          </div>
          <div className="profile-legal-entity-description">{infoLabels.legalEntityDescription}</div>

          <div className="legal-entity-field-title legal-entity-type-title">{infoLabels.labels.entityType}</div>
          <div className="legal-entity-field">
            {stryMutAct_9fa48("521") ? !isEdit || LegalEntityOptions.filter(item => item.value === legalEntityValue.entityType?.value)[0]?.label : stryMutAct_9fa48("520") ? false : stryMutAct_9fa48("519") ? true : (stryCov_9fa48("519", "520", "521"), (stryMutAct_9fa48("522") ? isEdit : (stryCov_9fa48("522"), !isEdit)) && (stryMutAct_9fa48("524") ? LegalEntityOptions[0]?.label : stryMutAct_9fa48("523") ? LegalEntityOptions.filter(item => item.value === legalEntityValue.entityType?.value)[0].label : (stryCov_9fa48("523", "524"), LegalEntityOptions.filter(stryMutAct_9fa48("525") ? () => undefined : (stryCov_9fa48("525"), item => stryMutAct_9fa48("528") ? item.value !== legalEntityValue.entityType?.value : stryMutAct_9fa48("527") ? false : stryMutAct_9fa48("526") ? true : (stryCov_9fa48("526", "527", "528"), item.value === (stryMutAct_9fa48("529") ? legalEntityValue.entityType.value : (stryCov_9fa48("529"), legalEntityValue.entityType?.value)))))[0]?.label)))}
            {stryMutAct_9fa48("532") ? isEdit || <Controller control={control} name="entityType" rules={rules.entityType} render={({
            field,
            fieldState: {
              error
            }
          }) => <RadioButton errorMessage={error && error.message || ""} {...field} selectedOption={field.value} onChange={item => updateEntityType(item, field)} options={LegalEntityOptions} />} /> : stryMutAct_9fa48("531") ? false : stryMutAct_9fa48("530") ? true : (stryCov_9fa48("530", "531", "532"), isEdit && <Controller control={control} name="entityType" rules={rules.entityType} render={stryMutAct_9fa48("533") ? () => undefined : (stryCov_9fa48("533"), ({
            field,
            fieldState: {
              error
            }
          }) => <RadioButton errorMessage={stryMutAct_9fa48("536") ? error && error.message && "" : stryMutAct_9fa48("535") ? false : stryMutAct_9fa48("534") ? true : (stryCov_9fa48("534", "535", "536"), (stryMutAct_9fa48("538") ? error || error.message : stryMutAct_9fa48("537") ? false : (stryCov_9fa48("537", "538"), error && error.message)) || (stryMutAct_9fa48("539") ? "Stryker was here!" : (stryCov_9fa48("539"), "")))} {...field} selectedOption={field.value} onChange={stryMutAct_9fa48("540") ? () => undefined : (stryCov_9fa48("540"), item => updateEntityType(item, field))} options={LegalEntityOptions} />)} />)}
          </div>

          {stryMutAct_9fa48("543") ? entityType?.value === "BUSINESS" || <>
              <div className="legal-entity-field-title inline">{infoLabels.labels.businessName}</div>
              <div className="legal-entity-field">
                {!isEdit && legalEntityValue.businessName}
                {isEdit && <Controller control={control} name="businessName" rules={rules.businessName} render={({
              field,
              fieldState: {
                error
              }
            }) => <Input id="legal-entity-businessname" errorMessage={error && error.message || ""} {...field} helpText={infoLabels.info.businessName} placeholder={infoLabels.labels.businessName} />} />}
              </div>
            </> : stryMutAct_9fa48("542") ? false : stryMutAct_9fa48("541") ? true : (stryCov_9fa48("541", "542", "543"), (stryMutAct_9fa48("545") ? entityType?.value !== "BUSINESS" : stryMutAct_9fa48("544") ? true : (stryCov_9fa48("544", "545"), (stryMutAct_9fa48("546") ? entityType.value : (stryCov_9fa48("546"), entityType?.value)) === (stryMutAct_9fa48("547") ? "" : (stryCov_9fa48("547"), "BUSINESS")))) && <>
              <div className="legal-entity-field-title inline">{infoLabels.labels.businessName}</div>
              <div className="legal-entity-field">
                {stryMutAct_9fa48("550") ? !isEdit || legalEntityValue.businessName : stryMutAct_9fa48("549") ? false : stryMutAct_9fa48("548") ? true : (stryCov_9fa48("548", "549", "550"), (stryMutAct_9fa48("551") ? isEdit : (stryCov_9fa48("551"), !isEdit)) && legalEntityValue.businessName)}
                {stryMutAct_9fa48("554") ? isEdit || <Controller control={control} name="businessName" rules={rules.businessName} render={({
              field,
              fieldState: {
                error
              }
            }) => <Input id="legal-entity-businessname" errorMessage={error && error.message || ""} {...field} helpText={infoLabels.info.businessName} placeholder={infoLabels.labels.businessName} />} /> : stryMutAct_9fa48("553") ? false : stryMutAct_9fa48("552") ? true : (stryCov_9fa48("552", "553", "554"), isEdit && <Controller control={control} name="businessName" rules={rules.businessName} render={stryMutAct_9fa48("555") ? () => undefined : (stryCov_9fa48("555"), ({
              field,
              fieldState: {
                error
              }
            }) => <Input id="legal-entity-businessname" errorMessage={stryMutAct_9fa48("558") ? error && error.message && "" : stryMutAct_9fa48("557") ? false : stryMutAct_9fa48("556") ? true : (stryCov_9fa48("556", "557", "558"), (stryMutAct_9fa48("560") ? error || error.message : stryMutAct_9fa48("559") ? false : (stryCov_9fa48("559", "560"), error && error.message)) || (stryMutAct_9fa48("561") ? "Stryker was here!" : (stryCov_9fa48("561"), "")))} {...field} helpText={infoLabels.info.businessName} placeholder={infoLabels.labels.businessName} />)} />)}
              </div>
            </>)}

          <div className="legal-entity-field-title">{infoLabels.labels.street}</div>
          <div className="legal-entity-field">
            {(stryMutAct_9fa48("564") ? !isEdit && asMailingAddress : stryMutAct_9fa48("563") ? false : stryMutAct_9fa48("562") ? true : (stryCov_9fa48("562", "563", "564"), (stryMutAct_9fa48("565") ? isEdit : (stryCov_9fa48("565"), !isEdit)) || asMailingAddress)) ? legalEntityValue.street : stryMutAct_9fa48("568") ? !asMailingAddress || <Controller control={control} name="street" rules={rules.street} render={({
            field,
            fieldState: {
              error
            }
          }) => <Input id="legal-entity-street" errorMessage={error && error.message || ""} {...field} placeholder={infoLabels.labels.street} />} /> : stryMutAct_9fa48("567") ? false : stryMutAct_9fa48("566") ? true : (stryCov_9fa48("566", "567", "568"), (stryMutAct_9fa48("569") ? asMailingAddress : (stryCov_9fa48("569"), !asMailingAddress)) && <Controller control={control} name="street" rules={rules.street} render={stryMutAct_9fa48("570") ? () => undefined : (stryCov_9fa48("570"), ({
            field,
            fieldState: {
              error
            }
          }) => <Input id="legal-entity-street" errorMessage={stryMutAct_9fa48("573") ? error && error.message && "" : stryMutAct_9fa48("572") ? false : stryMutAct_9fa48("571") ? true : (stryCov_9fa48("571", "572", "573"), (stryMutAct_9fa48("575") ? error || error.message : stryMutAct_9fa48("574") ? false : (stryCov_9fa48("574", "575"), error && error.message)) || (stryMutAct_9fa48("576") ? "Stryker was here!" : (stryCov_9fa48("576"), "")))} {...field} placeholder={infoLabels.labels.street} />)} />)}
          </div>

          <div className="legal-entity-field-title">{infoLabels.labels.city}</div>
          <div className="legal-entity-field">
            {(stryMutAct_9fa48("579") ? !isEdit && asMailingAddress : stryMutAct_9fa48("578") ? false : stryMutAct_9fa48("577") ? true : (stryCov_9fa48("577", "578", "579"), (stryMutAct_9fa48("580") ? isEdit : (stryCov_9fa48("580"), !isEdit)) || asMailingAddress)) ? legalEntityValue.city : stryMutAct_9fa48("583") ? !asMailingAddress || <Controller control={control} name="city" rules={rules.city} render={({
            field,
            fieldState: {
              error
            }
          }) => <Input id="legal-entity-city" errorMessage={error && error.message || ""} {...field} placeholder={infoLabels.labels.city} ariaLabel={infoLabels.labels.city} />} /> : stryMutAct_9fa48("582") ? false : stryMutAct_9fa48("581") ? true : (stryCov_9fa48("581", "582", "583"), (stryMutAct_9fa48("584") ? asMailingAddress : (stryCov_9fa48("584"), !asMailingAddress)) && <Controller control={control} name="city" rules={rules.city} render={stryMutAct_9fa48("585") ? () => undefined : (stryCov_9fa48("585"), ({
            field,
            fieldState: {
              error
            }
          }) => <Input id="legal-entity-city" errorMessage={stryMutAct_9fa48("588") ? error && error.message && "" : stryMutAct_9fa48("587") ? false : stryMutAct_9fa48("586") ? true : (stryCov_9fa48("586", "587", "588"), (stryMutAct_9fa48("590") ? error || error.message : stryMutAct_9fa48("589") ? false : (stryCov_9fa48("589", "590"), error && error.message)) || (stryMutAct_9fa48("591") ? "Stryker was here!" : (stryCov_9fa48("591"), "")))} {...field} placeholder={infoLabels.labels.city} ariaLabel={infoLabels.labels.city} />)} />)}
          </div>

          <div className="legal-entity-field-title">{infoLabels.labels.country}</div>
          <div className="legal-entity-field">
            {(stryMutAct_9fa48("594") ? !isEdit && asMailingAddress : stryMutAct_9fa48("593") ? false : stryMutAct_9fa48("592") ? true : (stryCov_9fa48("592", "593", "594"), (stryMutAct_9fa48("595") ? isEdit : (stryCov_9fa48("595"), !isEdit)) || asMailingAddress)) ? stryMutAct_9fa48("596") ? legalEntityValue.country.label : (stryCov_9fa48("596"), legalEntityValue.country?.label) : stryMutAct_9fa48("599") ? !asMailingAddress || <Controller control={control} name="country" rules={rules.country} render={({
            field,
            fieldState: {
              error
            }
          }) => <Select id="legal-entity-country" selectedOption={legalEntityValue.country} errorMessage={error && error.message} options={countries} onChange={item => {
            field.onChange(item);
          }} />} /> : stryMutAct_9fa48("598") ? false : stryMutAct_9fa48("597") ? true : (stryCov_9fa48("597", "598", "599"), (stryMutAct_9fa48("600") ? asMailingAddress : (stryCov_9fa48("600"), !asMailingAddress)) && <Controller control={control} name="country" rules={rules.country} render={stryMutAct_9fa48("601") ? () => undefined : (stryCov_9fa48("601"), ({
            field,
            fieldState: {
              error
            }
          }) => <Select id="legal-entity-country" selectedOption={legalEntityValue.country} errorMessage={stryMutAct_9fa48("604") ? error || error.message : stryMutAct_9fa48("603") ? false : stryMutAct_9fa48("602") ? true : (stryCov_9fa48("602", "603", "604"), error && error.message)} options={countries} onChange={item => {
            if (stryMutAct_9fa48("605")) {
              {}
            } else {
              stryCov_9fa48("605");
              field.onChange(item);
            }
          }} />)} />)}
          </div>

          <div className="legal-entity-field-title">{infoLabels.labels.state}</div>
          <div className="legal-entity-field">
            {(stryMutAct_9fa48("608") ? !isEdit && asMailingAddress : stryMutAct_9fa48("607") ? false : stryMutAct_9fa48("606") ? true : (stryCov_9fa48("606", "607", "608"), (stryMutAct_9fa48("609") ? isEdit : (stryCov_9fa48("609"), !isEdit)) || asMailingAddress)) ? legalEntityValue.state : stryMutAct_9fa48("612") ? !asMailingAddress || <Controller control={control} name="state" rules={rules.state} render={({
            field,
            fieldState: {
              error
            }
          }) => <Input id="legal-entity-state" errorMessage={error && error.message || ""} {...field} placeholder={infoLabels.labels.state} />} /> : stryMutAct_9fa48("611") ? false : stryMutAct_9fa48("610") ? true : (stryCov_9fa48("610", "611", "612"), (stryMutAct_9fa48("613") ? asMailingAddress : (stryCov_9fa48("613"), !asMailingAddress)) && <Controller control={control} name="state" rules={rules.state} render={stryMutAct_9fa48("614") ? () => undefined : (stryCov_9fa48("614"), ({
            field,
            fieldState: {
              error
            }
          }) => <Input id="legal-entity-state" errorMessage={stryMutAct_9fa48("617") ? error && error.message && "" : stryMutAct_9fa48("616") ? false : stryMutAct_9fa48("615") ? true : (stryCov_9fa48("615", "616", "617"), (stryMutAct_9fa48("619") ? error || error.message : stryMutAct_9fa48("618") ? false : (stryCov_9fa48("618", "619"), error && error.message)) || (stryMutAct_9fa48("620") ? "Stryker was here!" : (stryCov_9fa48("620"), "")))} {...field} placeholder={infoLabels.labels.state} />)} />)}
          </div>

          <div className="legal-entity-field-title">{infoLabels.labels.zipCode}</div>
          <div className="legal-entity-field">
            {(stryMutAct_9fa48("623") ? !isEdit && asMailingAddress : stryMutAct_9fa48("622") ? false : stryMutAct_9fa48("621") ? true : (stryCov_9fa48("621", "622", "623"), (stryMutAct_9fa48("624") ? isEdit : (stryCov_9fa48("624"), !isEdit)) || asMailingAddress)) ? legalEntityValue.zipCode : stryMutAct_9fa48("627") ? !asMailingAddress || <Controller control={control} name="zipCode" rules={rules.zipCode} render={({
            field,
            fieldState: {
              error
            }
          }) => <Input id="legal-entity-zipcode" errorMessage={error && error.message || ""} {...field} placeholder={infoLabels.labels.zipCode} />} /> : stryMutAct_9fa48("626") ? false : stryMutAct_9fa48("625") ? true : (stryCov_9fa48("625", "626", "627"), (stryMutAct_9fa48("628") ? asMailingAddress : (stryCov_9fa48("628"), !asMailingAddress)) && <Controller control={control} name="zipCode" rules={rules.zipCode} render={stryMutAct_9fa48("629") ? () => undefined : (stryCov_9fa48("629"), ({
            field,
            fieldState: {
              error
            }
          }) => <Input id="legal-entity-zipcode" errorMessage={stryMutAct_9fa48("632") ? error && error.message && "" : stryMutAct_9fa48("631") ? false : stryMutAct_9fa48("630") ? true : (stryCov_9fa48("630", "631", "632"), (stryMutAct_9fa48("634") ? error || error.message : stryMutAct_9fa48("633") ? false : (stryCov_9fa48("633", "634"), error && error.message)) || (stryMutAct_9fa48("635") ? "Stryker was here!" : (stryCov_9fa48("635"), "")))} {...field} placeholder={infoLabels.labels.zipCode} />)} />)}
          </div>

          {stryMutAct_9fa48("638") ? isEdit || <>
              <div className="legal-entity-field-title">{""}</div>
              <div className="legal-entity-field">
                <Checkbox options={[{
              id: "asMailingAddress",
              label: infoLabels.labels.legalAddressAsMailingAddress,
              isChecked: asMailingAddress,
              onChange: updateAsMailingAddress,
              dark: true
            }]} />
              </div>
            </> : stryMutAct_9fa48("637") ? false : stryMutAct_9fa48("636") ? true : (stryCov_9fa48("636", "637", "638"), isEdit && <>
              <div className="legal-entity-field-title">{stryMutAct_9fa48("639") ? "Stryker was here!" : (stryCov_9fa48("639"), "")}</div>
              <div className="legal-entity-field">
                <Checkbox options={stryMutAct_9fa48("640") ? [] : (stryCov_9fa48("640"), [stryMutAct_9fa48("641") ? {} : (stryCov_9fa48("641"), {
              id: stryMutAct_9fa48("642") ? "" : (stryCov_9fa48("642"), "asMailingAddress"),
              label: infoLabels.labels.legalAddressAsMailingAddress,
              isChecked: asMailingAddress,
              onChange: updateAsMailingAddress,
              dark: stryMutAct_9fa48("643") ? false : (stryCov_9fa48("643"), true)
            })])} />
              </div>
            </>)}
        </div>
      </>);
  }
};
export default memo(LegalEntityForm);