// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import React from "react";
import { DEFAULT_LOCALE, DOMAIN_ERROR, ERROR, HAS_EXCEPTION, SUCCESS, VALIDATION_ERROR } from "./constants";
import * as Sentry from "@sentry/nextjs";
import getConfig from "next/config";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { Dispatch, FunctionArgumentType, ValidationError } from "./types";
import { IncomingHttpHeaders } from "http";

//------------------------------------------------
// debounce to resolve duplicate calls on resize
//------------------------------------------------
type DebounceFunction = (...args: FunctionArgumentType[]) => void;
export function debounce<T extends DebounceFunction>(func: T, wait: number, immediate?: boolean): DebounceFunction {
  if (stryMutAct_9fa48("1118")) {
    {}
  } else {
    stryCov_9fa48("1118");
    let timeout: NodeJS.Timeout | null = null;
    return function (this: unknown, ...args: FunctionArgumentType[]): void {
      if (stryMutAct_9fa48("1119")) {
        {}
      } else {
        stryCov_9fa48("1119");
        const later = function (this: unknown) {
          if (stryMutAct_9fa48("1120")) {
            {}
          } else {
            stryCov_9fa48("1120");
            timeout = null;
            if (stryMutAct_9fa48("1123") ? false : stryMutAct_9fa48("1122") ? true : stryMutAct_9fa48("1121") ? immediate : (stryCov_9fa48("1121", "1122", "1123"), !immediate)) func.apply(this, args); // Use `this` directly
          }
        };
        const callNow = stryMutAct_9fa48("1126") ? immediate || !timeout : stryMutAct_9fa48("1125") ? false : stryMutAct_9fa48("1124") ? true : (stryCov_9fa48("1124", "1125", "1126"), immediate && (stryMutAct_9fa48("1127") ? timeout : (stryCov_9fa48("1127"), !timeout)));
        clearTimeout(timeout as NodeJS.Timeout);
        timeout = setTimeout(later, wait);
        if (stryMutAct_9fa48("1129") ? false : stryMutAct_9fa48("1128") ? true : (stryCov_9fa48("1128", "1129"), callNow)) func.apply(this, args); // Use `this` directly
      }
    };
  }
}

//------------------------------------------------
// Set cookie when local is updated
//------------------------------------------------
export function setCookie(locale: string): void {
  if (stryMutAct_9fa48("1130")) {
    {}
  } else {
    stryCov_9fa48("1130");
    document.cookie = stryMutAct_9fa48("1131") ? `` : (stryCov_9fa48("1131"), `NEXT_LOCALE=${locale}; expires=Fri, 31 Dec 9999 23:59:59 GMT; Path=/`);
  }
}
export function isObj(obj: object): boolean {
  if (stryMutAct_9fa48("1132")) {
    {}
  } else {
    stryCov_9fa48("1132");
    return stryMutAct_9fa48("1135") ? Object.prototype.toString.call(obj) !== "[object Object]" : stryMutAct_9fa48("1134") ? false : stryMutAct_9fa48("1133") ? true : (stryCov_9fa48("1133", "1134", "1135"), Object.prototype.toString.call(obj) === (stryMutAct_9fa48("1136") ? "" : (stryCov_9fa48("1136"), "[object Object]")));
  }
}
export function isString(obj: object): boolean {
  if (stryMutAct_9fa48("1137")) {
    {}
  } else {
    stryCov_9fa48("1137");
    return stryMutAct_9fa48("1140") ? Object.prototype.toString.call(obj) !== "[object String]" : stryMutAct_9fa48("1139") ? false : stryMutAct_9fa48("1138") ? true : (stryCov_9fa48("1138", "1139", "1140"), Object.prototype.toString.call(obj) === (stryMutAct_9fa48("1141") ? "" : (stryCov_9fa48("1141"), "[object String]")));
  }
}
export function isBrowser(): boolean {
  if (stryMutAct_9fa48("1142")) {
    {}
  } else {
    stryCov_9fa48("1142");
    return stryMutAct_9fa48("1145") ? typeof window === "undefined" : stryMutAct_9fa48("1144") ? false : stryMutAct_9fa48("1143") ? true : (stryCov_9fa48("1143", "1144", "1145"), typeof window !== (stryMutAct_9fa48("1146") ? "" : (stryCov_9fa48("1146"), "undefined")));
  }
}

//------------------------------------------------
// Extract Locale from accept-language header
//------------------------------------------------

export function getLocale(headers: IncomingHttpHeaders, SUPPORTED_LOCALES: string[]): string {
  if (stryMutAct_9fa48("1147")) {
    {}
  } else {
    stryCov_9fa48("1147");
    // If no browser accept-language then fallback to DEFAULT_LOCALE
    const browserLocale = (stryMutAct_9fa48("1150") ? headers && headers["accept-language"] && headers["accept-language"]?.match(/\w{2}\W\w{2}/) && [DEFAULT_LOCALE] : stryMutAct_9fa48("1149") ? false : stryMutAct_9fa48("1148") ? true : (stryCov_9fa48("1148", "1149", "1150"), (stryMutAct_9fa48("1152") ? headers && headers["accept-language"] || headers["accept-language"]?.match(/\w{2}\W\w{2}/) : stryMutAct_9fa48("1151") ? false : (stryCov_9fa48("1151", "1152"), (stryMutAct_9fa48("1154") ? headers || headers["accept-language"] : stryMutAct_9fa48("1153") ? true : (stryCov_9fa48("1153", "1154"), headers && headers[stryMutAct_9fa48("1155") ? "" : (stryCov_9fa48("1155"), "accept-language")])) && (stryMutAct_9fa48("1156") ? headers["accept-language"].match(/\w{2}\W\w{2}/) : (stryCov_9fa48("1156"), headers[stryMutAct_9fa48("1157") ? "" : (stryCov_9fa48("1157"), "accept-language")]?.match(stryMutAct_9fa48("1162") ? /\w{2}\W\W{2}/ : stryMutAct_9fa48("1161") ? /\w{2}\W\w/ : stryMutAct_9fa48("1160") ? /\w{2}\w\w{2}/ : stryMutAct_9fa48("1159") ? /\W{2}\W\w{2}/ : stryMutAct_9fa48("1158") ? /\w\W\w{2}/ : (stryCov_9fa48("1158", "1159", "1160", "1161", "1162"), /\w{2}\W\w{2}/)))))) || (stryMutAct_9fa48("1163") ? [] : (stryCov_9fa48("1163"), [DEFAULT_LOCALE]))))[0];
    return SUPPORTED_LOCALES.includes(stryMutAct_9fa48("1164") ? browserLocale.toUpperCase() : (stryCov_9fa48("1164"), browserLocale.toLowerCase())) ? stryMutAct_9fa48("1165") ? browserLocale.toUpperCase() : (stryCov_9fa48("1165"), browserLocale.toLowerCase()) : DEFAULT_LOCALE;
  }
}
export function toastContent(validationErrors: ValidationError[]): JSX.Element {
  if (stryMutAct_9fa48("1166")) {
    {}
  } else {
    stryCov_9fa48("1166");
    return <ul className="formatted-content">
      {validationErrors.map(stryMutAct_9fa48("1167") ? () => undefined : (stryCov_9fa48("1167"), (error, index) => (stryMutAct_9fa48("1168") ? Array.isArray(error.errorMessages) : (stryCov_9fa48("1168"), !Array.isArray(error.errorMessages))) ? <li className="text-gray-90" key={index}>{stryMutAct_9fa48("1169") ? `` : (stryCov_9fa48("1169"), `${error.propertyName} ${error.errorMessages}`)}</li> : <li className="text-gray-90" key={index}>
            {(stryMutAct_9fa48("1172") ? error.errorMessages.length !== 1 : stryMutAct_9fa48("1171") ? false : stryMutAct_9fa48("1170") ? true : (stryCov_9fa48("1170", "1171", "1172"), error.errorMessages.length === 1)) ? stryMutAct_9fa48("1173") ? `` : (stryCov_9fa48("1173"), `${error.propertyName} ${error.errorMessages[0]}`) : <>
                {error.propertyName}
                <ul key={(stryMutAct_9fa48("1174") ? "" : (stryCov_9fa48("1174"), "messages-")) + index}>
                  {error.errorMessages.map(stryMutAct_9fa48("1175") ? () => undefined : (stryCov_9fa48("1175"), (message, messageIndex) => <li key={(stryMutAct_9fa48("1176") ? "" : (stryCov_9fa48("1176"), "message-")) + messageIndex}>{message}</li>))}
                </ul>
              </>}
          </li>))}
    </ul>;
  }
}
export const onToastClose = (toast: string, dispatch: (action: {
  type: string;
  data: boolean;
}) => void): void => {
  if (stryMutAct_9fa48("1177")) {
    {}
  } else {
    stryCov_9fa48("1177");
    switch (toast) {
      case ERROR:
        if (stryMutAct_9fa48("1178")) {} else {
          stryCov_9fa48("1178");
          dispatch(stryMutAct_9fa48("1179") ? {} : (stryCov_9fa48("1179"), {
            type: ERROR,
            data: stryMutAct_9fa48("1180") ? true : (stryCov_9fa48("1180"), false)
          }));
          break;
        }
      case SUCCESS:
        if (stryMutAct_9fa48("1181")) {} else {
          stryCov_9fa48("1181");
          dispatch(stryMutAct_9fa48("1182") ? {} : (stryCov_9fa48("1182"), {
            type: SUCCESS,
            data: stryMutAct_9fa48("1183") ? true : (stryCov_9fa48("1183"), false)
          }));
          break;
        }
      case VALIDATION_ERROR:
        if (stryMutAct_9fa48("1184")) {} else {
          stryCov_9fa48("1184");
          dispatch(stryMutAct_9fa48("1185") ? {} : (stryCov_9fa48("1185"), {
            type: VALIDATION_ERROR,
            data: stryMutAct_9fa48("1186") ? true : (stryCov_9fa48("1186"), false)
          }));
          break;
        }
      case DOMAIN_ERROR:
        if (stryMutAct_9fa48("1187")) {} else {
          stryCov_9fa48("1187");
          dispatch(stryMutAct_9fa48("1188") ? {} : (stryCov_9fa48("1188"), {
            type: DOMAIN_ERROR,
            data: stryMutAct_9fa48("1189") ? true : (stryCov_9fa48("1189"), false)
          }));
          break;
        }
      default:
        if (stryMutAct_9fa48("1190")) {} else {
          stryCov_9fa48("1190");
          break;
        }
    }
  }
};
interface ErrorResponse {
  response?: {
    data?: {
      code?: string;
      message?: string;
      detail?: string;
      errors?: Record<string, string>;
    };
    status?: number;
  };
  code?: string;
}
type ErrorMap = Map<string, string>;
export const getExtractedErrorMessage = (errorMap: ErrorMap, error: ErrorResponse | boolean, fallback: string): string => {
  if (stryMutAct_9fa48("1191")) {
    {}
  } else {
    stryCov_9fa48("1191");
    return stryMutAct_9fa48("1194") ? (errorMap.get(((error as ErrorResponse)?.response?.data?.code || (error as ErrorResponse)?.code) ?? "") || (error as ErrorResponse)?.response?.data?.message || (error as ErrorResponse)?.response?.data?.detail) && fallback : stryMutAct_9fa48("1193") ? false : stryMutAct_9fa48("1192") ? true : (stryCov_9fa48("1192", "1193", "1194"), (stryMutAct_9fa48("1196") ? (errorMap.get(((error as ErrorResponse)?.response?.data?.code || (error as ErrorResponse)?.code) ?? "") || (error as ErrorResponse)?.response?.data?.message) && (error as ErrorResponse)?.response?.data?.detail : stryMutAct_9fa48("1195") ? false : (stryCov_9fa48("1195", "1196"), (stryMutAct_9fa48("1198") ? errorMap.get(((error as ErrorResponse)?.response?.data?.code || (error as ErrorResponse)?.code) ?? "") && (error as ErrorResponse)?.response?.data?.message : stryMutAct_9fa48("1197") ? false : (stryCov_9fa48("1197", "1198"), errorMap.get(stryMutAct_9fa48("1199") ? ((error as ErrorResponse)?.response?.data?.code || (error as ErrorResponse)?.code) && "" : (stryCov_9fa48("1199"), (stryMutAct_9fa48("1202") ? (error as ErrorResponse)?.response?.data?.code && (error as ErrorResponse)?.code : stryMutAct_9fa48("1201") ? false : stryMutAct_9fa48("1200") ? true : (stryCov_9fa48("1200", "1201", "1202"), (stryMutAct_9fa48("1205") ? (error as ErrorResponse).response?.data?.code : stryMutAct_9fa48("1204") ? (error as ErrorResponse)?.response.data?.code : stryMutAct_9fa48("1203") ? (error as ErrorResponse)?.response?.data.code : (stryCov_9fa48("1203", "1204", "1205"), (error as ErrorResponse)?.response?.data?.code)) || (stryMutAct_9fa48("1206") ? (error as ErrorResponse).code : (stryCov_9fa48("1206"), (error as ErrorResponse)?.code)))) ?? (stryMutAct_9fa48("1207") ? "Stryker was here!" : (stryCov_9fa48("1207"), "")))) || (stryMutAct_9fa48("1210") ? (error as ErrorResponse).response?.data?.message : stryMutAct_9fa48("1209") ? (error as ErrorResponse)?.response.data?.message : stryMutAct_9fa48("1208") ? (error as ErrorResponse)?.response?.data.message : (stryCov_9fa48("1208", "1209", "1210"), (error as ErrorResponse)?.response?.data?.message)))) || (stryMutAct_9fa48("1213") ? (error as ErrorResponse).response?.data?.detail : stryMutAct_9fa48("1212") ? (error as ErrorResponse)?.response.data?.detail : stryMutAct_9fa48("1211") ? (error as ErrorResponse)?.response?.data.detail : (stryCov_9fa48("1211", "1212", "1213"), (error as ErrorResponse)?.response?.data?.detail)))) || fallback);
  }
};
export function validationMessage(validationErrors: Record<string, string>): ValidationError[] {
  if (stryMutAct_9fa48("1214")) {
    {}
  } else {
    stryCov_9fa48("1214");
    return Object.keys(validationErrors).reduce((formattedErrors: ValidationError[], key: string) => {
      if (stryMutAct_9fa48("1215")) {
        {}
      } else {
        stryCov_9fa48("1215");
        const propertyName = stryMutAct_9fa48("1216") ? `` : (stryCov_9fa48("1216"), `${stryMutAct_9fa48("1217") ? key.split(/\.|(?=[A-Z])/).join(" ").toUpperCase() : (stryCov_9fa48("1217"), key.split(stryMutAct_9fa48("1219") ? /\.|(?=[^A-Z])/ : stryMutAct_9fa48("1218") ? /\.|(?![A-Z])/ : (stryCov_9fa48("1218", "1219"), /\.|(?=[A-Z])/)).join(stryMutAct_9fa48("1220") ? "" : (stryCov_9fa48("1220"), " ")).toLowerCase())}`);
        const errorMessages = validationErrors[key];
        formattedErrors.push(stryMutAct_9fa48("1221") ? {} : (stryCov_9fa48("1221"), {
          propertyName,
          errorMessages
        }));
        return formattedErrors;
      }
    }, stryMutAct_9fa48("1222") ? ["Stryker was here"] : (stryCov_9fa48("1222"), []));
  }
}
export function errorHandling(dispatch: Dispatch, e: ErrorResponse): void {
  if (stryMutAct_9fa48("1223")) {
    {}
  } else {
    stryCov_9fa48("1223");
    try {
      if (stryMutAct_9fa48("1224")) {
        {}
      } else {
        stryCov_9fa48("1224");
        const {
          publicRuntimeConfig: props = {}
        } = getConfig();
        const SUPPORTED_LOCALES = JSON.parse(props.SUPPORTED_LOCALES);
        Sentry.captureException(e);
        switch (stryMutAct_9fa48("1226") ? e.response?.status : stryMutAct_9fa48("1225") ? e?.response.status : (stryCov_9fa48("1225", "1226"), e?.response?.status)) {
          case 401:
            if (stryMutAct_9fa48("1227")) {} else {
              stryCov_9fa48("1227");
              const redirectUrl = (stryMutAct_9fa48("1230") ? SUPPORTED_LOCALES.indexOf(window.location.pathname.split("/")[1]) !== -1 : stryMutAct_9fa48("1229") ? false : stryMutAct_9fa48("1228") ? true : (stryCov_9fa48("1228", "1229", "1230"), SUPPORTED_LOCALES.indexOf(window.location.pathname.split(stryMutAct_9fa48("1231") ? "" : (stryCov_9fa48("1231"), "/"))[1]) === (stryMutAct_9fa48("1232") ? +1 : (stryCov_9fa48("1232"), -1)))) ? stryMutAct_9fa48("1233") ? "" : (stryCov_9fa48("1233"), "/") : stryMutAct_9fa48("1234") ? `` : (stryCov_9fa48("1234"), `/${window.location.pathname.split(stryMutAct_9fa48("1235") ? "" : (stryCov_9fa48("1235"), "/"))[1]}`);
              window.location.href = redirectUrl;
              break;
            }
          case 404:
            if (stryMutAct_9fa48("1236")) {} else {
              stryCov_9fa48("1236");
              dispatch(stryMutAct_9fa48("1237") ? {} : (stryCov_9fa48("1237"), {
                type: HAS_EXCEPTION,
                data: e.response.status
              }));
              break;
            }
          case 500:
            if (stryMutAct_9fa48("1238")) {} else {
              stryCov_9fa48("1238");
              dispatch(stryMutAct_9fa48("1239") ? {} : (stryCov_9fa48("1239"), {
                type: HAS_EXCEPTION,
                data: e.response.status
              }));
              break;
            }
          case 422:
            if (stryMutAct_9fa48("1240")) {} else {
              stryCov_9fa48("1240");
              dispatch(stryMutAct_9fa48("1241") ? {} : (stryCov_9fa48("1241"), {
                type: VALIDATION_ERROR,
                data: validationMessage(stryMutAct_9fa48("1242") ? e.response?.data?.errors && {} : (stryCov_9fa48("1242"), (stryMutAct_9fa48("1244") ? e.response.data?.errors : stryMutAct_9fa48("1243") ? e.response?.data.errors : (stryCov_9fa48("1243", "1244"), e.response?.data?.errors)) ?? {}))
              }));
              break;
            }
          case 409:
            if (stryMutAct_9fa48("1245")) {} else {
              stryCov_9fa48("1245");
              dispatch(stryMutAct_9fa48("1246") ? {} : (stryCov_9fa48("1246"), {
                type: DOMAIN_ERROR,
                data: e
              }));
              break;
            }
          default:
            if (stryMutAct_9fa48("1247")) {} else {
              stryCov_9fa48("1247");
              if (stryMutAct_9fa48("1250") ? false : stryMutAct_9fa48("1249") ? true : stryMutAct_9fa48("1248") ? e?.response?.data?.message : (stryCov_9fa48("1248", "1249", "1250"), !(stryMutAct_9fa48("1253") ? e.response?.data?.message : stryMutAct_9fa48("1252") ? e?.response.data?.message : stryMutAct_9fa48("1251") ? e?.response?.data.message : (stryCov_9fa48("1251", "1252", "1253"), e?.response?.data?.message)))) console.log(e);
              dispatch(stryMutAct_9fa48("1254") ? {} : (stryCov_9fa48("1254"), {
                type: HAS_EXCEPTION,
                data: stryMutAct_9fa48("1255") ? e?.response?.status && [] : (stryCov_9fa48("1255"), (stryMutAct_9fa48("1257") ? e.response?.status : stryMutAct_9fa48("1256") ? e?.response.status : (stryCov_9fa48("1256", "1257"), e?.response?.status)) ?? (stryMutAct_9fa48("1258") ? ["Stryker was here"] : (stryCov_9fa48("1258"), [])))
              }));
              break;
            }
        }
      }
    } catch (error) {
      if (stryMutAct_9fa48("1259")) {
        {}
      } else {
        stryCov_9fa48("1259");
        // TODO: in local environment it should console.error, anywhere else should send error to server for logging
        console.error(stryMutAct_9fa48("1260") ? {} : (stryCov_9fa48("1260"), {
          originalError: e,
          errorHandlingError: error
        }));
      }
    }
  }
}

//---------------------------------
// Age must be 18 years or older
//---------------------------------
export function isAdult(birthDate: string): boolean {
  if (stryMutAct_9fa48("1261")) {
    {}
  } else {
    stryCov_9fa48("1261");
    const dateOfBirth = LocalizedDate.fromFormattedDate(birthDate);
    return dateOfBirth.isAfter(LocalizedDate.subtractFromNow(18, stryMutAct_9fa48("1262") ? "" : (stryCov_9fa48("1262"), "years")));
  }
}