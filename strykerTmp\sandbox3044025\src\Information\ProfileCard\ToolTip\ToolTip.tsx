// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import React from "react";
import Tooltip from "rc-tooltip";
import { memo, ReactNode } from "react";
import { useDetectScreen } from "../../../utils";
import classNames from "classnames";
type ToolTip = {
  children: ReactNode;
  id?: string;
  directionForDesktop?: string;
  directionForMobile?: string;
  overlay: string | ReactNode;
  classes?: string;
};
export default memo(function ToolTip({
  children,
  id = stryMutAct_9fa48("944") ? "" : (stryCov_9fa48("944"), "tooltip-element"),
  directionForDesktop = stryMutAct_9fa48("945") ? "" : (stryCov_9fa48("945"), "top"),
  directionForMobile = stryMutAct_9fa48("946") ? "" : (stryCov_9fa48("946"), "bottom"),
  overlay,
  classes
}: ToolTip) {
  if (stryMutAct_9fa48("947")) {
    {}
  } else {
    stryCov_9fa48("947");
    const isMobile = useDetectScreen(767);
    return <Tooltip overlayClassName={classNames(stryMutAct_9fa48("948") ? "" : (stryCov_9fa48("948"), "tooltip-overlay-content"), classes)} placement={(stryMutAct_9fa48("949") ? isMobile : (stryCov_9fa48("949"), !isMobile)) ? directionForDesktop : directionForMobile} trigger={stryMutAct_9fa48("950") ? [] : (stryCov_9fa48("950"), [stryMutAct_9fa48("951") ? "" : (stryCov_9fa48("951"), "hover")])} overlay={<div>{overlay}</div>}>
      <div data-testid={id} className="tooltip-element">
        {children}
      </div>
    </Tooltip>;
  }
});