// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import React, { memo } from "react";
import ToolTip from "../ToolTip/ToolTip";
import { AuthenticatedUser } from "@src/utils/types";
export type CreatorDisplayNameProps = {
  user: AuthenticatedUser;
  labels?: {
    title: string;
  };
  tooltip: string;
};
export default memo(function CreatorDisplayName({
  user,
  labels,
  tooltip
}: CreatorDisplayNameProps) {
  if (stryMutAct_9fa48("926")) {
    {}
  } else {
    stryCov_9fa48("926");
    return <section className="creator-display-name-container">
      <div className={(stryMutAct_9fa48("927") ? labels : (stryCov_9fa48("927"), !labels)) ? stryMutAct_9fa48("928") ? "" : (stryCov_9fa48("928"), "creator-display-name-division") : stryMutAct_9fa48("929") ? "Stryker was here!" : (stryCov_9fa48("929"), "")}>
        {stryMutAct_9fa48("932") ? labels || <span className="creator-display-name-welcome"> {labels.title}</span> : stryMutAct_9fa48("931") ? false : stryMutAct_9fa48("930") ? true : (stryCov_9fa48("930", "931", "932"), labels && <span className="creator-display-name-welcome"> {labels.title}</span>)}
        <h3 className={labels ? stryMutAct_9fa48("933") ? "" : (stryCov_9fa48("933"), "creator-display-name-username creator-display-name-dashboard") : stryMutAct_9fa48("934") ? "" : (stryCov_9fa48("934"), "creator-display-name-username")}>
          {user.username}
        </h3>
      </div>
      {stryMutAct_9fa48("937") ? user.needsMigration || <div className="creator-display-name-badge-container">
          <ToolTip overlay={tooltip}>
            <img alt="GCN Badge" src={`/img/icons/badge.svg`} className="creator-display-badge" />
          </ToolTip>
        </div> : stryMutAct_9fa48("936") ? false : stryMutAct_9fa48("935") ? true : (stryCov_9fa48("935", "936", "937"), user.needsMigration && <div className="creator-display-name-badge-container">
          <ToolTip overlay={tooltip}>
            <img alt="GCN Badge" src={stryMutAct_9fa48("938") ? `` : (stryCov_9fa48("938"), `/img/icons/badge.svg`)} className="creator-display-badge" />
          </ToolTip>
        </div>)}
    </section>;
  }
});