import React, { memo } from "react";
import ToolTip from "../ToolTip/ToolTip";
import { AuthenticatedUser } from "@src/utils/types";

export type CreatorDisplayNameProps = {
  user: AuthenticatedUser;
  labels?: {
    profilePictureLabels: {
      title: string;
    };
  };
  tooltip: string;
};

export default memo(function CreatorDisplayName({ user, labels, tooltip }: CreatorDisplayNameProps) {
  return (
    <section className="creator-display-name-container">
      <div className={!labels ? "creator-display-name-division" : ""}>
        {labels && <span className="creator-display-name-welcome"> {labels.profilePictureLabels.title}</span>}
        <h3
          className={
            labels ? "creator-display-name-username creator-display-name-dashboard" : "creator-display-name-username"
          }
        >
          {user.username}
        </h3>
      </div>
      {user.needsMigration && (
        <div className="creator-display-name-badge-container">
          <ToolTip overlay={tooltip}>
            <img alt="GCN Badge" src={`/img/icons/badge.svg`} className="creator-display-badge" />
          </ToolTip>
        </div>
      )}
    </section>
  );
});
