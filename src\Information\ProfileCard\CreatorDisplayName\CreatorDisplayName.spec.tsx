import { act, render, screen } from "@testing-library/react";
import CreatorDisplayName from "./CreatorDisplayName";
import React from "react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import { AuthenticatedUser } from "@src/utils/types";

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

describe("CreatorDisplayName - Tests for creator display name functionality including rendering, username display, welcome message, GCN badge, tooltip, accessibility, and error handling", () => {
  const user: AuthenticatedUser = {
    analyticsId: "xNIEuLMZCvlw+7q2oZaBuG0cTdqHTa2JP3l2eCZ2Sjg=",
    avatar: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png",
    creatorCode: "TestAFFILIATE20250",
    isFlagged: false,
    isPayable: true,
    needsMigration: false,
    programs: ["creator_network", "affiliate", "sims_creator_program"],
    status: "ACTIVE",
    tier: "CREATORNETWORK",
    type: "CREATOR",
    username: "245902"
  };

  const labels = {
    title: "Welcome back,"
  };

  const tooltip = "GCN Badge tooltip text";

  const props = {
    user,
    labels,
    tooltip
  };

  it("should show creator display name correctly", () => {
    render(<CreatorDisplayName {...props} />);

    expect(screen.getByText("Welcome back,")).toBeInTheDocument();
    expect(screen.getByText("245902")).toBeInTheDocument();
  });

  it("should display username correctly", () => {
    render(<CreatorDisplayName {...props} />);

    const usernameElement = screen.getByText(user.username);
    expect(usernameElement).toBeInTheDocument();
    expect(usernameElement).toHaveClass("creator-display-name-username");
    expect(usernameElement).toHaveClass("creator-display-name-dashboard");
  });

  it("should show welcome message when labels are provided", () => {
    render(<CreatorDisplayName {...props} />);

    expect(screen.getByText("Welcome back,")).toBeInTheDocument();
    expect(screen.getByText("Welcome back,")).toHaveClass("creator-display-name-welcome");
  });

  it("should not show welcome message when labels are not provided", () => {
    const propsWithoutLabels = {
      user,
      tooltip
    };

    render(<CreatorDisplayName {...propsWithoutLabels} />);

    expect(screen.queryByText("Welcome back,")).not.toBeInTheDocument();
    expect(screen.getByText(user.username)).toBeInTheDocument();
  });

  it("should apply different CSS classes when labels are not provided", () => {
    const propsWithoutLabels = {
      user,
      tooltip
    };

    render(<CreatorDisplayName {...propsWithoutLabels} />);

    const usernameElement = screen.getByText(user.username);
    expect(usernameElement).toHaveClass("creator-display-name-username");
    expect(usernameElement).not.toHaveClass("creator-display-name-dashboard");
  });

  it("should show GCN badge when user needs migration", () => {
    const userWithMigration = {
      ...user,
      needsMigration: true
    };

    const propsWithMigration = {
      ...props,
      user: userWithMigration
    };

    render(<CreatorDisplayName {...propsWithMigration} />);

    const badge = screen.getByAltText("GCN Badge");
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveAttribute("src", "/img/icons/badge.svg");
    expect(badge).toHaveClass("creator-display-badge");
  });

  it("should not show GCN badge when user does not need migration", () => {
    render(<CreatorDisplayName {...props} />);

    expect(screen.queryByAltText("GCN Badge")).not.toBeInTheDocument();
  });

  it("should show tooltip on GCN badge hover", async () => {
    const userWithMigration = {
      ...user,
      needsMigration: true
    };

    const propsWithMigration = {
      ...props,
      user: userWithMigration
    };

    render(<CreatorDisplayName {...propsWithMigration} />);

    const badge = screen.getByAltText("GCN Badge");
    await userEvent.hover(badge);

    expect(screen.getByText(tooltip)).toBeInTheDocument();
  });

  it("should handle different usernames correctly", () => {
    const userWithDifferentUsername = {
      ...user,
      username: "TestCreator123"
    };

    const propsWithDifferentUser = {
      ...props,
      user: userWithDifferentUsername
    };

    render(<CreatorDisplayName {...propsWithDifferentUser} />);

    expect(screen.getByText("TestCreator123")).toBeInTheDocument();
    expect(screen.queryByText("245902")).not.toBeInTheDocument();
  });

  it("should handle different welcome messages correctly", () => {
    const differentLabels = {
      title: "Hello,"
    };

    const propsWithDifferentLabels = {
      ...props,
      labels: differentLabels
    };

    render(<CreatorDisplayName {...propsWithDifferentLabels} />);

    expect(screen.getByText("Hello,")).toBeInTheDocument();
    expect(screen.queryByText("Welcome back,")).not.toBeInTheDocument();
  });

  it("should handle different tooltip text correctly", () => {
    const userWithMigration = {
      ...user,
      needsMigration: true
    };

    const propsWithDifferentTooltip = {
      ...props,
      user: userWithMigration,
      tooltip: "Different tooltip text"
    };

    render(<CreatorDisplayName {...propsWithDifferentTooltip} />);

    const badge = screen.getByAltText("GCN Badge");
    expect(badge).toBeInTheDocument();
  });

  it("should be accessible", async () => {
    let results: any;
    const { container } = render(<CreatorDisplayName {...props} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  it("should be accessible with GCN badge", async () => {
    const userWithMigration = {
      ...user,
      needsMigration: true
    };

    const propsWithMigration = {
      ...props,
      user: userWithMigration
    };

    let results: any;
    const { container } = render(<CreatorDisplayName {...propsWithMigration} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  it("should handle missing props gracefully", () => {
    const minimalProps = {
      user,
      tooltip: ""
    };

    expect(() => {
      render(<CreatorDisplayName {...minimalProps} />);
    }).not.toThrow();
  });

  it("should be memoized and not re-render unnecessarily", () => {
    const { rerender } = render(<CreatorDisplayName {...props} />);

    rerender(<CreatorDisplayName {...props} />);

    expect(screen.getByText("Welcome back,")).toBeInTheDocument();
    expect(screen.getByText(user.username)).toBeInTheDocument();
  });
});