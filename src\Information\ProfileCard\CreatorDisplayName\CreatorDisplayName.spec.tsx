import { render, screen } from "@testing-library/react";
import CreatorDisplayName from "./CreatorDisplayName";
import React from "react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import { AuthenticatedUser } from "@src/utils/types";

Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn()
  }))
});

describe("CreatorDisplayName", () => {
  const user: AuthenticatedUser = {
    analyticsId: "xNIEuLMZCvlw+7q2oZaBuG0cTdqHTa2JP3l2eCZ2Sjg=",
    avatar: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png",
    creatorCode: "TestAFFILIATE20250",
    isFlagged: false,
    isPayable: true,
    needsMigration: false,
    programs: ["creator_network", "affiliate", "sims_creator_program"],
    status: "ACTIVE",
    tier: "CREATORNETWORK",
    type: "CREATOR",
    username: "245902"
  };

  const labels = {
    profilePictureLabels: {
      title: "Welcome back,"
    }
  };
  const tooltip = "GCN Badge tooltip text";
  const props = {
    user,
    labels,
    tooltip
  };

  it("shows creator display name correctly", () => {
    render(<CreatorDisplayName {...props} />);

    expect(screen.getByText("Welcome back,")).toBeInTheDocument();
    expect(screen.getByText("Welcome back,")).toHaveClass("creator-display-name-welcome");
    expect(screen.getByText("245902")).toBeInTheDocument();
    const usernameElement = screen.getByText(user.username);
    expect(usernameElement).toBeInTheDocument();
    expect(usernameElement).toHaveClass("creator-display-name-username");
    expect(usernameElement).toHaveClass("creator-display-name-dashboard");
  });

  it("should not show welcome message when labels are not provided", () => {
    const propsWithoutLabels = {
      user,
      tooltip
    };

    render(<CreatorDisplayName {...propsWithoutLabels} />);

    expect(screen.queryByText("Welcome back,")).not.toBeInTheDocument();
    const usernameElement = screen.getByText(user.username);
    expect(usernameElement).toBeInTheDocument();
    expect(usernameElement).toHaveClass("creator-display-name-username");
    expect(usernameElement).not.toHaveClass("creator-display-name-dashboard");
  });

  it("should show GCN badge when user needs migration", () => {
    const userWithMigration = {
      ...user,
      needsMigration: true
    };

    const propsWithMigration = {
      ...props,
      user: userWithMigration
    };

    render(<CreatorDisplayName {...propsWithMigration} />);

    const badge = screen.getByAltText("GCN Badge");
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveAttribute("src", "/img/icons/badge.svg");
    expect(badge).toHaveClass("creator-display-badge");
  });

  it("should not show GCN badge when user does not need migration", () => {
    render(<CreatorDisplayName {...props} />);

    expect(screen.queryByAltText("GCN Badge")).not.toBeInTheDocument();
  });

  it("should show tooltip on GCN badge hover", async () => {
    const userWithMigration = {
      ...user,
      needsMigration: true
    };
    const propsWithMigration = {
      ...props,
      user: userWithMigration
    };

    render(<CreatorDisplayName {...propsWithMigration} />);

    const badge = screen.getByAltText("GCN Badge");
    await userEvent.hover(badge);

    expect(screen.getByText(tooltip)).toBeInTheDocument();
  });

  it("should be accessible", async () => {
    const { container } = render(<CreatorDisplayName {...props} />);

    expect(await axe(container)).toHaveNoViolations();
  });

  it("should handle missing props gracefully", () => {
    const minimalProps = {
      user,
      tooltip: ""
    };

    expect(() => {
      render(<CreatorDisplayName {...minimalProps} />);
    }).not.toThrow();
  });
});
