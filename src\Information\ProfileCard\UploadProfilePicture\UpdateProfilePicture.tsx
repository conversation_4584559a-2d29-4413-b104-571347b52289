import React, { memo, useCallback, useMemo, useRef, useState } from "react";
import {
  Button,
  edit,
  Icon,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalV2
} from "@eait-playerexp-cn/core-ui-kit";
import { SESSION_USER } from "../../../utils";
import { CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import { AuthenticatedUser, Dispatch } from "../../../utils/types";
import { NextRouter } from "next/router";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { FormDataBody } from "@eait-playerexp-cn/http";
import { ButtonLabels, ProfileLabels, ProfilePictureLabels } from "@src/Information/types";

type Labels = {
  buttons: ButtonLabels;
  profilePictureLabels: ProfilePictureLabels;
  profileLabels: ProfileLabels;
};

type PicturePlaceholderProps = {
  showModal: () => void;
  placeholderRef: (node: HTMLButtonElement | null) => void;
  src: string;
  updateAvatarLabel: string;
};

type FooterButtonsProps = {
  buttons: ButtonLabels;
  onClose: () => void;
  onSave: (event: React.FormEvent) => Promise<void>;
  cancelButtonRef: React.RefObject<HTMLButtonElement>;
  disabled: boolean;
  isLoader: boolean;
};

type ModalBodyContentProps = {
  translate: {
    buttons: ButtonLabels;
    messages: ProfilePictureLabels;
  };
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  selector: string;
  previewSrc: string | null;
  size: number;
  invalidImage: string | null;
  router: NextRouter;
};

type UpdateProfilePictureProps = {
  selector?: string;
  size?: number;
  src: string;
  labels: Labels;
  isPlaceholderDefault?: boolean;
  user: AuthenticatedUser;
  stableDispatch: Dispatch;
  creatorsClient: TraceableHttpClient;
  DEFAULT_AVATAR_IMAGE: string;
  router: NextRouter;
};

type ErrorResponse = {
  response?: {
    status?: number;
    data?: {
      errors?: {
        avatar?: string;
      };
    };
  };
  message?: string;
};

type AvatarProps = {
  showModal: () => void;
  placeholderRef: (node: HTMLButtonElement | null) => void;
  src: string;
  updateAvatarLabel: string;
};

const Avatar = ({ showModal, placeholderRef, src, updateAvatarLabel }: AvatarProps) => {
  return (
    <>
      <div className="profile-card-logo">
        <img src={src} alt="" className="profile-card-avatar" />
      </div>
      <button
        type="button"
        className="profile-card-logo-edit"
        ref={placeholderRef}
        onClick={showModal}
        aria-label={updateAvatarLabel}
      >
        <Icon icon={edit} />
      </button>
    </>
  );
};

const PicturePlaceholder = ({ showModal, placeholderRef, src, updateAvatarLabel }: PicturePlaceholderProps) => {
  return (
    <section className="update-profile-picture-placeholder" ref={placeholderRef}>
      <Avatar {...{ showModal, placeholderRef, src, updateAvatarLabel }} />
    </section>
  );
};

const FooterButtons = memo(function FooterButtons({
  buttons: { save, cancel },
  onClose,
  onSave,
  cancelButtonRef,
  disabled,
  isLoader
}: FooterButtonsProps) {
  return (
    <>
      <Button variant="tertiary" dark size="sm" ref={cancelButtonRef} onClick={onClose}>
        {cancel}
      </Button>
      <Button disabled={disabled} spinner={isLoader} size="sm" type="submit" onClick={onSave}>
        {save}
      </Button>
    </>
  );
});

const ModalBodyContent = memo(function ModalBodyContent({
  translate,
  onChange,
  selector,
  previewSrc,
  size,
  invalidImage,
  router
}: ModalBodyContentProps) {
  return (
    <div className="update-profile-picture-container">
      <div className="update-profile-picture-upload-section">
        <div className="update-profile-picture-placeholder-title">{translate.messages.message}</div>
        <button className="btn btn-secondary btn-dark">
          <input type="file" id={selector} name={selector} hidden onChange={onChange} size={size} />
          <label htmlFor={selector} className="update-profile-picture-label">
            {translate.buttons.browse}
          </label>
        </button>
        {invalidImage && <div className="form-error-message">{invalidImage}</div>}
        <div className="update-profile-picture-placeholder-terms">
          {translate.messages.termsAndConditionsFirst}{" "}
          <a
            target="_blank"
            rel="noreferrer"
            href={`${
              router.locale === "en-us"
                ? "https://tos.ea.com/legalapp/WEBTERMS/US/en/PC/"
                : "https://tos.ea.com/legalapp/WEBTERMS/US/ja/PC/"
            }`}
            className="update-profile-picture-agreement-link"
          >
            {translate.messages.termsAndConditionsMiddle}
          </a>{" "}
          {translate.messages.termsAndConditionsLast}
        </div>
      </div>
      <div className="update-profile-picture-image-placeholder">
        <img
          src={`${previewSrc}` || `/img/migrations/default.png`}
          className="update-profile-picture-preview"
          alt="Avatar Preview"
        />
      </div>
    </div>
  );
});

const UpdateProfilePicture = ({
  selector = "upload",
  size = 1048576,
  src,
  labels,
  isPlaceholderDefault = true,
  user,
  stableDispatch,
  creatorsClient,
  DEFAULT_AVATAR_IMAGE,
  router
}: UpdateProfilePictureProps): JSX.Element => {
  const [isShown, setIsShown] = useState<boolean>(false);
  const [profilePicturePlaceholder, setProfilePicturePlaceholder] = useState<HTMLButtonElement | null>(null);
  const [previewSrc, setPreviewSrc] = useState<string | null>(null);
  const [invalidImage, setInvalidImage] = useState<string | null>(null);
  const [uploadedSrc, setUploadedSrc] = useState<string>(src);
  const [isFilePicked, setIsFilePicked] = useState<boolean>(false);
  const [selectedFile, setSelectedFile] = useState<File | undefined>();
  const { translate, updateAvatarLabel } = useMemo(() => {
    return {
      translate: { buttons: labels.buttons, messages: labels.profilePictureLabels },
      updateAvatarLabel: labels.profileLabels.updateAvatar
    };
  }, [labels]);

  // We'll use the actual service without custom typing since it has specific requirements
  const creatorService = useMemo(() => new CreatorsService(creatorsClient, DEFAULT_AVATAR_IMAGE), [creatorsClient]);

  const showModal = () => {
    setIsShown(true);
    setPreviewSrc(uploadedSrc);
  };
  const [isLoader, setIsLoader] = useState<boolean>(false);

  const closeModal = () => {
    setIsShown(false);
    setIsFilePicked(false);
    setPreviewSrc(null);
    setInvalidImage(null);
    profilePicturePlaceholder?.focus();
  };
  const changeHandler = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file === undefined) {
      setInvalidImage(translate.messages.avatarRequired);
      return false;
    }
    if (!(file?.type === "image/jpeg" || file?.type === "image/png" || file?.type === "image/gif")) {
      setInvalidImage(translate.messages.avatarInvalid);
      return false;
    }

    if (file?.size > size) {
      setInvalidImage(translate.messages.avatarMoreThanLimit);
      return false;
    }
    setInvalidImage(null);
    setSelectedFile(file);
    setPreviewSrc(URL.createObjectURL(file));
    setIsFilePicked(true);
  };
  const submitHandler = async (event: React.FormEvent) => {
    event.preventDefault();
    setIsLoader(true);
    if (previewSrc !== null) {
      const formData = new FormData();
      formData.append("avatar", selectedFile as Blob, selectedFile?.name as string);
      creatorService
        .updateAvatar(formData as unknown as FormDataBody)
        .then(() => {
          setUploadedSrc(previewSrc);
          setIsLoader(false);
          closeModal();
          // Update the user prop. So that the new avatar gets rendered across components
          const extension = selectedFile?.name.split(".").pop();
          user.avatar = user.avatar
            ? user.avatar.split(".").slice(0, -1).join(".").slice(0) + `.${extension}?t=${new Date().getTime()}`
            : null;
          stableDispatch({ type: SESSION_USER, data: user });
        })
        .catch((e: ErrorResponse) => {
          setIsLoader(false);
          if (e?.response?.status === 422) {
            setInvalidImage(e.response.data.errors.avatar);
          } else {
            setInvalidImage(e.message);
          }
        });
    }
  };

  const isInvalidFormatPicked = useCallback(() => isFilePicked && !!invalidImage, [isFilePicked, invalidImage]);
  const cancelButtonRef = useRef<HTMLButtonElement | null>(null);

  return (
    <>
      {isPlaceholderDefault && (
        <PicturePlaceholder
          {...{
            showModal,
            src: uploadedSrc,
            placeholderRef: (n) => setProfilePicturePlaceholder(n),
            updateAvatarLabel
          }}
        />
      )}
      {!isPlaceholderDefault && (
        <div className="profile-card-logo-container">
          <Avatar
            {...{
              showModal,
              src: uploadedSrc,
              placeholderRef: (n) => setProfilePicturePlaceholder(n),
              updateAvatarLabel
            }}
          />
        </div>
      )}
      {isShown && (
        <ModalV2 closeButtonRef={cancelButtonRef}>
          <ModalHeader>
            <ModalTitle>{translate.messages.title}</ModalTitle>
            <ModalCloseButton ariaLabel={translate.buttons.close} closeButtonRef={cancelButtonRef}></ModalCloseButton>
          </ModalHeader>
          <ModalBody>
            <ModalBodyContent
              {...{ selector, translate, onChange: changeHandler, previewSrc, size, invalidImage, router }}
            />
          </ModalBody>
          <ModalFooter showDivider>
            <FooterButtons
              {...{
                buttons: translate.buttons,
                onClose: closeModal,
                onSave: submitHandler,
                disabled: !isFilePicked || isLoader || isInvalidFormatPicked(),
                cancelButtonRef,
                isLoader
              }}
            />
          </ModalFooter>
        </ModalV2>
      )}
    </>
  );
};

export default UpdateProfilePicture;
