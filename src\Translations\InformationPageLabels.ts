export const InformationPageLabels = {
  infoLabels: {
    personalInformation: "Personal Information",
    creatorSince: "Creator Since",
    legalEntityType: "Legal Entity Type",
    legalEntityDescription: "Legal Entity Description",
    mailingAddress: "Mailing Address",
    miscellaneous: "Miscellaneous",
    success: {
      updatedInformationHeader: "Information update successful",
      personalInformation: "You have successfully updated your Personal Information.",
      legalEntityType: "You have successfully updated your Legal Entity Type.",
      mailingAddress: "You have successfully updated your Mailing Address.",
      miscellaneous: "You have successfully updated your Miscellaneous Information."
    },
    header: {
      calendar: "Calendar"
    },
    labels: {
      none: "None",
      firstName: "First Name",
      lastName: "Last Name",
      EAID: "Electronic Arts ID",
      EAEmail: "Electronic Arts Account Email",
      dateOfBirth: "Date of Birth",
      country: "Country/Region",
      street: "Street",
      city: "City",
      state: "State or Province",
      zipCode: "Zip Code or Postal Code",
      tShirtSize: "T-Shirt Size",
      hardwarePartners: "Hardware Partners",
      entityType: "Entity Type",
      individual: "Individual",
      business: "Business",
      businessName: "Name of Business",
      legalAddressAsMailingAddress: "Address is the same as my mailing address."
    },
    messages: {
      firstName: "First Name is required",
      firstNameTooLong: "First Name is too long",
      lastName: "Last Name is required",
      lastNameTooLong: "Last Name is too long",
      dateOfBirth: "Date of Birth is required",
      dateOfBirthInvalid: "Date of Birth is invalid",
      ageMustBe18OrOlder: "Age must be 18 years or older",
      country: "Country is required",
      street: "Street is required",
      streetTooLong: "Street is too long",
      city: "City is required",
      cityTooLong: "City is too long",
      state: "State is required",
      stateTooLong: "State is too long",
      zipCode: "Zip Code is required",
      zipCodeTooLong: "Zip Code is too long",
      primaryPlatform: "Primary Platform is required",
      tShirtSize: "T-Shirt Size is required",
      hardwarePartners: "Hardware Partners is required",
      entityType: "Entity Type is required",
      businessName: "Business Name is required",
      businessNameTooLong: "Business Name is too long",
      email: "Email is required",
      emailTooLong: "Email is too long",
      emailInvalid: "Email is invalid",
      url: "URL is required",
      invalidUrl: "Invalid URL",
      duplicateUrl: "Duplicate URL",
      urlScanFailed: "URL scan failed",
      followersMaxLength: "Followers max length exceeded"
    },
    info: {
      businessName: "Only if you are contracting under a business entity; otherwise, leave blank."
    },
    profileLabels: {
      updateAvatar: "Update Avatar"
    },
    profilePictureLabels: {
      title: "Change My Avatar",
      message: "Select an image from your computer (JPEG, PNG or GIF). Image should be square and less than 1MB.",
      avatarRequired: "Please select an image",
      avatarInvalid: "Please select valid image",
      avatarMoreThanLimit: "Image size should be less than 1MB",
      termsAndConditionsFirst:
        "Respect the rights of others. Only upload images that you own or that you have written permission to freely distribute.",
      termsAndConditionsMiddle: "User Agreement",
      termsAndConditionsLast: "for more information."
    }
  }
};

export const Buttons = {
  edit: "Edit",
  cancel: "Cancel",
  save: "Save",
  close: "Close",
  browse: "Browse"
};
