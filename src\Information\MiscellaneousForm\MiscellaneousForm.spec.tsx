import { act, render, screen, waitFor, within } from "@testing-library/react";
import MiscellaneousForm from "./MiscellaneousForm";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import { anAdditionalInformationPayload } from "@eait-playerexp-cn/creator-test-fixtures";
import { AdditionalInformationPayload, HoodieSize } from "@eait-playerexp-cn/creator-types";
import { HardwarePartner } from "@eait-playerexp-cn/metadata-types";
import formsRules from "@src/utils/FormRules/CreatorForm";

// Mock the useToast hook
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: jest.fn()
  })
}));

// Mock react-hook-form for testing
const MockFormProvider = ({ children, defaultValues = {} }: { children: React.ReactNode; defaultValues?: any }) => {
  const methods = useForm({
    defaultValues: {
      hoodieSize: defaultValues.hoodieSize || "",
      hardwarePartners: defaultValues.hardwarePartners || [],
      ...defaultValues
    },
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("MiscellaneousForm", () => {
  // Arrange - Mock data setup
  const mockAdditionalInformation: AdditionalInformationPayload = {
    ...anAdditionalInformationPayload(),
    hoodieSize: "M" as HoodieSize,
    hardwarePartners: [
      {
        id: "a0K4x000004kw1EEAQ",
        name: "PlayStation"
      },
      {
        id: "a0K4x000004kw1JEAQ",
        name: "Xbox"
      }
    ]
  };

  const mockHardwarePartners: HardwarePartner[] = [
    {
      value: "a0K4x000004kw1EEAQ",
      label: "PlayStation",
      isCustom: false
    },
    {
      value: "a0K4x000004kw1JEAQ",
      label: "Xbox",
      isCustom: false
    },
    {
      value: "a0K4x000004kw1OEAQ",
      label: "Corsair",
      isCustom: false
    }
  ];

  const infoLabels: {
      personalInformation: "Personal Information",
      creatorSince: "Creator Since",
      legalEntityType: "Legal Entity Type",
      legalEntityDescription: "Legal Entity Description",
      mailingAddress: "Mailing Address",
      miscellaneous: "Miscellaneous",
      success: {
        updatedInformationHeader: "Information update successful",
        personalInformation: "You have successfully updated your Personal Information.",
        legalEntityType: "You have successfully updated your Legal Entity Type.",
        mailingAddress: "You have successfully updated your Mailing Address.",
        miscellaneous: "You have successfully updated your Miscellaneous Information."
      },
      header: {
        calendar: "Calendar"
      },
      labels: {
        none: "None",
        firstName: "First Name",
        lastName: "Last Name",
        EAID: "Electronic Arts ID",
        EAEmail: "Electronic Arts Account Email",
        dateOfBirth: "Date of Birth",
        country: "Country/Region",
        street: "Street",
        city: "City",
        state: "State or Province",
        zipCode: "Zip Code or Postal Code",
        tShirtSize: "T-Shirt Size",
        hardwarePartners: "Hardware Partners",
        entityType: "Entity Type",
        individual: "Individual",
        business: "Business",
        businessName: "Name of Business",
        legalAddressAsMailingAddress: "Address is the same as my mailing address."
      },
      messages: {
        firstName: "First Name is required",
        firstNameTooLong: "First Name is too long",
        lastName: "Last Name is required",
        lastNameTooLong: "Last Name is too long",
        dateOfBirth: "Date of Birth is required",
        dateOfBirthInvalid: "Date of Birth is invalid",
        ageMustBe18OrOlder: "Age must be 18 years or older",
        country: "Country is required",
        street: "Street is required",
        streetTooLong: "Street is too long",
        city: "City is required",
        cityTooLong: "City is too long",
        state: "State is required",
        stateTooLong: "State is too long",
        zipCode: "Zip Code is required",
        zipCodeTooLong: "Zip Code is too long",
        primaryPlatform: "Primary Platform is required",
        tShirtSize: "T-Shirt Size is required",
        hardwarePartners: "Hardware Partners is required",
        entityType: "Entity Type is required",
        businessName: "Business Name is required",
        businessNameTooLong: "Business Name is too long",
        email: "Email is required",
        emailTooLong: "Email is too long",
        emailInvalid: "Email is invalid",
        url: "URL is required",
        invalidUrl: "Invalid URL",
        duplicateUrl: "Duplicate URL",
        urlScanFailed: "URL scan failed",
        followersMaxLength: "Followers max length exceeded"
      },
      info: {
        businessName: "Only if you are contracting under a business entity; otherwise, leave blank."
      },
      profileLabels: {
        updateAvatar: "Update Avatar"
      },
      profilePictureLabels: {
        title: "Change My Avatar",
        message: "Select an image from your computer (JPEG, PNG or GIF). Image should be square and less than 1MB.",
        avatarRequired: "Please select an image",
        avatarInvalid: "Please select valid image",
        avatarMoreThanLimit: "Image size should be less than 1MB",
        termsAndConditionsFirst: "Respect the rights of others. Only upload images that you own or that you have written permission to freely distribute.",
        termsAndConditionsMiddle: "User Agreement",
        termsAndConditionsLast: "for more information."
      }
    };

  const mockProps = {
    
    rules: formsRules.rules(mockProps.infoLabels),
    additionalInformation: mockAdditionalInformation,
    hardwarePartners: mockHardwarePartners,
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close"
    },
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Rendering", () => {
    it("should render miscellaneous form correctly", () => {
      // Arrange & Act
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Assert
      expect(screen.getByText("Miscellaneous")).toBeInTheDocument();
      expect(screen.getByText("T-Shirt Size")).toBeInTheDocument();
      expect(screen.getByText("Hardware Partners")).toBeInTheDocument();
    });

    it("should display hoodie size in read-only mode initially", () => {
      // Arrange & Act
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Assert
      expect(screen.getByText("M")).toBeInTheDocument();
    });

    it("should display hardware partners in read-only mode initially", () => {
      // Arrange & Act
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Assert
      expect(screen.getByText("PlayStation")).toBeInTheDocument();
      expect(screen.getByText("Xbox")).toBeInTheDocument();
    });

    it("should show 'None' when no hoodie size is selected", () => {
      // Arrange
      const propsWithoutHoodieSize = {
        ...mockProps,
        additionalInformation: {
          ...mockAdditionalInformation,
          hoodieSize: ""
        }
      };

      // Act
      render(
        <MockFormProvider>
          <MiscellaneousForm {...propsWithoutHoodieSize} />
        </MockFormProvider>
      );

      // Assert
      expect(screen.getByText("None")).toBeInTheDocument();
    });

    it("should show 'None' when no hardware partners are selected", () => {
      // Arrange
      const propsWithoutHardwarePartners = {
        ...mockProps,
        additionalInformation: {
          ...mockAdditionalInformation,
          hardwarePartners: []
        }
      };

      // Act
      render(
        <MockFormProvider>
          <MiscellaneousForm {...propsWithoutHardwarePartners} />
        </MockFormProvider>
      );

      // Assert
      const hardwarePartnersSection = screen.getByText("Hardware Partners").closest(".miscellaneous-field");
      expect(within(hardwarePartnersSection!).getByText("None")).toBeInTheDocument();
    });

    it("should show edit button initially", () => {
      // Arrange & Act
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Assert
      expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    });
  });

  describe("Edit Mode", () => {
    it("should enable edit mode when edit button is clicked", async () => {
      // Arrange
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      // Assert
      expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
      expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
    });

    it("should show form inputs in edit mode", async () => {
      // Arrange
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      // Assert
      expect(screen.getByRole("combobox")).toBeInTheDocument(); // T-shirt size select
      expect(screen.getByPlaceholderText("Hardware Partners")).toBeInTheDocument(); // MultiSelect
    });

    it("should call onChange when edit mode is toggled", async () => {
      // Arrange
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      // Assert
      expect(mockProps.onChange).toHaveBeenCalledTimes(1);
    });

    it("should exit edit mode when cancel button is clicked", async () => {
      // Arrange
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      const cancelButton = screen.getByRole("button", { name: "Cancel" });
      await userEvent.click(cancelButton);

      // Assert
      expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
      expect(screen.queryByRole("button", { name: "Cancel" })).not.toBeInTheDocument();
      expect(screen.queryByRole("button", { name: "Save" })).not.toBeInTheDocument();
    });

    it("should exit edit mode when isSaved becomes true", async () => {
      // Arrange
      const { rerender } = render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();

      // Simulate save success
      const propsWithSaved = { ...mockProps, isSaved: true };
      rerender(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...propsWithSaved} />
        </MockFormProvider>
      );

      // Assert
      await waitFor(() => {
        expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
      });
    });
  });

  describe("T-Shirt Size Selection", () => {
    it("should display all available t-shirt sizes in edit mode", async () => {
      // Arrange
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      const selectElement = screen.getByRole("combobox");
      await userEvent.click(selectElement);

      // Assert
      const expectedSizes = ["XS", "S", "M", "L", "XL", "XXL", "XXXL"];
      expectedSizes.forEach(size => {
        expect(screen.getByText(size)).toBeInTheDocument();
      });
    });

    it("should show selected t-shirt size in edit mode", async () => {
      // Arrange
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      // Assert - The selected value should be displayed
      expect(screen.getByDisplayValue("M")).toBeInTheDocument();
    });

    it("should handle t-shirt size selection change", async () => {
      // Arrange
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      const selectElement = screen.getByRole("combobox");
      await userEvent.click(selectElement);
      await userEvent.click(screen.getByText("XL"));

      // Assert
      expect(screen.getByDisplayValue("XL")).toBeInTheDocument();
    });
  });

  describe("Hardware Partners Selection", () => {
    it("should display hardware partners multiselect in edit mode", async () => {
      // Arrange
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      // Assert
      expect(screen.getByPlaceholderText("Hardware Partners")).toBeInTheDocument();
    });

    it("should show selected hardware partners in edit mode", async () => {
      // Arrange
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      // Assert - Selected hardware partners should be visible
      expect(screen.getByText("PlayStation")).toBeInTheDocument();
      expect(screen.getByText("Xbox")).toBeInTheDocument();
    });

    it("should handle hardware partner selection", async () => {
      // Arrange
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      const multiSelect = screen.getByPlaceholderText("Hardware Partners");
      await userEvent.click(multiSelect);

      // Assert - Available options should be displayed
      expect(screen.getByText("Corsair")).toBeInTheDocument();
    });

    it("should display multiple hardware partners in read-only mode", () => {
      // Arrange & Act
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Assert
      const hardwarePartnerElements = screen.getAllByText(/PlayStation|Xbox/);
      expect(hardwarePartnerElements).toHaveLength(2);
    });
  });

  describe("Toast Messages", () => {
    it("should show success toast when information is saved", async () => {
      // Arrange
      const mockSuccessToast = jest.fn();
      jest.mocked(require("@eait-playerexp-cn/core-ui-kit").useToast).mockReturnValue({
        success: mockSuccessToast
      });

      const propsWithSaved = {
        ...mockProps,
        isSaved: true
      };

      const { unmount } = renderWithToast(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...propsWithSaved} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      // Assert
      expect(mockSuccessToast).toHaveBeenCalledWith(
        expect.objectContaining({
          props: expect.objectContaining({
            header: "Information update successful",
            content: "You have successfully updated your Miscellaneous Information."
          })
        }),
        expect.objectContaining({
          autoClose: expect.objectContaining({
            timetoDisplay: expect.any(Number)
          })
        })
      );

      unmount();
    });

    it("should calculate toast display time based on message length", async () => {
      // Arrange
      const mockSuccessToast = jest.fn();
      jest.mocked(require("@eait-playerexp-cn/core-ui-kit").useToast).mockReturnValue({
        success: mockSuccessToast
      });

      const longMessage = "A".repeat(200); // Long message to test calculation
      const propsWithLongMessage = {
        ...mockProps,
        isSaved: true,
        infoLabels: {
          ...mockProps.infoLabels,
          success: {
            ...mockProps.infoLabels.success,
            miscellaneous: longMessage
          }
        }
      };

      const { unmount } = renderWithToast(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...propsWithLongMessage} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      // Assert - Toast should appear with calculated time (min 2000ms, max 7000ms)
      expect(mockSuccessToast).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          autoClose: expect.objectContaining({
            timetoDisplay: 7000 // Should be capped at 7000ms
          })
        })
      );

      unmount();
    });
  });

  describe("Component Lifecycle", () => {
    it("should update default values when additionalInformation changes", () => {
      // Arrange
      const { rerender } = render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      const updatedAdditionalInformation = {
        ...mockAdditionalInformation,
        hoodieSize: "XL" as HoodieSize,
        hardwarePartners: [
          {
            id: "a0K4x000004kw1OEAQ",
            name: "Corsair"
          }
        ]
      };

      const updatedProps = {
        ...mockProps,
        additionalInformation: updatedAdditionalInformation
      };

      rerender(
        <MockFormProvider defaultValues={updatedAdditionalInformation}>
          <MiscellaneousForm {...updatedProps} />
        </MockFormProvider>
      );

      // Assert
      expect(screen.getByText("XL")).toBeInTheDocument();
      expect(screen.getByText("Corsair")).toBeInTheDocument();
      expect(screen.queryByText("PlayStation")).not.toBeInTheDocument();
    });

    it("should call onChange when edit state changes", async () => {
      // Arrange
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      // Assert
      expect(mockProps.onChange).toHaveBeenCalledTimes(1);

      // Act
      const cancelButton = screen.getByRole("button", { name: "Cancel" });
      await userEvent.click(cancelButton);

      // Assert
      expect(mockProps.onChange).toHaveBeenCalledTimes(2);
    });
  });

  describe("Loading State", () => {
    it("should show loading state when isLoader is true", () => {
      // Arrange
      const propsWithLoader = {
        ...mockProps,
        isLoader: true
      };

      // Act
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...propsWithLoader} />
        </MockFormProvider>
      );

      // Assert - The ProfileFormAction component should handle the loading state
      expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("should be accessible", async () => {
      // Arrange
      let results: any;
      const { container } = render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      await act(async () => {
        results = await axe(container);
      });

      // Assert
      expect(results).toHaveNoViolations();
    });

    it("should be accessible in edit mode", async () => {
      // Arrange
      let results: any;
      const { container } = render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      await act(async () => {
        results = await axe(container);
      });

      // Assert
      expect(results).toHaveNoViolations();
    });

    it("should have proper aria-labelledby for hoodie size select", async () => {
      // Arrange
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      // Assert
      const selectElement = screen.getByRole("combobox");
      expect(selectElement).toHaveAttribute("id", "hoodie-size");
    });
  });

  describe("Error Handling", () => {
    it("should handle missing onChange prop gracefully", async () => {
      // Arrange
      const propsWithoutOnChange = {
        ...mockProps,
        onChange: undefined
      };

      // Act & Assert
      expect(() => {
        render(
          <MockFormProvider defaultValues={mockAdditionalInformation}>
            <MiscellaneousForm {...propsWithoutOnChange} />
          </MockFormProvider>
        );
      }).not.toThrow();
    });

    it("should handle empty hardware partners array gracefully", () => {
      // Arrange
      const propsWithEmptyHardwarePartners = {
        ...mockProps,
        hardwarePartners: []
      };

      // Act & Assert
      expect(() => {
        render(
          <MockFormProvider defaultValues={mockAdditionalInformation}>
            <MiscellaneousForm {...propsWithEmptyHardwarePartners} />
          </MockFormProvider>
        );
      }).not.toThrow();
    });

    it("should handle null/undefined additionalInformation properties gracefully", () => {
      // Arrange
      const propsWithNullValues = {
        ...mockProps,
        additionalInformation: {
          ...mockAdditionalInformation,
          hoodieSize: null,
          hardwarePartners: null
        }
      };

      // Act & Assert
      expect(() => {
        render(
          <MockFormProvider>
            <MiscellaneousForm {...propsWithNullValues} />
          </MockFormProvider>
        );
      }).not.toThrow();
    });
  });

  describe("Form Validation", () => {
    it("should show validation error for required t-shirt size", async () => {
      // Arrange
      const propsWithValidation = {
        ...mockProps,
        rules: {
          ...mockProps.rules,
          tShirtSize: {
            required: "T-Shirt Size is required"
          }
        }
      };

      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...propsWithValidation} />
        </MockFormProvider>
      );

      // Act
      const editButton = screen.getByRole("button", { name: "Edit" });
      await userEvent.click(editButton);

      const selectElement = screen.getByRole("combobox");
      await userEvent.clear(selectElement);

      // Assert
      await waitFor(() => {
        expect(screen.getByText("T-Shirt Size is required")).toBeInTheDocument();
      });
    });
  });

  describe("Memoization", () => {
    it("should be memoized and not re-render unnecessarily", () => {
      // Arrange
      const { rerender } = render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Act - Re-render with same props
      rerender(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...mockProps} />
        </MockFormProvider>
      );

      // Assert - Component should still render correctly
      expect(screen.getByText("Miscellaneous")).toBeInTheDocument();
      expect(screen.getByText("T-Shirt Size")).toBeInTheDocument();
      expect(screen.getByText("Hardware Partners")).toBeInTheDocument();
    });
  });
});