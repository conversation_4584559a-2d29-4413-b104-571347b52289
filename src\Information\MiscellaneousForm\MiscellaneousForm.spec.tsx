import { act, render, screen, waitFor } from "@testing-library/react";
import MiscellaneousForm from "./MiscellaneousForm";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import { anAdditionalInformationPayload } from "@eait-playerexp-cn/creator-test-fixtures";
import { AdditionalInformationPayload, HoodieSize } from "@eait-playerexp-cn/creator-types";
import { HardwarePartner } from "@eait-playerexp-cn/metadata-types";
import formsRules from "@src/utils/FormRules/CreatorForm";

const mockSuccessToast = jest.fn();
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: mockSuccessToast
  })
}));

const MockFormProvider = ({ children, defaultValues = {} }: { children: React.ReactNode; defaultValues?: any }) => {
  const methods = useForm({
    defaultValues: {
      hoodieSize: defaultValues.hoodieSize || "",
      hardwarePartners: defaultValues.hardwarePartners || [],
      ...defaultValues
    },
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("MiscellaneousForm - Tests for T-Shirt Size and Hardware Partners form functionality including rendering, edit mode, validation, toast messages, accessibility, and error handling", () => {
  const mockAdditionalInformation: AdditionalInformationPayload = {
    ...anAdditionalInformationPayload(),
    hoodieSize: "M" as HoodieSize,
    hardwarePartners: [
      {
        id: "a0K4x000004kw1EEAQ",
        name: "PlayStation"
      },
      {
        id: "a0K4x000004kw1JEAQ",
        name: "Xbox"
      }
    ]
  };

  const mockHardwarePartners: HardwarePartner[] = [
    {
      value: "a0K4x000004kw1EEAQ",
      label: "PlayStation",
      isCustom: false
    },
    {
      value: "a0K4x000004kw1JEAQ",
      label: "Xbox",
      isCustom: false
    },
    {
      value: "a0K4x000004kw1OEAQ",
      label: "Corsair",
      isCustom: false
    }
  ];

  const infoLabels = {
    personalInformation: "Personal Information",
    creatorSince: "Creator Since",
    legalEntityType: "Legal Entity Type",
    legalEntityDescription: "Legal Entity Description",
    mailingAddress: "Mailing Address",
    miscellaneous: "Miscellaneous",
    success: {
      updatedInformationHeader: "Information update successful",
      personalInformation: "You have successfully updated your Personal Information.",
      legalEntityType: "You have successfully updated your Legal Entity Type.",
      mailingAddress: "You have successfully updated your Mailing Address.",
      miscellaneous: "You have successfully updated your Miscellaneous Information."
    },
    header: {
      calendar: "Calendar"
    },
    labels: {
      none: "None",
      firstName: "First Name",
      lastName: "Last Name",
      EAID: "Electronic Arts ID",
      EAEmail: "Electronic Arts Account Email",
      dateOfBirth: "Date of Birth",
      country: "Country/Region",
      street: "Street",
      city: "City",
      state: "State or Province",
      zipCode: "Zip Code or Postal Code",
      tShirtSize: "T-Shirt Size",
      hardwarePartners: "Hardware Partners",
      entityType: "Entity Type",
      individual: "Individual",
      business: "Business",
      businessName: "Name of Business",
      legalAddressAsMailingAddress: "Address is the same as my mailing address."
    },
    messages: {
      firstName: "First Name is required",
      firstNameTooLong: "First Name is too long",
      lastName: "Last Name is required",
      lastNameTooLong: "Last Name is too long",
      dateOfBirth: "Date of Birth is required",
      dateOfBirthInvalid: "Date of Birth is invalid",
      ageMustBe18OrOlder: "Age must be 18 years or older",
      country: "Country is required",
      street: "Street is required",
      streetTooLong: "Street is too long",
      city: "City is required",
      cityTooLong: "City is too long",
      state: "State is required",
      stateTooLong: "State is too long",
      zipCode: "Zip Code is required",
      zipCodeTooLong: "Zip Code is too long",
      primaryPlatform: "Primary Platform is required",
      tShirtSize: "T-Shirt Size is required",
      hardwarePartners: "Hardware Partners is required",
      entityType: "Entity Type is required",
      businessName: "Business Name is required",
      businessNameTooLong: "Business Name is too long",
      email: "Email is required",
      emailTooLong: "Email is too long",
      emailInvalid: "Email is invalid",
      url: "URL is required",
      invalidUrl: "Invalid URL",
      duplicateUrl: "Duplicate URL",
      urlScanFailed: "URL scan failed",
      followersMaxLength: "Followers max length exceeded"
    },
    info: {
      businessName: "Only if you are contracting under a business entity; otherwise, leave blank."
    },
    profileLabels: {
      updateAvatar: "Update Avatar"
    },
    profilePictureLabels: {
      title: "Change My Avatar",
      message: "Select an image from your computer (JPEG, PNG or GIF). Image should be square and less than 1MB.",
      avatarRequired: "Please select an image",
      avatarInvalid: "Please select valid image",
      avatarMoreThanLimit: "Image size should be less than 1MB",
      termsAndConditionsFirst: "Respect the rights of others. Only upload images that you own or that you have written permission to freely distribute.",
      termsAndConditionsMiddle: "User Agreement",
      termsAndConditionsLast: "for more information."
    }
  };

  const miscellaneousProps = {
    infoLabels,
    rules: formsRules.rules(infoLabels),
    additionalInformation: mockAdditionalInformation,
    hardwarePartners: mockHardwarePartners,
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close"
    },
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render miscellaneous form correctly", () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    expect(screen.getByText("Miscellaneous")).toBeInTheDocument();
    expect(screen.getByText("T-Shirt Size")).toBeInTheDocument();
    expect(screen.getByText("Hardware Partners")).toBeInTheDocument();
  });

  it("should display hoodie size in read-only mode initially", () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    expect(screen.getByText("M")).toBeInTheDocument();
  });

  it("should display hardware partners in read-only mode initially", () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    expect(screen.getByText("PlayStation")).toBeInTheDocument();
    expect(screen.getByText("Xbox")).toBeInTheDocument();
  });

  it("should show 'None' when no hoodie size is selected", () => {
    const propsWithoutHoodieSize = {
      ...miscellaneousProps,
      additionalInformation: {
        ...mockAdditionalInformation,
        hoodieSize: "" as HoodieSize
      }
    };

    render(
      <MockFormProvider>
        <MiscellaneousForm {...propsWithoutHoodieSize} />
      </MockFormProvider>
    );

    expect(screen.getByText("None")).toBeInTheDocument();
  });

  it("should show 'None' when no hardware partners are selected", () => {
    const propsWithoutHardwarePartners = {
      ...miscellaneousProps,
      additionalInformation: {
        ...mockAdditionalInformation,
        hardwarePartners: []
      }
    };

    render(
      <MockFormProvider>
        <MiscellaneousForm {...propsWithoutHardwarePartners} />
      </MockFormProvider>
    );

    expect(screen.getByText("None")).toBeInTheDocument();
  });

  it("should show edit button initially", () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
  });

  it("should enable edit mode when edit button is clicked", async () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
  });

  it("should show form inputs in edit mode", async () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("button", { name: "T-Shirt Size" })).toBeInTheDocument();
    expect(screen.getByTestId("multi-select-id")).toBeInTheDocument();
  });

  it("should call onChange when edit mode is toggled", async () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(miscellaneousProps.onChange).toHaveBeenCalledTimes(1);
  });

  it("should exit edit mode when cancel button is clicked", async () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    await userEvent.click(cancelButton);

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Cancel" })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Save" })).not.toBeInTheDocument();
  });

  it("should exit edit mode when isSaved becomes true", async () => {
    const { rerender } = render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();

    const propsWithSaved = { ...miscellaneousProps, isSaved: true };
    rerender(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...propsWithSaved} />
      </MockFormProvider>
    );

    await waitFor(() => {
      expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    });
  });

  it("should display all available t-shirt sizes in edit mode", async () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const selectElement = screen.getByRole("button", { name: "T-Shirt Size" });
    await userEvent.click(selectElement);

    const expectedSizes = ["XS", "S", "L", "XL", "XXL", "XXXL"];
    expectedSizes.forEach(size => {
      expect(screen.getByText(size)).toBeInTheDocument();
    });
    expect(screen.getAllByText("M")).toHaveLength(2);
  });

  it("should show selected t-shirt size in edit mode", async () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByText("M")).toBeInTheDocument();
  });

  it("should handle t-shirt size selection change", async () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const selectElement = screen.getByRole("button", { name: "T-Shirt Size" });
    await userEvent.click(selectElement);
    await userEvent.click(screen.getByText("XL"));

    expect(screen.getByText("XL")).toBeInTheDocument();
  });

  it("should display hardware partners multiselect in edit mode", async () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByTestId("multi-select-id")).toBeInTheDocument();
  });

  it("should show selected hardware partners in edit mode", async () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByTestId("multi-select-id")).toBeInTheDocument();
  });

  it("should handle hardware partner selection", async () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const multiSelect = screen.getByTestId("multi-select-id");
    await userEvent.click(multiSelect);

    expect(screen.getByText("Corsair")).toBeInTheDocument();
  });

  it("should display multiple hardware partners in read-only mode", () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    const hardwarePartnerElements = screen.getAllByText(/PlayStation|Xbox/);
    expect(hardwarePartnerElements).toHaveLength(2);
  });

  it("should show success toast when information is saved", async () => {
    const propsWithSaved = {
      ...miscellaneousProps,
      isSaved: true
    };

    const { unmount } = renderWithToast(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...propsWithSaved} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(mockSuccessToast).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          header: "Information update successful",
          content: "You have successfully updated your Miscellaneous Information."
        })
      }),
      expect.objectContaining({
        autoClose: expect.objectContaining({
          timetoDisplay: expect.any(Number)
        })
      })
    );

    unmount();
  });

  it("should calculate toast display time based on message length", async () => {
    const longMessage = "A".repeat(200);
    const propsWithLongMessage = {
      ...miscellaneousProps,
      isSaved: true,
      infoLabels: {
        ...miscellaneousProps.infoLabels,
        success: {
          ...miscellaneousProps.infoLabels.success,
          miscellaneous: longMessage
        }
      }
    };

    const { unmount } = renderWithToast(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...propsWithLongMessage} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(mockSuccessToast).toHaveBeenCalledWith(
      expect.anything(),
      expect.objectContaining({
        autoClose: expect.objectContaining({
          timetoDisplay: 7000
        })
      })
    );

    unmount();
  });

  it("should update default values when additionalInformation changes", () => {
    const { rerender } = render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    const updatedAdditionalInformation = {
      ...mockAdditionalInformation,
      hoodieSize: "XL" as HoodieSize,
      hardwarePartners: [
        {
          id: "a0K4x000004kw1OEAQ",
          name: "Corsair"
        }
      ]
    };

    const updatedProps = {
      ...miscellaneousProps,
      additionalInformation: updatedAdditionalInformation
    };

    rerender(
      <MockFormProvider defaultValues={updatedAdditionalInformation}>
        <MiscellaneousForm {...updatedProps} />
      </MockFormProvider>
    );

    expect(screen.getByText("XL")).toBeInTheDocument();
    expect(screen.getByText("Corsair")).toBeInTheDocument();
    expect(screen.queryByText("PlayStation")).not.toBeInTheDocument();
  });

  it("should call onChange when edit state changes", async () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(miscellaneousProps.onChange).toHaveBeenCalledTimes(1);

    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    await userEvent.click(cancelButton);

    expect(miscellaneousProps.onChange).toHaveBeenCalledTimes(2);
  });

  it("should show loading state when isLoader is true", () => {
    const propsWithLoader = {
      ...miscellaneousProps,
      isLoader: true
    };

    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...propsWithLoader} />
      </MockFormProvider>
    );

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
  });

  it("should be accessible", async () => {
    let results: any;
    const { container } = render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  it("should have proper aria-labelledby for hoodie size select", async () => {
    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const selectElement = screen.getByRole("button", { name: "T-Shirt Size" });
    expect(selectElement).toHaveAttribute("aria-labelledby", "hoodie-size");
  });

  it("should handle missing onChange prop gracefully", async () => {
    const propsWithoutOnChange = {
      ...miscellaneousProps,
      onChange: undefined
    };

    expect(() => {
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...propsWithoutOnChange} />
        </MockFormProvider>
      );
    }).not.toThrow();
  });

  it("should handle empty hardware partners array gracefully", () => {
    const propsWithEmptyHardwarePartners = {
      ...miscellaneousProps,
      hardwarePartners: []
    };

    expect(() => {
      render(
        <MockFormProvider defaultValues={mockAdditionalInformation}>
          <MiscellaneousForm {...propsWithEmptyHardwarePartners} />
        </MockFormProvider>
      );
    }).not.toThrow();
  });

  it("should handle null/undefined additionalInformation properties gracefully", () => {
    const propsWithNullValues = {
      ...miscellaneousProps,
      additionalInformation: {
        ...mockAdditionalInformation,
        hoodieSize: "" as HoodieSize,
        hardwarePartners: []
      }
    };

    expect(() => {
      render(
        <MockFormProvider>
          <MiscellaneousForm {...propsWithNullValues} />
        </MockFormProvider>
      );
    }).not.toThrow();
  });

  it("should show validation error for required t-shirt size", async () => {
    const propsWithValidation = {
      ...miscellaneousProps,
      rules: {
        ...miscellaneousProps.rules,
        tShirtSize: {
          required: "T-Shirt Size is required"
        }
      }
    };

    render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...propsWithValidation} />
      </MockFormProvider>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("button", { name: "T-Shirt Size" })).toBeInTheDocument();
  });

  it("should be memoized and not re-render unnecessarily", () => {
    const { rerender } = render(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    rerender(
      <MockFormProvider defaultValues={mockAdditionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    expect(screen.getByText("Miscellaneous")).toBeInTheDocument();
    expect(screen.getByText("T-Shirt Size")).toBeInTheDocument();
    expect(screen.getByText("Hardware Partners")).toBeInTheDocument();
  });
});