import { render, screen } from "@testing-library/react";
import MiscellaneousForm from "./MiscellaneousForm";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import { anAdditionalInformationPayload } from "@eait-playerexp-cn/creator-test-fixtures";
import { AdditionalInformationPayload, HoodieSize } from "@eait-playerexp-cn/creator-types";
import { HardwarePartner } from "@eait-playerexp-cn/metadata-types";
import { Buttons, InformationPageLabels } from "@src/Translations/InformationPageLabels";
import { aHardwarePartner } from "@eait-playerexp-cn/metadata-test-fixtures";

const successToast = jest.fn();
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: successToast
  })
}));

const FormWrapper = ({
  children,
  defaultValues = {} as AdditionalInformationPayload
}: {
  children: React.ReactNode;
  defaultValues?: AdditionalInformationPayload;
}) => {
  const methods = useForm({
    defaultValues,
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("MiscellaneousForm", () => {
  const additionalInformation: AdditionalInformationPayload = anAdditionalInformationPayload();
  const hardwarePartners: HardwarePartner[] = [aHardwarePartner(), aHardwarePartner(), aHardwarePartner()];
  const infoLabels = InformationPageLabels.infoLabels;
  const miscellaneousProps = {
    labels: { infoLabels, buttons: Buttons },
    additionalInformation: additionalInformation,
    hardwarePartners,
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show miscellaneous form correctly", () => {
    render(
      <FormWrapper>
        <MiscellaneousForm {...miscellaneousProps} />
      </FormWrapper>
    );

    expect(screen.getByText(infoLabels.miscellaneous)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.tShirtSize)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.hardwarePartners)).toBeInTheDocument();
    expect(screen.getByText(additionalInformation.hoodieSize)).toBeInTheDocument();
    expect(screen.getByText(additionalInformation.hardwarePartners[0].name)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
  });

  it("should show 'None' when hoodie size and hardware partners are not present", () => {
    const propsWithoutHoodieSize = {
      ...miscellaneousProps,
      additionalInformation: {
        ...additionalInformation,
        hoodieSize: "" as HoodieSize,
        hardwarePartners: []
      }
    };

    render(
      <FormWrapper>
        <MiscellaneousForm {...propsWithoutHoodieSize} />
      </FormWrapper>
    );

    expect(screen.getAllByText(infoLabels.labels.none)).toHaveLength(2);
  });

  it("should enable edit mode when edit button is clicked", async () => {
    render(
      <FormWrapper>
        <MiscellaneousForm {...{ ...miscellaneousProps, isSaved: undefined }} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: infoLabels.labels.tShirtSize })).toBeInTheDocument();
    expect(screen.getByTestId("multi-select-id")).toBeInTheDocument();
    expect(miscellaneousProps.onChange).toHaveBeenCalledTimes(1);
  });

  it("should exit edit mode when cancel button is clicked", async () => {
    render(
      <FormWrapper>
        <MiscellaneousForm
          {...{
            ...miscellaneousProps,
            additionalInformation: { ...additionalInformation, hardwarePartners: undefined }
          }}
        />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: "Edit" }));
    expect(screen.queryByRole("button", { name: "Edit" })).not.toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();

    await userEvent.click(screen.getByRole("button", { name: "Cancel" }));

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Cancel" })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Save" })).not.toBeInTheDocument();
  });

  it("should display all available t-shirt sizes in edit mode", async () => {
    render(
      <FormWrapper>
        <MiscellaneousForm {...miscellaneousProps} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    await userEvent.click(screen.getByRole("button", { name: infoLabels.labels.tShirtSize }));

    const expectedSizes = ["XS", "S", "L", "XL", "XXL", "XXXL"];
    expectedSizes.forEach((size) => {
      expect(screen.getAllByText(size).length).toBeGreaterThan(0);
    });
    expect(screen.getAllByText(additionalInformation.hoodieSize)).toHaveLength(2);
  });

  it("should show success toast when information is saved", async () => {
    const propsWithSaved = {
      ...miscellaneousProps,
      isSaved: true
    };

    const { unmount } = renderWithToast(
      <FormWrapper>
        <MiscellaneousForm {...propsWithSaved} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(successToast).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          header: infoLabels.success.updatedInformationHeader,
          content: infoLabels.success.miscellaneous
        })
      }),
      expect.objectContaining({
        autoClose: expect.objectContaining({
          timetoDisplay: expect.any(Number)
        })
      })
    );

    unmount();
  });

  it("should be accessible", async () => {
    const { container } = render(
      <FormWrapper>
        <MiscellaneousForm {...miscellaneousProps} />
      </FormWrapper>
    );

    expect(await axe(container)).toHaveNoViolations();
  });

  it("should handle null/undefined additionalInformation properties", () => {
    const propsWithNullValues = {
      ...miscellaneousProps,
      additionalInformation: {
        ...additionalInformation,
        hoodieSize: "" as HoodieSize,
        hardwarePartners: []
      }
    };

    expect(() => {
      render(
        <FormWrapper>
          <MiscellaneousForm {...propsWithNullValues} />
        </FormWrapper>
      );
    }).not.toThrow();
  });
});