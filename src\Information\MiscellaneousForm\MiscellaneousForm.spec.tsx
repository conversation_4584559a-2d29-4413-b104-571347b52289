import { act, render, screen, waitFor } from "@testing-library/react";
import MiscellaneousForm from "./MiscellaneousForm";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import { anAdditionalInformationPayload } from "@eait-playerexp-cn/creator-test-fixtures";
import { AdditionalInformationPayload, HoodieSize } from "@eait-playerexp-cn/creator-types";
import { HardwarePartner } from "@eait-playerexp-cn/metadata-types";
import formsRules from "@src/utils/FormRules/CreatorForm";
import { InformationPageLabels } from "@src/Translations/InformationPageLabels";

const successToast = jest.fn();
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: successToast
  })
}));

const MockFormProvider = ({ children, defaultValues = {} }: { children: React.ReactNode; defaultValues?: any }) => {
  const methods = useForm({
    defaultValues: {
      hoodieSize: defaultValues.hoodieSize || "",
      hardwarePartners: defaultValues.hardwarePartners || [],
      ...defaultValues
    },
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("MiscellaneousForm - Tests for T-Shirt Size and Hardware Partners form functionality including rendering, edit mode, validation, toast messages, accessibility, and error handling", () => {
  const additionalInformation: AdditionalInformationPayload = {
    ...anAdditionalInformationPayload(),
    hoodieSize: "M" as HoodieSize,
    hardwarePartners: [
      {
        id: "a0K4x000004kw1EEAQ",
        name: "PlayStation"
      },
      {
        id: "a0K4x000004kw1JEAQ",
        name: "Xbox"
      }
    ]
  };

  const mockHardwarePartners: HardwarePartner[] = [
    {
      value: "a0K4x000004kw1EEAQ",
      label: "PlayStation",
      isCustom: false
    },
    {
      value: "a0K4x000004kw1JEAQ",
      label: "Xbox",
      isCustom: false
    },
    {
      value: "a0K4x000004kw1OEAQ",
      label: "Corsair",
      isCustom: false
    }
  ];

  const infoLabels = InformationPageLabels.infoLabels;

  const miscellaneousProps = {
    infoLabels,
    rules: formsRules.rules(infoLabels),
    additionalInformation: additionalInformation,
    hardwarePartners: mockHardwarePartners,
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close"
    },
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show miscellaneous form correctly", () => {
    render(
      <MockFormProvider defaultValues={additionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    expect(screen.getByText(infoLabels.miscellaneous)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.tShirtSize)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.hardwarePartners)).toBeInTheDocument();
    expect(screen.getByText("M")).toBeInTheDocument();
    expect(screen.getByText("PlayStation")).toBeInTheDocument();
    expect(screen.getByText("Xbox")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
  });

  it("should show 'None' when hoodie size and hardware partners are not present", () => {
    const propsWithoutHoodieSize = {
      ...miscellaneousProps,
      additionalInformation: {
        ...additionalInformation,
        hoodieSize: "" as HoodieSize,
        hardwarePartners: []
      }
    };

    render(
      <MockFormProvider>
        <MiscellaneousForm {...propsWithoutHoodieSize} />
      </MockFormProvider>
    );

    expect(screen.getAllByText(infoLabels.labels.none)).toHaveLength(2);
  });

  it("should enable edit mode when edit button is clicked", async () => {
    render(
      <MockFormProvider defaultValues={additionalInformation}>
        <MiscellaneousForm {...{...miscellaneousProps, isSaved: undefined}} />
      </MockFormProvider>
    );
    
    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: infoLabels.labels.tShirtSize })).toBeInTheDocument();
    expect(screen.getByTestId("multi-select-id")).toBeInTheDocument();
    expect(miscellaneousProps.onChange).toHaveBeenCalledTimes(1);
  });

  it("should exit edit mode when cancel button is clicked", async () => {
    render(
      <MockFormProvider defaultValues={additionalInformation}>
        <MiscellaneousForm {...{...miscellaneousProps, additionalInformation: {...additionalInformation, hardwarePartners: undefined}}} />
      </MockFormProvider>
    );
    await userEvent.click(screen.getByRole("button", { name: "Edit" }));
    expect(screen.queryByRole("button", { name: "Edit" })).not.toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();

    await userEvent.click(screen.getByRole("button", { name: "Cancel" }));

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Cancel" })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Save" })).not.toBeInTheDocument();
  });

  it("should display all available t-shirt sizes in edit mode", async () => {
    render(
      <MockFormProvider defaultValues={additionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    await userEvent.click(screen.getByRole("button", { name: infoLabels.labels.tShirtSize }));

    const expectedSizes = ["XS", "S", "L", "XL", "XXL", "XXXL"];
    expectedSizes.forEach(size => {
      expect(screen.getByText(size)).toBeInTheDocument();
    });
    expect(screen.getAllByText("M")).toHaveLength(2);
  });

  it("should show success toast when information is saved", async () => {
    const propsWithSaved = {
      ...miscellaneousProps,
      isSaved: true
    };

    const { unmount } = renderWithToast(
      <MockFormProvider defaultValues={additionalInformation}>
        <MiscellaneousForm {...propsWithSaved} />
      </MockFormProvider>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(successToast).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          header: infoLabels.success.updatedInformationHeader,
          content: infoLabels.success.miscellaneous
        })
      }),
      expect.objectContaining({
        autoClose: expect.objectContaining({
          timetoDisplay: expect.any(Number)
        })
      })
    );

    unmount();
  });

  it("should calculate toast display time based on message length", async () => {
    const longMessage = "A".repeat(200);
    const propsWithLongMessage = {
      ...miscellaneousProps,
      isSaved: true,
      infoLabels: {
        ...miscellaneousProps.infoLabels,
        success: {
          ...miscellaneousProps.infoLabels.success,
          miscellaneous: longMessage
        }
      }
    };

    const { unmount } = renderWithToast(
      <MockFormProvider defaultValues={additionalInformation}>
        <MiscellaneousForm {...propsWithLongMessage} />
      </MockFormProvider>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }))

    expect(successToast).toHaveBeenCalledWith(
      expect.anything(),
      expect.objectContaining({
        autoClose: expect.objectContaining({
          timetoDisplay: 7000
        })
      })
    );

    unmount();
  });

  it("should be accessible", async () => {
    let results: any;
    const { container } = render(
      <MockFormProvider defaultValues={additionalInformation}>
        <MiscellaneousForm {...miscellaneousProps} />
      </MockFormProvider>
    );

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  it("should handle null/undefined additionalInformation properties gracefully", () => {
    const propsWithNullValues = {
      ...miscellaneousProps,
      additionalInformation: {
        ...additionalInformation,
        hoodieSize: "" as HoodieSize,
        hardwarePartners: []
      }
    };

    expect(() => {
      render(
        <MockFormProvider>
          <MiscellaneousForm {...propsWithNullValues} />
        </MockFormProvider>
      );
    }).not.toThrow();
  });
});