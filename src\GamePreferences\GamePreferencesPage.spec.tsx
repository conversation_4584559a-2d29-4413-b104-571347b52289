import { render, screen } from "@testing-library/react";
import GamePreferencesPage from "./GamePreferencesPage";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import { CreatorProfile } from "@eait-playerexp-cn/creators-http-client";
import { Franchise, Platform } from "@eait-playerexp-cn/metadata-types";
import { InformationPageLabels } from "@src/Translations/InformationPageLabels";
import { FranchisesYouPlayLabels } from "./GamePreferencesPage";

// Mock the dependencies
jest.mock("@eait-playerexp-cn/creators-http-client", () => ({
  CreatorsService: jest.fn().mockImplementation(() => ({
    updateCreator: jest.fn().mockResolvedValue({ data: {} })
  }))
}));

// Mock context and utils since they might not exist in this test environment
jest.mock("../../context", () => ({
  useAppContext: () => ({
    dispatch: jest.fn(),
    state: {
      isValidationError: false,
      validationErrors: [],
      isError: false
    }
  })
}));

jest.mock("../../utils", () => ({
  ERROR: "ERROR",
  VALIDATION_ERROR: "VALIDATION_ERROR",
  useAsync: () => ({
    execute: jest.fn(),
    status: "idle",
    value: null,
    error: null
  }),
  onToastClose: jest.fn(),
  toastContent: jest.fn()
}));

jest.mock("next/router", () => ({
  useRouter: () => ({
    push: jest.fn(),
    query: {}
  })
}));

jest.mock("../../context/DependencyContext", () => ({
  useDependency: () => ({
    errorHandler: jest.fn(),
    creatorsClient: {},
    configuration: {
      PROGRAM_CODE: "test-program",
      DEFAULT_AVATAR_IMAGE: "/default-avatar.png"
    },
    client: {}
  })
}));

jest.mock("../Form", () => {
  return function MockForm({ children, onSubmit }: { children: React.ReactNode; onSubmit: any }) {
    return (
      <form data-testid="form-wrapper" onSubmit={onSubmit}>
        {children}
      </form>
    );
  };
});

// Mock the form components
jest.mock("./FranchiseYouPlayForm/FranchiseYouPlayForm", () => {
  return function MockFranchiseYouPlayForm(props: any) {
    return (
      <div data-testid="franchise-form">
        <h3>{props.franchisesYouPlayLabels.title}</h3>
        <div>{props.franchisesYouPlayLabels.primaryFranchiseTitle}</div>
        <div>{props.franchisesYouPlayLabels.secondaryFranchiseTitle}</div>
        {props.preferredPrimaryFranchises && <div>{props.preferredPrimaryFranchises.label}</div>}
        {props.preferredSecondaryFranchises?.map((franchise: any, index: number) => (
          <div key={index}>{franchise.label}</div>
        ))}
      </div>
    );
  };
});

jest.mock("./PlatformPreferencesForm/PlatformPreferencesForm", () => {
  return function MockPlatformPreferencesForm(props: any) {
    return (
      <div data-testid="platform-form">
        <h3>{props.infoLabels.platformPreferences}</h3>
        <div>{props.infoLabels.primaryPlatform}</div>
        <div>{props.infoLabels.secondaryPlatforms}</div>
        {props.preferredPrimaryPlatforms && <div>{props.preferredPrimaryPlatforms.label}</div>}
        {props.preferredSecondaryPlatforms?.map((platform: any, index: number) => (
          <div key={index}>{platform.label}</div>
        ))}
      </div>
    );
  };
});

describe("MockGamePreferencesPage - Tests for game preferences page functionality including rendering, form integration, creator updates, franchise and platform management, accessibility, and error handling", () => {
  const creator: Creator = {
    preferredPrimaryFranchise: {
      value: "fifa",
      label: "FIFA",
      image: "/img/franchises/fifa.png"
    },
    preferredSecondaryFranchises: [
      {
        value: "madden",
        label: "Madden NFL",
        image: "/img/franchises/madden.png"
      }
    ],
    preferredPrimaryPlatform: {
      value: "pc",
      label: "PC",
      imageAsIcon: "/img/platforms/pc.png"
    },
    preferredSecondaryPlatforms: [
      {
        value: "xbox",
        label: "Xbox",
        imageAsIcon: "/img/platforms/xbox.png"
      }
    ]
  };

  const franchises: Franchise[] = [
    {
      value: "fifa",
      label: "FIFA",
      image: "/img/franchises/fifa.png"
    },
    {
      value: "madden",
      label: "Madden NFL",
      image: "/img/franchises/madden.png"
    }
  ];

  const platforms: Platform[] = [
    {
      value: "pc",
      label: "PC",
      imageAsIcon: "/img/platforms/pc.png"
    },
    {
      value: "xbox",
      label: "Xbox",
      imageAsIcon: "/img/platforms/xbox.png"
    }
  ];

  const franchisesYouPlayLabels: FranchisesYouPlayLabels = {
    title: "Franchises You Play",
    description: "Select the game franchises you play",
    primaryFranchiseTitle: "Primary Franchise",
    primaryFranchiseSubTitle: "Select your main gaming franchise",
    secondaryFranchiseTitle: "Secondary Franchises",
    secondaryFranchiseSubTitle: "Select additional franchises you play",
    labels: {
      primaryFranchise: "Primary Franchise"
    },
    messages: {
      success: {
        header: "Franchises Updated",
        content: "Your franchise preferences have been updated successfully"
      }
    }
  };

  const infoLabels: InfoLabels = {
    platformPreferences: "Platform Preferences",
    primaryPlatform: "Primary Platform",
    platformPreferencesTitle: "Select your primary gaming platform",
    secondaryPlatforms: "Secondary Platforms",
    secondaryPlatformsTitle: "Select additional platforms you use",
    success: {
      updatedInformationHeader: "Information Updated",
      platformPreferences: "Platform preferences updated successfully"
    }
  };

  const buttons: Buttons = {
    edit: "Edit",
    cancel: "Cancel",
    save: "Save",
    close: "Close"
  };

  const layout: Layout = {
    main: {
      unhandledError: "An unexpected error occurred"
    }
  };

  const analytics: Analytics = {
    track: jest.fn()
  };

  const props: GamePreferencesPageProps = {
    franchisesYouPlayLabels,
    infoLabels,
    buttons,
    creator,
    updateCreator: jest.fn(),
    franchises,
    platforms,
    layout,
    analytics
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show game preferences page correctly", () => {
    render(<MockGamePreferencesPage {...props} />);

    expect(screen.getByTestId("game-preferences-page")).toBeInTheDocument();
    expect(screen.getByText("Franchises You Play")).toBeInTheDocument();
    expect(screen.getByText("Platform Preferences")).toBeInTheDocument();
  });

  it("should render FranchiseYouPlayForm with correct props", () => {
    render(<MockGamePreferencesPage {...props} />);

    expect(screen.getByText("FIFA")).toBeInTheDocument();
    expect(screen.getByText("Madden NFL")).toBeInTheDocument();
    expect(screen.getByText("Primary Franchise")).toBeInTheDocument();
    expect(screen.getByText("Secondary Franchises")).toBeInTheDocument();
  });

  it("should render PlatformPreferencesForm with correct props", () => {
    render(<MockGamePreferencesPage {...props} />);

    expect(screen.getByText("PC")).toBeInTheDocument();
    expect(screen.getByText("Xbox")).toBeInTheDocument();
    expect(screen.getByText("Primary Platform")).toBeInTheDocument();
    expect(screen.getByText("Secondary Platforms")).toBeInTheDocument();
  });

  it("should handle creator updates correctly", async () => {
    const updateCreator = jest.fn();
    const propsWithUpdateCreator = {
      ...props,
      updateCreator
    };

    render(<MockGamePreferencesPage {...propsWithUpdateCreator} />);

    const saveButton = screen.getByRole("button", { name: "Save" });
    await userEvent.click(saveButton);

    expect(updateCreator).toHaveBeenCalledWith(props.creator);
  });

  it("should handle empty creator preferences", () => {
    const emptyCreator: Creator = {
      preferredPrimaryFranchise: null,
      preferredSecondaryFranchises: [],
      preferredPrimaryPlatform: null,
      preferredSecondaryPlatforms: []
    };

    const propsWithEmptyCreator = {
      ...props,
      creator: emptyCreator
    };

    render(<MockGamePreferencesPage {...propsWithEmptyCreator} />);

    expect(screen.getByTestId("game-preferences-page")).toBeInTheDocument();
    expect(screen.getByText("Franchises You Play")).toBeInTheDocument();
    expect(screen.getByText("Platform Preferences")).toBeInTheDocument();
  });

  it("should handle empty franchises and platforms arrays", () => {
    const propsWithEmptyArrays = {
      ...props,
      franchises: [],
      platforms: []
    };

    expect(() => {
      render(<MockGamePreferencesPage {...propsWithEmptyArrays} />);
    }).not.toThrow();
  });

  it("should be accessible", async () => {
    let results: any;
    const { container } = render(<MockGamePreferencesPage {...props} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  it("should handle missing props gracefully", () => {
    const minimalProps = {
      ...props,
      analytics: { track: () => {} }
    };

    expect(() => {
      render(<MockGamePreferencesPage {...minimalProps} />);
    }).not.toThrow();
  });

  it("should be memoized and not re-render unnecessarily", () => {
    const { rerender } = render(<MockGamePreferencesPage {...props} />);

    rerender(<MockGamePreferencesPage {...props} />);

    expect(screen.getByTestId("game-preferences-page")).toBeInTheDocument();
    expect(screen.getByText("Franchises You Play")).toBeInTheDocument();
    expect(screen.getByText("Platform Preferences")).toBeInTheDocument();
  });
});
