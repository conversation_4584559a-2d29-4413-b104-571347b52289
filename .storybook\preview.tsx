import React from "react";
import { Preview } from "@storybook/react";
import { Description, Stories, Title } from "@storybook/blocks";
import "@eait-playerexp-cn/core-ui-kit/dist/style/core-ui-kit.css";
import "@eait-playerexp-cn/core-ui-kit/dist/style/the-sims-ugx.css"; // remove this after dev
import "../src/styles/global.css";
import "../src/styles/storybook.css";

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i
      }
    },
    docs: {
      page: () => (
        <>
          <Title />
          <Description />
          <Stories includePrimary />
        </>
      )
    }
  }
};

export default preview;
