{"compilerOptions": {"baseUrl": ".", "target": "es2020", "lib": ["dom", "dom.iterable", "esnext"], "paths": {"@src/*": ["src/*"]}, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "jsx": "react", "strict": false, "skipLibCheck": true, "outDir": "dist", "noEmit": true, "allowImportingTsExtensions": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "jest.setup.ts", "src/**/*.js", "src/**/**/*.ts", "src/**/**/*.js"], "exclude": ["node_modules"]}