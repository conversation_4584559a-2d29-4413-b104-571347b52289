// @ts-nocheck
function stryNS_9fa48() {
  var g = typeof globalThis === 'object' && globalThis && globalThis.Math === Math && globalThis || new Function("return this")();
  var ns = g.__stryker__ || (g.__stryker__ = {});
  if (ns.activeMutant === undefined && g.process && g.process.env && g.process.env.__STRYKER_ACTIVE_MUTANT__) {
    ns.activeMutant = g.process.env.__STRYKER_ACTIVE_MUTANT__;
  }
  function retrieveNS() {
    return ns;
  }
  stryNS_9fa48 = retrieveNS;
  return retrieveNS();
}
stryNS_9fa48();
function stryCov_9fa48() {
  var ns = stryNS_9fa48();
  var cov = ns.mutantCoverage || (ns.mutantCoverage = {
    static: {},
    perTest: {}
  });
  function cover() {
    var c = cov.static;
    if (ns.currentTestId) {
      c = cov.perTest[ns.currentTestId] = cov.perTest[ns.currentTestId] || {};
    }
    var a = arguments;
    for (var i = 0; i < a.length; i++) {
      c[a[i]] = (c[a[i]] || 0) + 1;
    }
  }
  stryCov_9fa48 = cover;
  cover.apply(null, arguments);
}
function stryMutAct_9fa48(id) {
  var ns = stryNS_9fa48();
  function isActive(id) {
    if (ns.activeMutant === id) {
      if (ns.hitCount !== void 0 && ++ns.hitCount > ns.hitLimit) {
        throw new Error('Stryker: Hit count limit reached (' + ns.hitCount + ')');
      }
      return true;
    }
    return false;
  }
  stryMutAct_9fa48 = isActive;
  return isActive(id);
}
import { screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
export async function selectOption({
  option,
  container,
  label
}: {
  option: string;
  container: HTMLElement;
  label: string;
}): Promise<HTMLElement> {
  if (stryMutAct_9fa48("228")) {
    {}
  } else {
    stryCov_9fa48("228");
    const dropdown = screen.getByLabelText(label);
    await userEvent.click(dropdown);
    // Wait for the options to be displayed
    await waitFor(stryMutAct_9fa48("229") ? () => undefined : (stryCov_9fa48("229"), () => expect(container.querySelector(stryMutAct_9fa48("230") ? "" : (stryCov_9fa48("230"), ".select-list"))).toBeInTheDocument()), stryMutAct_9fa48("231") ? {} : (stryCov_9fa48("231"), {
      timeout: 1_100
    }));
    await userEvent.click(await screen.findByRole(stryMutAct_9fa48("232") ? "" : (stryCov_9fa48("232"), "button"), stryMutAct_9fa48("233") ? {} : (stryCov_9fa48("233"), {
      name: option
    })));
    await waitFor(stryMutAct_9fa48("234") ? () => undefined : (stryCov_9fa48("234"), () => expect(container.querySelector(stryMutAct_9fa48("235") ? "" : (stryCov_9fa48("235"), ".select-list"))).not.toBeInTheDocument()));
    return dropdown;
  }
}
export async function selectMultipleOptions(options: string[]): Promise<HTMLElement> {
  if (stryMutAct_9fa48("236")) {
    {}
  } else {
    stryCov_9fa48("236");
    const dropdown = screen.getByTestId(stryMutAct_9fa48("237") ? "" : (stryCov_9fa48("237"), "multi-select-id")); // can cause issues

    for (let i = 0; stryMutAct_9fa48("240") ? i >= options.length : stryMutAct_9fa48("239") ? i <= options.length : stryMutAct_9fa48("238") ? false : (stryCov_9fa48("238", "239", "240"), i < options.length); stryMutAct_9fa48("241") ? i-- : (stryCov_9fa48("241"), i++)) {
      if (stryMutAct_9fa48("242")) {
        {}
      } else {
        stryCov_9fa48("242");
        // Expand options
        await userEvent.click(dropdown);
        await waitFor(stryMutAct_9fa48("243") ? () => undefined : (stryCov_9fa48("243"), () => expect(screen.queryByRole(stryMutAct_9fa48("244") ? "" : (stryCov_9fa48("244"), "menu"))).toBeInTheDocument()));
        // Select option
        await userEvent.click(await screen.findByRole(stryMutAct_9fa48("245") ? "" : (stryCov_9fa48("245"), "checkbox"), stryMutAct_9fa48("246") ? {} : (stryCov_9fa48("246"), {
          name: options[i]
        })));
        await waitFor(stryMutAct_9fa48("247") ? () => undefined : (stryCov_9fa48("247"), () => expect(screen.queryByRole(stryMutAct_9fa48("248") ? "" : (stryCov_9fa48("248"), "menu"))).not.toBeInTheDocument()));
      }
    }
    return dropdown;
  }
}
export async function enterValueFor(label: RegExp | string, data: string): Promise<void> {
  if (stryMutAct_9fa48("249")) {
    {}
  } else {
    stryCov_9fa48("249");
    const inputField = await screen.findByLabelText(label);
    await userEvent.type(inputField, data);
    await waitFor(stryMutAct_9fa48("250") ? () => undefined : (stryCov_9fa48("250"), () => expect(inputField).toHaveValue(data)));
  }
}
export async function clearValueFor(label: RegExp | string): Promise<void> {
  if (stryMutAct_9fa48("251")) {
    {}
  } else {
    stryCov_9fa48("251");
    const inputField = screen.getByLabelText(label);
    await userEvent.click(inputField); // we need to open the calendar when the input has no value
    await userEvent.clear(inputField);
    await waitFor(stryMutAct_9fa48("252") ? () => undefined : (stryCov_9fa48("252"), () => expect(inputField).toHaveValue(stryMutAct_9fa48("253") ? "Stryker was here!" : (stryCov_9fa48("253"), ""))));
  }
}
export async function clearDateFor(label: RegExp | string): Promise<void> {
  if (stryMutAct_9fa48("254")) {
    {}
  } else {
    stryCov_9fa48("254");
    await clearValueFor(label);
    await userEvent.click(screen.getByRole(stryMutAct_9fa48("255") ? "" : (stryCov_9fa48("255"), "button"), stryMutAct_9fa48("256") ? {} : (stryCov_9fa48("256"), {
      name: /Ok/i
    })));
  }
}
export async function enterDateFor(label: RegExp | string, data: string): Promise<void> {
  if (stryMutAct_9fa48("257")) {
    {}
  } else {
    stryCov_9fa48("257");
    await enterValueFor(label, data);
    await userEvent.click(screen.getByRole(stryMutAct_9fa48("258") ? "" : (stryCov_9fa48("258"), "button"), stryMutAct_9fa48("259") ? {} : (stryCov_9fa48("259"), {
      name: /Ok/i
    })));
  }
}
export async function clearValueForUrl(label: RegExp | string): Promise<void> {
  if (stryMutAct_9fa48("260")) {
    {}
  } else {
    stryCov_9fa48("260");
    const inputField = screen.getByLabelText(label);
    await userEvent.clear(inputField);
    await waitFor(stryMutAct_9fa48("261") ? () => undefined : (stryCov_9fa48("261"), () => expect(inputField).toHaveValue(stryMutAct_9fa48("262") ? "" : (stryCov_9fa48("262"), "https://"))));
  }
}