import React, { useCallback, useEffect, useMemo, useState } from "react";
import FranchiseYouPlayForm from "./forms/FranchiseYouPlayForm";
import PlatformPreferencesForm from "./forms/PlatformPreferencesForm";
import Form from "../Form";
import { CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "../../context";
import { ERROR, onToastClose, toastContent, useAsync, VALIDATION_ERROR } from "../../utils";
import { useRouter } from "next/router";
import { useDependency } from "../../context/DependencyContext";

const GamePreferences = ({
  franchisesYouPlayLabels,
  infoLabels,
  buttons,
  creator,
  updateCreator,
  franchises,
  platforms,
  layout,
  analytics
}) => {
  const { errorHandler, creatorsClient, configuration: config, client } = useDependency();
  const {
    main: { unhandledError }
  } = layout;
  const { dispatch, state: { isValidationError, validationErrors, isError } = {} } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { error: errorToast } = useToast();
  const [isFranchiseSaved, setIsFranchiseSaved] = useState(false);
  const [isPlatformSaved, setIsPlatformSaved] = useState(false);
  const [preferredPrimaryFranchises, setPreferredPrimaryFranchises] = useState(null);
  const [preferredPrimaryPlatforms, setPreferredPrimaryPlatforms] = useState(null);
  const [preferredSecondaryFranchises, setPreferredSecondaryFranchises] = useState(null);
  const [preferredSecondaryPlatforms, setPreferredSecondaryPlatforms] = useState(null);
  const router = useRouter();
  const creatorService = useMemo(() => new CreatorsService(creatorsClient, config.DEFAULT_AVATAR_IMAGE), [
    creatorsClient
  ]);

  useEffect(() => {
    if (creator && franchises) {
      let preferredPrimaryFranchises = creator.preferredPrimaryFranchise;
      franchises.forEach((item) => {
        if (preferredPrimaryFranchises && item.value === preferredPrimaryFranchises.value)
          preferredPrimaryFranchises.image = item.image;
      });
      setPreferredPrimaryFranchises(preferredPrimaryFranchises);
      let preferredSecondaryFranchises = creator.preferredSecondaryFranchises;
      preferredSecondaryFranchises.forEach((item) => {
        franchises.forEach((obj) => {
          if (item.value === obj.value) item.image = obj.image;
        });
      });
      setPreferredSecondaryFranchises(preferredSecondaryFranchises);
    }
    if (creator && platforms) {
      let preferredPrimaryPlatforms = creator.preferredPrimaryPlatform;
      platforms.forEach((item) => {
        if (preferredPrimaryPlatforms && item.value === preferredPrimaryPlatforms.value)
          preferredPrimaryPlatforms.imageAsIcon = item.imageAsIcon;
      });
      setPreferredPrimaryPlatforms(preferredPrimaryPlatforms);
      setPreferredSecondaryPlatforms(creator.preferredSecondaryPlatforms);
    }
  }, [creator, franchises, platforms]);

  const submitFranchise = useCallback(
    async (data) => {
      try {
        const primaryFranchiseValue = { id: data.primaryFranchise.value, type: "PRIMARY" };
        const values = data.secondaryFranchise.map((secondaryFranchise) => ({
          id: secondaryFranchise.value,
          type: "SECONDARY"
        }));
        values.push(primaryFranchiseValue);
        await creatorService.updateCreator({
          preferredFranchises: values,
          program: { code: config.PROGRAM_CODE }
        });

        let preferredPrimaryFranchise = data.primaryFranchise;
        // TODO: this is sometimes null, while on live reload
        franchises?.forEach((franchise) => {
          if (franchise.value === preferredPrimaryFranchise.value) {
            preferredPrimaryFranchise.value = franchise.value;
            preferredPrimaryFranchise.image = franchise.image;
            preferredPrimaryFranchise.label = franchise.label;
            preferredPrimaryFranchise.value = franchise.value;
          }
        });
        setPreferredPrimaryFranchises(preferredPrimaryFranchise);
        let preferredSecondaryFranchises = data.secondaryFranchise;
        preferredSecondaryFranchises.forEach((item) => {
          franchises?.forEach((franchise) => {
            if (item.id === franchise.value) {
              item.value = franchise.value;
              item.image = franchise.image;
              item.label = franchise.label;
            }
          });
        });
        setPreferredSecondaryFranchises(preferredSecondaryFranchises);
        if (creator.updatedPrimaryFranchise(preferredPrimaryFranchise.label)) {
          creator.preferredPrimaryFranchise = preferredPrimaryFranchise;
          analytics.updatedPrimaryFranchise({ locale: router.locale, creator });
        }
        if (creator.updatedSecondaryFranchises(preferredSecondaryFranchises.map((franchise) => franchise.label))) {
          creator.preferredSecondaryFranchises = preferredSecondaryFranchises;
          analytics.updatedSecondaryFranchises({ locale: router.locale, creator });
        }
        updateCreator(creator);
        setIsFranchiseSaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [stableDispatch, franchises]
  );

  const submitPlatform = useCallback(
    async (data) => {
      try {
        const primaryPlatformValue = { id: data.primaryPlatform.value, type: "PRIMARY" };
        const values = data.secondaryPlatforms.map((secondaryPlatform) => {
          return { id: secondaryPlatform.value, type: "SECONDARY" };
        });
        values.push(primaryPlatformValue);

        await creatorService.updateCreator({
          preferredPlatforms: values,
          program: { code: config.PROGRAM_CODE }
        });

        let preferredPrimaryPlatforms = data.primaryPlatform;
        platforms.forEach((item) => {
          if (item.value === preferredPrimaryPlatforms.value) {
            preferredPrimaryPlatforms.label = item.label;
            preferredPrimaryPlatforms.value = item.value;
            preferredPrimaryPlatforms.imageAsIcon = item.imageAsIcon;
          }
        });
        setPreferredPrimaryPlatforms(preferredPrimaryPlatforms);
        let preferredSecondaryPlatformsResponse = data.secondaryPlatforms;
        preferredSecondaryPlatformsResponse.forEach((item) => {
          platforms.forEach((obj) => {
            if (item.id === obj.value) {
              item.imageAsIcon = obj.imageAsIcon;
              item.label = obj.label;
              item.value = obj.value;
            }
          });
        });
        setPreferredSecondaryPlatforms(preferredSecondaryPlatformsResponse);
        if (creator.updatedPrimaryPlatform(preferredPrimaryPlatforms.label)) {
          creator.preferredPrimaryPlatform = preferredPrimaryPlatforms;
          analytics.updatedPrimaryPlatformInProfile({ locale: router.locale, creator });
        }
        if (creator.updatedSecondaryPlatforms(preferredSecondaryPlatformsResponse.map((platform) => platform.label))) {
          analytics.updatedSecondaryPlatformsInProfile({
            locale: router.locale,
            creator,
            selectedPlatforms: preferredSecondaryPlatformsResponse
          });
          creator.preferredSecondaryPlatforms = preferredSecondaryPlatformsResponse;
        }
        updateCreator(creator);
        setIsPlatformSaved(!isPlatformSaved);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [stableDispatch, platforms]
  );

  const franchiseOnChange = useCallback(() => setIsFranchiseSaved(false), []);
  const platformOnChange = useCallback(() => setIsPlatformSaved(false), []);
  const { pending: pendingFranchiseUpd, execute: onSubmitFranchise } = useAsync(submitFranchise, false);
  const { pending: pendingPlatformUpd, execute: onSubmitPlatform } = useAsync(submitPlatform, false);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(validationErrors)}
          closeButtonAriaLabel={buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  return (
    <div className="profile-game-preferences">
      {preferredSecondaryFranchises && (
        <Form key="franchise" mode="onChange" onSubmit={onSubmitFranchise}>
          <FranchiseYouPlayForm
            {...{
              franchisesYouPlayLabels,
              franchises,
              preferredPrimaryFranchises,
              preferredSecondaryFranchises,
              buttons,
              onChange: franchiseOnChange,
              isSaved: isFranchiseSaved,
              isLoader: pendingFranchiseUpd
            }}
          />
        </Form>
      )}
      {preferredSecondaryPlatforms && (
        <Form key="platform" mode="onChange" onSubmit={onSubmitPlatform}>
          <PlatformPreferencesForm
            {...{
              infoLabels,
              platforms,
              preferredPrimaryPlatforms,
              preferredSecondaryPlatforms,
              buttons,
              onChange: platformOnChange,
              isSaved: isPlatformSaved,
              isLoader: pendingPlatformUpd
            }}
          />
        </Form>
      )}
    </div>
  );
};
export default GamePreferences;
