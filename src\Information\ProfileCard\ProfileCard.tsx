import React, { memo } from "react";
import CreatorDisplayName from "./CreatorDisplayName/CreatorDisplayName";
import UpdateProfilePicture from "./UploadProfilePicture/UpdateProfilePicture";
import { NextRouter } from "next/router";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { AuthenticatedUser, Dispatch } from "../../utils/types";
import { ButtonLabels, ProfileLabels, ProfilePictureLabels } from "../types";
import { AccountInformationWithLocalizedDate } from "../InformationPage";

interface ProfileCardProps {
  labels: {
    creatorSince: string;
    buttons: ButtonLabels;
    profilePictureLabels: ProfilePictureLabels;
    profileLabels: ProfileLabels;
  };
  user: AuthenticatedUser;
  registrationDate: string;
  accountInformation: AccountInformationWithLocalizedDate;
  data: string;
  stableDispatch: Dispatch;
  DEFAULT_AVATAR_IMAGE: string;
  creatorsClient: TraceableHttpClient;
  router: NextRouter;
}

const ProfileCard = ({
  labels,
  user,
  accountInformation,
  registrationDate,
  data,
  stableDispatch,
  creatorsClient,
  DEFAULT_AVATAR_IMAGE,
  router
}: ProfileCardProps) => {
  return (
    <div className="profile-card">
      <UpdateProfilePicture
        isPlaceholderDefault={false}
        src={user.avatar}
        user={user}
        labels={labels}
        stableDispatch={stableDispatch}
        creatorsClient={creatorsClient}
        DEFAULT_AVATAR_IMAGE={DEFAULT_AVATAR_IMAGE}
        router={router}
      />

      <div className="profile-card-info">
        <CreatorDisplayName user={user} tooltip={data} />
        {accountInformation && (
          <div className="profile-card-sub-title">
            <span className="profile-card-creator-since">{labels.creatorSince} </span>
            {registrationDate}
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(ProfileCard);
