import React, { memo } from "react";
import CreatorDisplayName from "./CreatorDisplayName/CreatorDisplayName";
import UpdateProfilePicture from "./UploadProfilePicture/UpdateProfilePicture";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { AuthenticatedUser, Dispatch } from "../../utils/types";
import { ButtonLabels, ProfileLabels, ProfilePictureLabels } from "../types";
import { AccountInformationWithLocalizedDate } from "../InformationPage";

interface ProfileCardProps {
  labels: {
    creatorSince: string;
    buttons: ButtonLabels;
    profilePictureLabels: ProfilePictureLabels;
    profileLabels: ProfileLabels;
  };
  user: AuthenticatedUser;
  registrationDate: string;
  accountInformation: AccountInformationWithLocalizedDate;
  data: string;
  stableDispatch: Dispatch;
  defaultAvatarImage: string;
  creatorsClient: TraceableHttpClient;
  locale: string;
}

const ProfileCard = ({
  labels,
  user,
  accountInformation,
  registrationDate,
  data,
  stableDispatch,
  creatorsClient,
  defaultAvatarImage,
  locale
}: ProfileCardProps) => {
  return (
    <div className="profile-card">
      <UpdateProfilePicture
        isPlaceholderDefault={false}
        src={user?.avatar}
        user={user}
        labels={labels}
        stableDispatch={stableDispatch}
        creatorsClient={creatorsClient}
        defaultAvatarImage={defaultAvatarImage}
        locale={locale}
      />

      <div className="profile-card-info">
        <CreatorDisplayName user={user} labels={labels} tooltip={data} />
        {accountInformation && (
          <div className="profile-card-sub-title">
            <span className="profile-card-creator-since">{labels.creatorSince} </span>
            {registrationDate}
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(ProfileCard);
