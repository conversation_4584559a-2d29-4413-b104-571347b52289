.profile-game-preferences {
  @apply font-text-regular text-black;
}
.profile-game-preferences-title {
  @apply mb-meas6 self-center font-display-bold xs:text-mobile-h4 md:mb-meas0 md:text-tablet-h4 lg:text-desktop-h4;
}
.profile-game-preferences-title-actions {
  @apply mb-meas6 block justify-between md:mb-meas12 md:flex xl:mb-meas10;
}
.profile-game-preferences-buttons {
  @apply mb-meas6 md:mb-meas0;
}
.profile-game-preferences-description {
  @apply max-w-[590px]  xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.profile-game-preferences-primary-container {
  @apply mt-meas16 border-b border-gray-40 border-opacity-[0.33] pb-meas18 md:mt-meas20 md:pb-meas26 xl:mt-meas16 xl:pb-meas30;
}
.profile-primary-platform-container {
  @apply mt-meas16 pb-meas18 md:mt-meas20 md:pb-meas26;
}
.profile-game-preferences-primary-title {
  @apply mb-meas6 font-text-bold xs:text-mobile-h4 md:mb-meas2 md:text-tablet-h4 lg:text-desktop-h4 xl:mb-meas4;
}
.profile-game-preferences-primary-subtitle {
  @apply xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.profile-game-preferences-primary-show {
  @apply mt-meas16 md:mt-meas26 xl:mt-meas30;
}
.profile-game-preferences-tag {
  @apply mr-[6px] mt-meas2 inline rounded-[100px] bg-gray-20 pl-meas4 pr-meas4 text-gray-90 xs:text-mobile-body-small md:text-tablet-body-small lg:text-desktop-body-small;
}
.profile-game-preferences-option {
  @apply mt-meas18 md:mt-meas13;
}
.profile-game-preferences-option-image {
  @apply h-[217px] w-[221px] border-2  border-solid;
}
.profile-game-preferences-secondary-container {
  @apply mt-meas16 border-b border-gray-40 border-opacity-[0.33] pb-meas18 md:pb-meas26 xl:mt-meas20;
}
.profile-platform-secondary-container {
  @apply mt-meas16 pb-meas18 md:pb-meas35 xl:pb-[319px];
}
.profile-game-preferences-secondary-title {
  @apply mb-meas6 font-text-bold xs:text-mobile-h4 md:mb-meas2 md:text-tablet-h4 lg:text-desktop-h4 xl:mb-meas4;
}
.profile-game-preferences-secondary-subtitle {
  @apply xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.profile-secondary-game-preferences-show {
  @apply mt-meas13 md:mt-meas26 xl:mt-meas20;
}
.profile-secondary-platform-show {
  @apply mt-meas13 md:mt-meas18 xl:mt-meas24;
}
.profile-secondary-game-preferences-options {
  @apply mt-meas13 flex flex-wrap gap-meas10;
}
.profile-secondary-franchise-option-image {
  @apply h-[130px] rounded;
  border: 4px solid rgb(115, 204, 117);
}
.profile-secondary-franchise-option-text {
  @apply mt-meas4 w-[130px] overflow-hidden text-ellipsis whitespace-nowrap text-center xs:text-mobile-body-small md:text-tablet-body-small lg:text-desktop-body-small;
}
.profile-platform-preferences {
  @apply mt-meas16 md:mt-meas26 xl:mt-meas20;
}
.profile-platform-icon {
  @apply h-[100px];
}
.profile-platform-preferences-option > .empty-card {
  @apply m-meas0;
}
.profile-platform-preferences-option {
  @apply mt-meas10 md:mt-meas20;
}
.profile-secondary-platform-icon {
  @apply m-auto mb-meas10 mt-meas10 h-[90px] text-custom-2;
}
.profile-secondary-platform-icon-default {
  @apply m-auto mb-meas10 mt-meas10 h-[90px] w-[90px] text-custom-2;
}
.profile-secondary-platform-option {
  @apply w-[130px] rounded-[3px] border border-navy-60 bg-navy-80;
}
.profile-secondary-platform-option-text {
  @apply mt-[6px] text-center xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.profile-secondary-platform-option-container {
  @apply justify-self-center opacity-30;
}
.profile-secondary-platform-option-container-selected {
  @apply opacity-100;
}
.profile-secondary-platform-option-selected {
  border: 4px solid rgb(115, 204, 117);
  border-radius: 4px;
}
.profile-secondary-game-preferences-tags {
  @apply flex flex-wrap;
}
.profile-game-preferences-primary-show .search-box {
  @apply inline;
}
.profile-platform-secondary-container .card-col {
  @apply h-auto w-[130px];
}
.profile-platform-secondary-container .checkmark {
  @apply h-[130px] w-[130px];
}
.profile-platform-secondary-container .card-container {
  @apply mt-meas13 flex flex-wrap justify-start gap-x-meas10 gap-y-meas0 md:mt-meas18 xl:mt-meas24;
}
.profile-game-preferences .select-box {
  @apply max-w-[320px];
}
.profile-game-preferences .select-header-title,
.select-header-label {
  @apply w-full;
}
.profile-game-preferences .select-list,
.select-scroll-list {
  @apply w-[99.7%];
}
.profile-game-preferences .multi-select-menu {
  @apply max-w-[544px];
}
