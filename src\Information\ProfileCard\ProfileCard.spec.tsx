import { render, screen } from "@testing-library/react";
import ProfileCard from "./ProfileCard";
import React from "react";
import { axe } from "jest-axe";
import { AuthenticatedUser } from "@src/utils/types";
import { ButtonLabels, ProfileLabels, ProfilePictureLabels } from "../types";
import { AccountInformationWithLocalizedDate } from "../InformationPage";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";

Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn()
  }))
});

describe("ProfileCard", () => {
  const user: AuthenticatedUser = {
    analyticsId: "xNIEuLMZCvlw+7q2oZaBuG0cTdqHTa2JP3l2eCZ2Sjg=",
    avatar: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png",
    creatorCode: "TestAFFILIATE20250",
    isFlagged: false,
    isPayable: true,
    needsMigration: false,
    programs: ["creator_network", "affiliate", "sims_creator_program"],
    status: "ACTIVE",
    tier: "CREATORNETWORK",
    type: "CREATOR",
    username: "245902"
  };

  const accountInformation: AccountInformationWithLocalizedDate = {
    defaultGamerTag: "245902",
    nucleusId: *************,
    firstName: "John",
    lastName: "Doe",
    originEmail: "<EMAIL>",
    dateOfBirth: {
      millisecondsEpoch: ************,
      format: () => "July 30, 1995",
      toDate: () => new Date(************)
    } as unknown as LocalizedDate,
    needsMigration: false,
    payable: true,
    flagged: false,
    disabled: false,
    preferredName: "John",
    preferredPronouns: null
  };

  const labels = {
    creatorSince: "Creator since",
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close",
      browse: "Browse"
    } as ButtonLabels,
    profilePictureLabels: {
      title: "Change My Avatar",
      message: "Select an image from your computer (JPEG, PNG or GIF). Image should be square and less than 1MB.",
      termsAndConditionsFirst:
        "Respect the rights of others. Only upload images that you own or that you have written permission to freely distribute.",
      termsAndConditionsMiddle: "User Agreement",
      termsAndConditionsLast: "for more information.",
      avatarRequired: "Please select an image",
      avatarInvalid: "Please select valid image",
      avatarMoreThanLimit: "Image size should be less than 1MB"
    } as ProfilePictureLabels,
    profileLabels: {
      updateAvatar: "Update Avatar"
    } as ProfileLabels
  };

  const props = {
    labels,
    user,
    registrationDate: "July 15, 2020",
    accountInformation,
    data: "GCN Badge tooltip text",
    stableDispatch: jest.fn(),
    defaultAvatarImage: "https://example.com/default-avatar.png",
    creatorsClient: jest.fn() as unknown as TraceableHttpClient,
    locale: "en-us"
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show profile card correctly", () => {
    render(<ProfileCard {...props} />);

    expect(screen.getByText("245902")).toBeInTheDocument();
    expect(screen.getByText("Creator since")).toBeInTheDocument();
    expect(screen.getByText("July 15, 2020")).toBeInTheDocument();
    expect(screen.getByText(user.username)).toBeInTheDocument();
    const avatarImage = screen.getByRole("img", { name: "profile-card-avatar" });
    expect(avatarImage).toBeInTheDocument();
    expect(avatarImage).toHaveAttribute("src", user.avatar);
    expect(avatarImage).toHaveClass("profile-card-avatar");
    expect(screen.getByRole("button", { name: "Update Avatar" })).toBeInTheDocument();
  });

  it("should not show registration date when account information is not present", () => {
    const propsWithoutAccountInfo = {
      ...props,
      accountInformation: null
    };

    render(<ProfileCard {...propsWithoutAccountInfo} />);

    expect(screen.queryByText("Creator since")).not.toBeInTheDocument();
    expect(screen.queryByText("July 15, 2020")).not.toBeInTheDocument();
  });

  it("should show GCN badge when user needs migration", () => {
    const userWithMigration = {
      ...user,
      needsMigration: true
    };
    const propsWithMigration = {
      ...props,
      user: userWithMigration
    };

    render(<ProfileCard {...propsWithMigration} />);

    const badge = screen.getByAltText("GCN Badge");
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveAttribute("src", "/img/icons/badge.svg");
  });

  it("should not show GCN badge when user does not need migration", () => {
    render(<ProfileCard {...props} />);

    expect(screen.queryByAltText("GCN Badge")).not.toBeInTheDocument();
  });

  it("shows CreatorDisplayName component with correct props", () => {
    render(<ProfileCard {...props} />);

    expect(screen.getByText(user.username)).toBeInTheDocument();
  });

  it("should handle different user types correctly", () => {
    const interestedCreatorUser = {
      ...user,
      type: "INTERESTED_CREATOR" as const
    };
    const propsWithInterestedCreator = {
      ...props,
      user: interestedCreatorUser
    };

    render(<ProfileCard {...propsWithInterestedCreator} />);

    expect(screen.getByText(interestedCreatorUser.username)).toBeInTheDocument();
  });

  it("should be accessible", async () => {
    const { container } = render(<ProfileCard {...props} />);

    expect(await axe(container)).toHaveNoViolations();
  });

  it("should handle missing props gracefully", () => {
    const minimalProps = {
      ...props,
      data: ""
    };

    expect(() => {
      render(<ProfileCard {...minimalProps} />);
    }).not.toThrow();
  });
});
