import { act, render, screen } from "@testing-library/react";
import ProfileCard from "./ProfileCard";
import React from "react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import { AuthenticatedUser } from "@src/utils/types";
import { ButtonLabels, ProfileLabels, ProfilePictureLabels } from "../types";
import { AccountInformationWithLocalizedDate } from "../InformationPage";

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  reload: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  prefetch: jest.fn(),
  beforePopState: jest.fn(),
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn()
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  defaultLocale: "en",
  domainLocales: [],
  isPreview: false,
  route: "/",
  pathname: "/",
  query: {},
  asPath: "/",
  basePath: "",
  locale: "en",
  locales: ["en"]
} as any;

const mockCreatorsClient = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  patch: jest.fn(),
  client: {} as any,
  provider: {} as any,
  tracer: {} as any,
  upload: jest.fn()
} as any;

const mockDispatch = jest.fn();

describe("ProfileCard - Tests for profile card functionality including rendering, user information display, avatar handling, creator display name, registration date, and accessibility", () => {
  const user: AuthenticatedUser = {
    analyticsId: "xNIEuLMZCvlw+7q2oZaBuG0cTdqHTa2JP3l2eCZ2Sjg=",
    avatar: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png",
    creatorCode: "TestAFFILIATE20250",
    isFlagged: false,
    isPayable: true,
    needsMigration: false,
    programs: ["creator_network", "affiliate", "sims_creator_program"],
    status: "ACTIVE",
    tier: "CREATORNETWORK",
    type: "CREATOR",
    username: "245902"
  };

  const accountInformation: AccountInformationWithLocalizedDate = {
    defaultGamerTag: "245902",
    nucleusId: *************,
    firstName: "John",
    lastName: "Doe",
    originEmail: "<EMAIL>",
    dateOfBirth: {
      millisecondsEpoch: ************,
      format: () => "July 30, 1995",
      toDate: () => new Date(************)
    } as any,
    needsMigration: false,
    payable: true,
    flagged: false,
    disabled: false,
    preferredName: "John",
    preferredPronouns: null
  };

  const labels = {
    creatorSince: "Creator since",
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close",
      browse: "Browse"
    } as ButtonLabels,
    profilePictureLabels: {
      title: "Change My Avatar",
      message: "Select an image from your computer (JPEG, PNG or GIF). Image should be square and less than 1MB.",
      termsAndConditionsFirst: "Respect the rights of others. Only upload images that you own or that you have written permission to freely distribute.",
      termsAndConditionsMiddle: "User Agreement",
      termsAndConditionsLast: "for more information.",
      avatarRequired: "Please select an image",
      avatarInvalid: "Please select valid image",
      avatarMoreThanLimit: "Image size should be less than 1MB"
    } as ProfilePictureLabels,
    profileLabels: {
      updateAvatar: "Update Avatar"
    } as ProfileLabels
  };

  const props = {
    labels,
    user,
    registrationDate: "July 15, 2020",
    accountInformation,
    data: "GCN Badge tooltip text",
    stableDispatch: mockDispatch,
    DEFAULT_AVATAR_IMAGE: "https://example.com/default-avatar.png",
    creatorsClient: mockCreatorsClient,
    router: mockRouter
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show profile card correctly", () => {
    render(<ProfileCard {...props} />);

    expect(screen.getByText("245902")).toBeInTheDocument();
    expect(screen.getByText("Creator since")).toBeInTheDocument();
    expect(screen.getByText("July 15, 2020")).toBeInTheDocument();
  });

  it("should display user avatar correctly", () => {
    render(<ProfileCard {...props} />);

    const avatarImage = screen.getByAltText("");
    expect(avatarImage).toBeInTheDocument();
    expect(avatarImage).toHaveAttribute("src", user.avatar);
    expect(avatarImage).toHaveClass("profile-card-avatar");
  });

  it("should display username correctly", () => {
    render(<ProfileCard {...props} />);

    expect(screen.getByText(user.username)).toBeInTheDocument();
  });

  it("should show registration date when account information is present", () => {
    render(<ProfileCard {...props} />);

    expect(screen.getByText("Creator since")).toBeInTheDocument();
    expect(screen.getByText("July 15, 2020")).toBeInTheDocument();
  });

  it("should not show registration date when account information is not present", () => {
    const propsWithoutAccountInfo = {
      ...props,
      accountInformation: null
    };

    render(<ProfileCard {...propsWithoutAccountInfo} />);

    expect(screen.queryByText("Creator since")).not.toBeInTheDocument();
    expect(screen.queryByText("July 15, 2020")).not.toBeInTheDocument();
  });

  it("should show GCN badge when user needs migration", () => {
    const userWithMigration = {
      ...user,
      needsMigration: true
    };

    const propsWithMigration = {
      ...props,
      user: userWithMigration
    };

    render(<ProfileCard {...propsWithMigration} />);

    const badge = screen.getByAltText("GCN Badge");
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveAttribute("src", "/img/icons/badge.svg");
  });

  it("should not show GCN badge when user does not need migration", () => {
    render(<ProfileCard {...props} />);

    expect(screen.queryByAltText("GCN Badge")).not.toBeInTheDocument();
  });

  it("should render UpdateProfilePicture component with correct props", () => {
    render(<ProfileCard {...props} />);

    const avatarImage = screen.getByAltText("");
    expect(avatarImage).toBeInTheDocument();
    expect(avatarImage).toHaveClass("profile-card-avatar");
    expect(screen.getByRole("button", { name: "Update Avatar" })).toBeInTheDocument();
  });

  it("should render CreatorDisplayName component with correct props", () => {
    render(<ProfileCard {...props} />);

    expect(screen.getByText(user.username)).toBeInTheDocument();
  });

  it("should handle different user types correctly", () => {
    const interestedCreatorUser = {
      ...user,
      type: "INTERESTED_CREATOR" as const
    };

    const propsWithInterestedCreator = {
      ...props,
      user: interestedCreatorUser
    };

    render(<ProfileCard {...propsWithInterestedCreator} />);

    expect(screen.getByText(interestedCreatorUser.username)).toBeInTheDocument();
  });

  it("should handle missing avatar gracefully", () => {
    const userWithoutAvatar = {
      ...user,
      avatar: undefined
    };

    const propsWithoutAvatar = {
      ...props,
      user: userWithoutAvatar
    };

    render(<ProfileCard {...propsWithoutAvatar} />);

    expect(screen.getByText(user.username)).toBeInTheDocument();
  });

  it("should handle different registration dates correctly", () => {
    const propsWithDifferentDate = {
      ...props,
      registrationDate: "January 1, 2021"
    };

    render(<ProfileCard {...propsWithDifferentDate} />);

    expect(screen.getByText("Creator since")).toBeInTheDocument();
    expect(screen.getByText("January 1, 2021")).toBeInTheDocument();
  });

  it("should be accessible", async () => {
    let results: any;
    const { container } = render(<ProfileCard {...props} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  it("should handle missing props gracefully", () => {
    const minimalProps = {
      ...props,
      data: ""
    };

    expect(() => {
      render(<ProfileCard {...minimalProps} />);
    }).not.toThrow();
  });

  it("should be memoized and not re-render unnecessarily", () => {
    const { rerender } = render(<ProfileCard {...props} />);

    rerender(<ProfileCard {...props} />);

    expect(screen.getByText(user.username)).toBeInTheDocument();
    expect(screen.getByText("Creator since")).toBeInTheDocument();
    expect(screen.getByText("July 15, 2020")).toBeInTheDocument();
  });
});