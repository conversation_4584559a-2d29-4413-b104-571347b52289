import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import GamePreferencesPage from "./GamePreferencesPage";

// Mock data for the story
const mockCreator = {
  preferredPrimaryFranchise: {
    value: "fifa",
    label: "FIFA",
    image: "/images/fifa-logo.png"
  },
  preferredSecondaryFranchises: [
    {
      value: "madden",
      label: "Madden NFL",
      image: "/images/madden-logo.png"
    }
  ],
  preferredPrimaryPlatform: {
    value: "pc",
    label: "PC"
  },
  preferredSecondaryPlatforms: [
    {
      value: "xbox",
      label: "Xbox"
    }
  ]
};

const mockFranchises = [
  {
    value: "fifa",
    label: "FIFA",
    image: "/images/fifa-logo.png"
  },
  {
    value: "madden",
    label: "Madden NFL",
    image: "/images/madden-logo.png"
  },
  {
    value: "apex",
    label: "Apex Legends",
    image: "/images/apex-logo.png"
  }
];

const mockPlatforms = [
  {
    value: "pc",
    label: "PC"
  },
  {
    value: "xbox",
    label: "Xbox"
  },
  {
    value: "playstation",
    label: "PlayStation"
  }
];

const mockLabels = {
  franchisesYouPlayLabels: {
    title: "Franchises You Play",
    description: "Select the game franchises you play",
    primaryFranchise: "Primary Franchise",
    secondaryFranchises: "Secondary Franchises"
  },
  infoLabels: {
    gamePreferences: "Game Preferences",
    platformPreferences: "Platform Preferences"
  },
  buttons: {
    edit: "Edit",
    cancel: "Cancel",
    save: "Save",
    close: "Close"
  }
};

const mockLayout = {
  main: {
    unhandledError: "An unexpected error occurred"
  }
};

const mockAnalytics = {
  track: () => {}
};

const meta: Meta<typeof GamePreferencesPage> = {
  title: "Game Preferences/GamePreferencesPage",
  component: GamePreferencesPage,
  parameters: {
    layout: "fullscreen"
  },
  decorators: [
    (Story) => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ],
  args: {
    franchisesYouPlayLabels: mockLabels.franchisesYouPlayLabels,
    infoLabels: mockLabels.infoLabels,
    buttons: mockLabels.buttons,
    creator: mockCreator,
    updateCreator: () => {},
    franchises: mockFranchises,
    platforms: mockPlatforms,
    layout: mockLayout,
    analytics: mockAnalytics
  }
};

export default meta;

type Story = StoryObj<typeof GamePreferencesPage>;

export const Default: Story = {
  args: {}
};

export const WithoutPreferences: Story = {
  args: {
    creator: {
      preferredPrimaryFranchise: null,
      preferredSecondaryFranchises: [],
      preferredPrimaryPlatform: null,
      preferredSecondaryPlatforms: []
    }
  }
};

export const WithMultiplePreferences: Story = {
  args: {
    creator: {
      preferredPrimaryFranchise: {
        value: "fifa",
        label: "FIFA",
        image: "/images/fifa-logo.png"
      },
      preferredSecondaryFranchises: [
        {
          value: "madden",
          label: "Madden NFL",
          image: "/images/madden-logo.png"
        },
        {
          value: "apex",
          label: "Apex Legends",
          image: "/images/apex-logo.png"
        }
      ],
      preferredPrimaryPlatform: {
        value: "pc",
        label: "PC"
      },
      preferredSecondaryPlatforms: [
        {
          value: "xbox",
          label: "Xbox"
        },
        {
          value: "playstation",
          label: "PlayStation"
        }
      ]
    }
  }
};