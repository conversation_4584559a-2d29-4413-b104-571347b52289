import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import GamePreferencesPage from "./GamePreferencesPage";
import FranchisesYouPlayLabels from "@src/Translations/FranchisesYouPlayLabels";
import { InformationPageLabels } from "@src/Translations/InformationPageLabels";
import { Franchise } from "@eait-playerexp-cn/metadata-types";
import { PreferredFranchiseResponse, PreferredPlatformRequest } from "@eait-playerexp-cn/creator-types";
import { aCommunicationPreferencesPayload, aConnectedAccountResponse, aCreatorCodeResponse, aLegalEntityInformationPayload, aMailingAddressPayload, anAccountInformationResponse, anAdditionalInformationPayload, aPreferredFranchiseResponse, aPreferredPlatformResponse, aProgramRequest } from "@eait-playerexp-cn/creator-test-fixtures";
import { aFranchise, aPlatform } from "@eait-playerexp-cn/metadata-test-fixtures";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { CreatorProfile } from "@eait-playerexp-cn/creators-http-client";

const meta: Meta<typeof GamePreferencesPage> = {
  title: "Game Preferences Page",
  component: GamePreferencesPage,
  parameters: {
    layout: "fullscreen"
  },
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof GamePreferencesPage>;

export const GamePreferencesPageStory: Story = {
  args: {
    franchisesYouPlayLabels: FranchisesYouPlayLabels,
    infoLabels: InformationPageLabels.infoLabels,
    buttons: InformationPageLabels.buttons,
    creator: {
      preferredPrimaryFranchise: aPreferredFranchiseResponse(),
      preferredSecondaryFranchises: [aPreferredFranchiseResponse()],
      preferredPrimaryPlatform: aPreferredPlatformResponse(),
      preferredSecondaryPlatforms: [aPreferredPlatformResponse()],
      
      id: "001VB00000DUe5PYAT",
      creatorTypes: ["YOUTUBER"],
      accountInformation: {
        defaultGamerTag: "245902",
        nucleusId: *************,
        firstName: "Mouli",
        lastName: "Nrusimhadri",
        originEmail: "<EMAIL>",
        dateOfBirth: {
          millisecondsEpoch: ************,
          format: () => "July 30, 1995",
          toDate: () => new Date(************)
        } as unknown as LocalizedDate,
        needsMigration: false,
        payable: true,
        flagged: false,
        disabled: false,
        preferredName: "Mouli",
        preferredPronouns: null
      },
      communicationPreferences: aCommunicationPreferencesPayload(),
      legalInformation: aLegalEntityInformationPayload(),
      mailingAddress: aMailingAddressPayload(),
      connectedAccounts: aConnectedAccountResponse(),
      additionalInformation: anAdditionalInformationPayload(),
      socialLinks: [],
      creatorCode: aCreatorCodeResponse(),
      joinedPrograms: ["creator_network", "affiliate", "sims_creator_program"],
      program: aProgramRequest(),
      formattedRegistrationDate: () => ""
    } as unknown as CreatorProfile,
    updateCreator: () => {},
    franchises: [aFranchise(),aFranchise(),aFranchise()],
    platforms: [aPlatform(),aPlatform(),aPlatform()],
    layout: {
      main: {
        unhandledError: "Oops! Something has gone wrong."
      }
    },
    analytics: {
      updatedBasicInformation: () => {},
      updatedPrimaryFranchise: () => {},
      updatedSecondaryFranchises: () => {},
      updatedPrimaryPlatformInProfile: () => {},
      updatedSecondaryPlatformsInProfile: () => {}
    }
  }
};